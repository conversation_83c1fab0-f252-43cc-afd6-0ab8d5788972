// CentOS 8 部署包创建脚本
const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

// 部署包配置
const DEPLOY_CONFIG = {
    version: 'V3.0',
    timestamp: new Date().toISOString().replace(/[:.]/g, '-'),
    outputDir: './deploy-packages',
    packageName: 'UPC-System-V3.0-CentOS8-Deploy',
    excludePatterns: [
        'node_modules',
        '.git',
        'backups',
        'deploy-packages',
        '*.log',
        'temp',
        'tmp',
        '.DS_Store',
        'Thumbs.db',
        'test-*.js',
        'create-*.js'
    ]
};

// 创建部署目录
function ensureDeployDir() {
    if (!fs.existsSync(DEPLOY_CONFIG.outputDir)) {
        fs.mkdirSync(DEPLOY_CONFIG.outputDir, { recursive: true });
        console.log(`📁 创建部署目录: ${DEPLOY_CONFIG.outputDir}`);
    }
}

// 检查文件是否应该被排除
function shouldExclude(filePath) {
    const relativePath = path.relative('.', filePath);
    return DEPLOY_CONFIG.excludePatterns.some(pattern => {
        if (pattern.includes('*')) {
            const regex = new RegExp(pattern.replace(/\*/g, '.*'));
            return regex.test(relativePath);
        }
        return relativePath.includes(pattern);
    });
}

// 递归获取所有文件
function getAllFiles(dir, fileList = []) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
        const filePath = path.join(dir, file);
        
        if (shouldExclude(filePath)) {
            console.log(`⏭️ 跳过: ${filePath}`);
            return;
        }
        
        if (fs.statSync(filePath).isDirectory()) {
            getAllFiles(filePath, fileList);
        } else {
            fileList.push(filePath);
        }
    });
    
    return fileList;
}

// 创建部署包
async function createDeployPackage() {
    console.log('🚀 开始创建 CentOS 8 部署包...');
    
    try {
        // 确保部署目录存在
        ensureDeployDir();
        
        // 获取所有文件
        console.log('📁 扫描文件...');
        const allFiles = getAllFiles('.');
        console.log(`📋 找到 ${allFiles.length} 个文件`);
        
        // 创建部署包文件名
        const packageFileName = `${DEPLOY_CONFIG.packageName}-${DEPLOY_CONFIG.timestamp}.zip`;
        const packagePath = path.join(DEPLOY_CONFIG.outputDir, packageFileName);
        
        // 创建压缩流
        const output = fs.createWriteStream(packagePath);
        const archive = archiver('zip', {
            zlib: { level: 9 } // 最高压缩级别
        });
        
        // 监听事件
        output.on('close', () => {
            const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
            console.log(`✅ CentOS 8 部署包创建完成！`);
            console.log(`📦 文件: ${packageFileName}`);
            console.log(`📏 大小: ${sizeInMB} MB`);
            console.log(`📁 位置: ${path.resolve(packagePath)}`);
            console.log(`📋 包含 ${allFiles.length + 1} 个文件（含安装脚本）`);
        });
        
        archive.on('error', (err) => {
            throw err;
        });
        
        archive.on('progress', (progress) => {
            const percent = ((progress.entries.processed / progress.entries.total) * 100).toFixed(1);
            process.stdout.write(`\r📦 压缩进度: ${percent}% (${progress.entries.processed}/${progress.entries.total})`);
        });
        
        // 连接输出流
        archive.pipe(output);
        
        // 添加所有文件
        console.log('📦 开始压缩文件...');
        allFiles.forEach(file => {
            archive.file(file, { name: file });
        });

        // 添加部署说明文件
        const deployInfo = {
            name: "UPC管理系统 V3.0 CentOS 8 部署包",
            version: "3.0.0",
            buildDate: new Date().toISOString(),
            platform: "CentOS 8",
            description: "企业级UPC码管理解决方案",
            features: [
                "一键安装脚本",
                "环境检查工具",
                "详细部署教程",
                "运维管理指南",
                "完整卸载工具"
            ],
            requirements: {
                os: "CentOS 8 (推荐)",
                cpu: "1核心以上",
                memory: "1GB以上",
                disk: "2GB以上"
            },
            installation: "请运行 ./install-centos8.sh 进行一键安装",
            documentation: "详细说明请参考 README.md 和 部署教程-小白版.md"
        };

        archive.append(JSON.stringify(deployInfo, null, 2), { name: 'deploy-info.json' });
        
        // 完成压缩
        await archive.finalize();
        
    } catch (error) {
        console.error('❌ 部署包创建失败:', error);
        process.exit(1);
    }
}

// 运行创建
if (require.main === module) {
    createDeployPackage();
}

module.exports = { createDeployPackage };
