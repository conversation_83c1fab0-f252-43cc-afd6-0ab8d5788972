// UPC管理系统 V3.0 完整备份脚本
const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

// 备份配置
const BACKUP_CONFIG = {
    version: 'V3.0',
    timestamp: new Date().toISOString().replace(/[:.]/g, '-'),
    outputDir: './backups',
    excludePatterns: [
        'node_modules',
        '.git',
        'backups',
        '*.log',
        'temp',
        'tmp',
        '.DS_Store',
        'Thumbs.db'
    ]
};

// 创建备份目录
function ensureBackupDir() {
    if (!fs.existsSync(BACKUP_CONFIG.outputDir)) {
        fs.mkdirSync(BACKUP_CONFIG.outputDir, { recursive: true });
        console.log(`📁 创建备份目录: ${BACKUP_CONFIG.outputDir}`);
    }
}

// 检查文件是否应该被排除
function shouldExclude(filePath) {
    const relativePath = path.relative('.', filePath);
    return BACKUP_CONFIG.excludePatterns.some(pattern => {
        if (pattern.includes('*')) {
            const regex = new RegExp(pattern.replace(/\*/g, '.*'));
            return regex.test(relativePath);
        }
        return relativePath.includes(pattern);
    });
}

// 获取系统信息
function getSystemInfo() {
    const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
    return {
        name: packageJson.name,
        version: packageJson.version,
        description: packageJson.description,
        backupTime: new Date().toISOString(),
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch
    };
}

// 创建备份清单
function createManifest(systemInfo, files) {
    const manifest = {
        backup: {
            version: BACKUP_CONFIG.version,
            timestamp: BACKUP_CONFIG.timestamp,
            created: new Date().toISOString()
        },
        system: systemInfo,
        files: {
            total: files.length,
            list: files.map(file => ({
                path: file,
                size: fs.existsSync(file) ? fs.statSync(file).size : 0,
                modified: fs.existsSync(file) ? fs.statSync(file).mtime.toISOString() : null
            }))
        },
        instructions: {
            deployment: "请参考 deployment-guide.md 进行部署",
            requirements: {
                node: ">=14.0.0",
                npm: ">=6.0.0",
                os: "Linux (推荐 CentOS 8)"
            },
            services: [
                "邮件服务 (nodemailer)",
                "短信服务 (腾讯云)",
                "定时备份 (node-cron)",
                "日志服务",
                "性能监控"
            ]
        }
    };
    
    return JSON.stringify(manifest, null, 2);
}

// 递归获取所有文件
function getAllFiles(dir, fileList = []) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
        const filePath = path.join(dir, file);
        
        if (shouldExclude(filePath)) {
            console.log(`⏭️ 跳过: ${filePath}`);
            return;
        }
        
        if (fs.statSync(filePath).isDirectory()) {
            getAllFiles(filePath, fileList);
        } else {
            fileList.push(filePath);
        }
    });
    
    return fileList;
}

// 创建压缩备份
async function createBackup() {
    console.log('🚀 开始创建 UPC管理系统 V3.0 完整备份...');
    
    try {
        // 确保备份目录存在
        ensureBackupDir();
        
        // 获取系统信息
        const systemInfo = getSystemInfo();
        console.log('📊 系统信息:', systemInfo);
        
        // 获取所有文件
        console.log('📁 扫描文件...');
        const allFiles = getAllFiles('.');
        console.log(`📋 找到 ${allFiles.length} 个文件`);
        
        // 创建备份清单
        const manifest = createManifest(systemInfo, allFiles);
        
        // 创建备份文件名
        const backupFileName = `UPC-System-V3.0-${BACKUP_CONFIG.timestamp}.zip`;
        const backupPath = path.join(BACKUP_CONFIG.outputDir, backupFileName);
        
        // 创建压缩流
        const output = fs.createWriteStream(backupPath);
        const archive = archiver('zip', {
            zlib: { level: 9 } // 最高压缩级别
        });
        
        // 监听事件
        output.on('close', () => {
            const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
            console.log(`✅ 备份完成！`);
            console.log(`📦 文件: ${backupFileName}`);
            console.log(`📏 大小: ${sizeInMB} MB`);
            console.log(`📁 位置: ${path.resolve(backupPath)}`);
            console.log(`📋 包含 ${allFiles.length} 个文件`);
        });
        
        archive.on('error', (err) => {
            throw err;
        });
        
        archive.on('progress', (progress) => {
            const percent = ((progress.entries.processed / progress.entries.total) * 100).toFixed(1);
            process.stdout.write(`\r📦 压缩进度: ${percent}% (${progress.entries.processed}/${progress.entries.total})`);
        });
        
        // 连接输出流
        archive.pipe(output);
        
        // 添加清单文件
        archive.append(manifest, { name: 'backup-manifest.json' });
        
        // 添加所有文件
        console.log('📦 开始压缩文件...');
        allFiles.forEach(file => {
            archive.file(file, { name: file });
        });
        
        // 完成压缩
        await archive.finalize();
        
    } catch (error) {
        console.error('❌ 备份失败:', error);
        process.exit(1);
    }
}

// 运行备份
if (require.main === module) {
    createBackup();
}

module.exports = { createBackup };
