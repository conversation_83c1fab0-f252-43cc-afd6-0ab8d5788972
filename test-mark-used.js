// 测试标记已用功能的脚本
const http = require('http');

// 测试标记已用功能
function testMarkUsed() {
    console.log('🧪 开始测试标记已用功能...');

    // 测试数据 - 使用一个已知存在的UPC码ID
    const testUPCId = 'upc_041'; // 使用修复后的UPC码ID

    // 构建请求数据 - 标记为已使用
    const postData = JSON.stringify({
        upcId: testUPCId,
        updates: {
            status: 'invalid',
            notes: '测试标记为已使用'
        }
    });

    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc/update',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData),
            'Cookie': 'session_id=test_session_123' // 模拟会话
        }
    };
    
    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            console.log(`📊 响应状态码: ${res.statusCode}`);
            console.log(`📊 响应头: ${JSON.stringify(res.headers, null, 2)}`);
            
            try {
                const response = JSON.parse(data);
                console.log(`📊 响应数据: ${JSON.stringify(response, null, 2)}`);
                
                if (res.statusCode === 200 && response.success) {
                    console.log('✅ 标记已用功能测试成功！');
                } else {
                    console.log('❌ 标记已用功能测试失败！');
                    console.log(`错误信息: ${response.message || '未知错误'}`);
                }
            } catch (error) {
                console.log('❌ 解析响应数据失败:', error.message);
                console.log('原始响应:', data);
            }
        });
    });
    
    req.on('error', (error) => {
        console.log('❌ 请求失败:', error.message);
    });
    
    // 发送请求
    req.write(postData);
    req.end();
}

// 运行测试
testMarkUsed();
