#!/bin/bash

# UPC管理系统 V2.9 Linux版 卸载脚本
# 适用于 CentOS 8 系统
# 作者: UPC管理系统开发团队
# 版本: V2.9.0
# 日期: 2025-07-07

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo bash uninstall.sh"
        exit 1
    fi
}

# 确认卸载
confirm_uninstall() {
    echo "=================================================================="
    echo -e "${RED}⚠️  UPC管理系统 V2.9 卸载确认${NC}"
    echo "=================================================================="
    echo
    echo -e "${YELLOW}警告: 此操作将完全删除UPC管理系统及其所有数据！${NC}"
    echo
    echo "将要删除的内容："
    echo "  • UPC系统服务"
    echo "  • 系统文件 (/opt/upc-system)"
    echo "  • 用户数据和配置"
    echo "  • 系统用户 (upcadmin)"
    echo "  • 防火墙规则"
    echo "  • systemd服务配置"
    echo
    echo -e "${RED}注意: 此操作不可逆！请确保已备份重要数据。${NC}"
    echo
    
    read -p "确定要继续卸载吗? 请输入 'YES' 确认: " -r
    if [[ ! $REPLY == "YES" ]]; then
        log_info "卸载已取消"
        exit 0
    fi
    
    echo
    read -p "最后确认: 真的要删除所有数据吗? 请再次输入 'YES': " -r
    if [[ ! $REPLY == "YES" ]]; then
        log_info "卸载已取消"
        exit 0
    fi
}

# 停止服务
stop_services() {
    log_step "停止UPC系统服务..."
    
    if systemctl is-active --quiet upc-system; then
        log_info "停止upc-system服务..."
        systemctl stop upc-system
        log_info "✓ 服务已停止"
    else
        log_info "服务未运行"
    fi
    
    if systemctl is-enabled --quiet upc-system; then
        log_info "禁用upc-system服务..."
        systemctl disable upc-system
        log_info "✓ 服务已禁用"
    fi
}

# 删除systemd服务文件
remove_systemd_service() {
    log_step "删除systemd服务配置..."
    
    local service_file="/etc/systemd/system/upc-system.service"
    
    if [[ -f "$service_file" ]]; then
        log_info "删除服务文件: $service_file"
        rm -f "$service_file"
        systemctl daemon-reload
        log_info "✓ systemd服务配置已删除"
    else
        log_info "systemd服务文件不存在"
    fi
}

# 删除防火墙规则
remove_firewall_rules() {
    log_step "删除防火墙规则..."
    
    if systemctl is-active --quiet firewalld; then
        log_info "删除防火墙规则..."
        
        # 删除端口规则
        firewall-cmd --permanent --remove-port=3000/tcp 2>/dev/null || true
        firewall-cmd --permanent --remove-port=8080/tcp 2>/dev/null || true
        
        # 重新加载防火墙
        firewall-cmd --reload
        
        log_info "✓ 防火墙规则已删除"
    else
        log_info "firewalld未运行，跳过防火墙配置"
    fi
}

# 备份数据（可选）
backup_data() {
    local install_dir="/opt/upc-system"
    local backup_dir="/tmp/upc-system-uninstall-backup-$(date +%Y%m%d_%H%M%S)"
    
    if [[ -d "$install_dir/data" ]]; then
        read -p "是否要在删除前备份数据? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_step "备份数据..."
            
            mkdir -p "$backup_dir"
            cp -r "$install_dir/data" "$backup_dir/"
            cp -r "$install_dir/logs" "$backup_dir/" 2>/dev/null || true
            cp "$install_dir/package.json" "$backup_dir/" 2>/dev/null || true
            
            # 创建备份信息文件
            cat > "$backup_dir/backup_info.txt" << EOF
UPC管理系统卸载备份
备份时间: $(date)
原安装目录: $install_dir
备份内容: 数据文件、日志文件、配置文件
EOF
            
            log_info "✓ 数据已备份到: $backup_dir"
            echo "   备份包含: 数据文件、日志文件、配置文件"
        fi
    fi
}

# 删除系统文件
remove_system_files() {
    log_step "删除系统文件..."
    
    local install_dir="/opt/upc-system"
    
    if [[ -d "$install_dir" ]]; then
        log_info "删除安装目录: $install_dir"
        rm -rf "$install_dir"
        log_info "✓ 系统文件已删除"
    else
        log_info "安装目录不存在"
    fi
    
    # 删除可能的其他文件
    local other_files=(
        "/etc/upc-system"
        "/var/log/upc-system"
        "/var/lib/upc-system"
    )
    
    for file in "${other_files[@]}"; do
        if [[ -e "$file" ]]; then
            log_info "删除: $file"
            rm -rf "$file"
        fi
    done
}

# 删除系统用户
remove_system_user() {
    log_step "删除系统用户..."
    
    local username="upcadmin"
    
    if id "$username" &>/dev/null; then
        log_info "删除用户: $username"
        
        # 杀死用户进程
        pkill -u "$username" 2>/dev/null || true
        sleep 2
        
        # 删除用户和家目录
        userdel -r "$username" 2>/dev/null || userdel "$username" 2>/dev/null || true
        
        # 确保家目录被删除
        if [[ -d "/home/<USER>" ]]; then
            rm -rf "/home/<USER>"
        fi
        
        log_info "✓ 用户已删除"
    else
        log_info "用户不存在"
    fi
}

# 清理环境变量
clean_environment() {
    log_step "清理环境变量..."
    
    # 从/etc/environment中删除UPC相关变量
    if [[ -f "/etc/environment" ]]; then
        sed -i '/^UPC_/d' /etc/environment
        log_info "✓ 环境变量已清理"
    fi
    
    # 清理可能的profile文件
    local profile_files=(
        "/etc/profile.d/upc-system.sh"
        "/etc/bash.bashrc.d/upc-system.sh"
    )
    
    for file in "${profile_files[@]}"; do
        if [[ -f "$file" ]]; then
            rm -f "$file"
            log_info "删除profile文件: $file"
        fi
    done
}

# 清理crontab任务
clean_crontab() {
    log_step "清理定时任务..."
    
    # 清理upcadmin用户的crontab
    if id "upcadmin" &>/dev/null; then
        crontab -u upcadmin -r 2>/dev/null || true
        log_info "✓ 用户定时任务已清理"
    fi
    
    # 清理系统crontab中的UPC相关任务
    if [[ -f "/etc/crontab" ]]; then
        sed -i '/upc-system\|UPC/d' /etc/crontab
    fi
    
    # 清理cron.d中的文件
    if [[ -f "/etc/cron.d/upc-system" ]]; then
        rm -f "/etc/cron.d/upc-system"
        log_info "删除系统定时任务文件"
    fi
}

# 清理日志轮转配置
clean_logrotate() {
    log_step "清理日志轮转配置..."
    
    local logrotate_file="/etc/logrotate.d/upc-system"
    
    if [[ -f "$logrotate_file" ]]; then
        rm -f "$logrotate_file"
        log_info "✓ 日志轮转配置已删除"
    else
        log_info "日志轮转配置不存在"
    fi
}

# 清理临时文件
clean_temp_files() {
    log_step "清理临时文件..."
    
    # 清理/tmp中的UPC相关文件
    find /tmp -name "*upc*" -type f -delete 2>/dev/null || true
    find /tmp -name "*UPC*" -type f -delete 2>/dev/null || true
    
    # 清理/var/tmp中的文件
    find /var/tmp -name "*upc*" -type f -delete 2>/dev/null || true
    
    log_info "✓ 临时文件已清理"
}

# 验证卸载
verify_uninstall() {
    log_step "验证卸载..."
    
    local issues=0
    
    # 检查服务
    if systemctl list-units --all | grep -q upc-system; then
        log_warn "警告: systemd中仍存在upc-system服务"
        ((issues++))
    fi
    
    # 检查文件
    if [[ -d "/opt/upc-system" ]]; then
        log_warn "警告: 安装目录仍然存在"
        ((issues++))
    fi
    
    # 检查用户
    if id "upcadmin" &>/dev/null; then
        log_warn "警告: upcadmin用户仍然存在"
        ((issues++))
    fi
    
    # 检查进程
    if pgrep -f "upc-system\|simple-server" >/dev/null; then
        log_warn "警告: 仍有相关进程在运行"
        ((issues++))
    fi
    
    # 检查端口
    if ss -tlnp | grep -q ":3000"; then
        log_warn "警告: 端口3000仍被占用"
        ((issues++))
    fi
    
    if [[ $issues -eq 0 ]]; then
        log_info "✓ 卸载验证通过"
        return 0
    else
        log_warn "发现 $issues 个潜在问题"
        return 1
    fi
}

# 显示卸载完成信息
show_completion_info() {
    echo
    echo "=================================================================="
    echo -e "${GREEN}✅ UPC管理系统 V2.9 卸载完成！${NC}"
    echo "=================================================================="
    echo
    echo -e "${BLUE}📋 卸载摘要:${NC}"
    echo "   ✓ UPC系统服务已停止并删除"
    echo "   ✓ 系统文件已删除"
    echo "   ✓ 用户账户已删除"
    echo "   ✓ 防火墙规则已清理"
    echo "   ✓ 环境变量已清理"
    echo "   ✓ 定时任务已清理"
    echo "   ✓ 临时文件已清理"
    echo
    
    # 显示备份信息
    local backup_dir=$(find /tmp -name "upc-system-uninstall-backup-*" -type d 2>/dev/null | head -1)
    if [[ -n "$backup_dir" ]]; then
        echo -e "${YELLOW}💾 数据备份:${NC}"
        echo "   备份位置: $backup_dir"
        echo "   备份内容: 数据文件、日志文件、配置文件"
        echo "   注意: 请及时转移备份文件到安全位置"
        echo
    fi
    
    echo -e "${BLUE}🔧 后续建议:${NC}"
    echo "   • 如果不再需要Node.js，可以手动卸载"
    echo "   • 检查系统是否还有其他相关文件"
    echo "   • 如需重新安装，请使用最新的安装包"
    echo
    echo "=================================================================="
}

# 主函数
main() {
    echo "=================================================================="
    echo -e "${RED}🗑️  UPC管理系统 V2.9 Linux版 卸载脚本${NC}"
    echo "=================================================================="
    echo
    
    check_root
    confirm_uninstall
    backup_data
    stop_services
    remove_systemd_service
    remove_firewall_rules
    remove_system_files
    remove_system_user
    clean_environment
    clean_crontab
    clean_logrotate
    clean_temp_files
    verify_uninstall
    show_completion_info
    
    echo -e "${GREEN}卸载完成！感谢您使用UPC管理系统。${NC}"
}

# 执行主函数
main "$@"
