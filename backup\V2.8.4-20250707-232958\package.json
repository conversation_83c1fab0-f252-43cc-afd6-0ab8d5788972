{"name": "upc-management-system-production", "version": "2.8.4", "description": "UPC管理系统 V2.8.4 Linux版 - 企业级UPC码管理解决方案，完善回收管理数据同步，优化用户体验", "main": "simple-server.js", "scripts": {"start": "node simple-server.js", "dev": "nodemon simple-server.js", "production": "NODE_ENV=production node simple-server.js", "debug": "node system-debug-check.js", "test": "node test-system.js", "test-old": "node system-test.js", "check-deps": "node check-dependencies.js", "fix-deps": "chmod +x fix-dependencies.sh && ./fix-dependencies.sh", "install-linux": "chmod +x install.sh && ./install.sh", "service-install": "sudo cp upc-system.service /etc/systemd/system/ && sudo systemctl daemon-reload", "service-start": "sudo systemctl start upc-system && sudo systemctl enable upc-system", "service-status": "sudo systemctl status upc-system", "logs": "sudo journalctl -u upc-system -f"}, "keywords": ["upc", "management", "system", "production", "enterprise"], "author": "深圳速拓电子商务有限公司", "license": "MIT", "dependencies": {"archiver": "^7.0.1", "cors": "^2.8.5", "express": "^4.18.2", "node-cron": "^4.1.1", "nodemailer": "^7.0.4", "tencentcloud-sdk-nodejs": "^4.1.67"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "repository": {"type": "git", "url": "https://github.com/sutuo/upc-management-system.git"}, "bugs": {"url": "https://github.com/sutuo/upc-management-system/issues"}, "homepage": "https://sutuo.net"}