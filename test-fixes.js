// 测试修复后的功能
const http = require('http');

// 测试配置
const BASE_URL = 'http://localhost:3001';
let sessionCookie = '';

// 发送HTTP请求的辅助函数
function makeRequest(path, options = {}) {
    return new Promise((resolve, reject) => {
        const url = new URL(path, BASE_URL);
        const requestOptions = {
            hostname: url.hostname,
            port: url.port,
            path: url.pathname + url.search,
            method: options.method || 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Cookie': sessionCookie,
                ...options.headers
            }
        };

        const req = http.request(requestOptions, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                // 保存会话cookie
                if (res.headers['set-cookie']) {
                    sessionCookie = res.headers['set-cookie'].join('; ');
                }
                
                try {
                    const jsonData = JSON.parse(data);
                    resolve({ status: res.statusCode, data: jsonData });
                } catch (e) {
                    resolve({ status: res.statusCode, data: data });
                }
            });
        });

        req.on('error', reject);
        
        if (options.body) {
            req.write(options.body);
        }
        
        req.end();
    });
}

// 测试函数
async function runTests() {
    console.log('🧪 开始测试修复后的功能...\n');

    try {
        // 1. 登录
        console.log('1️⃣ 测试登录...');
        const loginResult = await makeRequest('/api/auth/login', {
            method: 'POST',
            body: JSON.stringify({
                username: 'admin',
                password: 'admin123'
            })
        });
        
        if (loginResult.data.success) {
            console.log('✅ 登录成功');
        } else {
            console.log('❌ 登录失败:', loginResult.data.message);
            return;
        }

        // 2. 测试申请UPC码
        console.log('\n2️⃣ 测试申请UPC码...');
        const requestResult = await makeRequest('/api/upc/request', {
            method: 'POST',
            body: JSON.stringify({
                quantity: 2,
                purpose: '测试修复功能'
            })
        });
        
        if (requestResult.data.success) {
            console.log('✅ UPC码申请成功');
            console.log('📋 申请ID:', requestResult.data.requestId);
            console.log('🏷️ 分配的UPC码:', requestResult.data.upcCodes);
            
            const testUPCCode = requestResult.data.upcCodes[0];
            
            // 3. 测试自动激活功能
            console.log('\n3️⃣ 测试自动激活功能...');
            
            // 3.1 开启自动激活
            console.log('📝 开启自动激活...');
            const enableAutoResult = await makeRequest('/api/settings', {
                method: 'POST',
                body: JSON.stringify({
                    category: 'autoReactivate',
                    settings: {
                        enabled: true,
                        enabledAt: new Date().toISOString()
                    }
                })
            });
            
            if (enableAutoResult.data.success) {
                console.log('✅ 自动激活已开启');
            } else {
                console.log('❌ 开启自动激活失败:', enableAutoResult.data.message);
            }
            
            // 3.2 回收UPC码（应该自动激活）
            console.log('♻️ 回收UPC码（测试自动激活）...');
            const recycleResult = await makeRequest('/api/upc/recycle', {
                method: 'POST',
                body: JSON.stringify({
                    code: testUPCCode,
                    reason: '测试自动激活',
                    autoReactivateEnabled: true
                })
            });
            
            if (recycleResult.data.success) {
                console.log('✅ UPC码回收成功');
                console.log('📊 回收状态:', recycleResult.data.status);
                
                // 等待一下让自动激活处理完成
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // 检查UPC码状态
                console.log('🔍 检查UPC码状态...');
                const poolResult = await makeRequest('/api/upc/pool');
                if (poolResult.data.success) {
                    const upcCode = poolResult.data.codes.find(c => c.code === testUPCCode);
                    if (upcCode) {
                        console.log('📋 UPC码当前状态:', upcCode.status);
                        if (upcCode.status === 'available') {
                            console.log('✅ 自动激活功能正常工作！UPC码已自动转为可用状态');
                        } else {
                            console.log('⚠️ 自动激活可能未完全生效，当前状态:', upcCode.status);
                        }
                    }
                }
            } else {
                console.log('❌ UPC码回收失败:', recycleResult.data.message);
            }
            
            // 4. 测试申请记录数量显示
            console.log('\n4️⃣ 测试申请记录数量显示...');
            const historyResult = await makeRequest('/api/user/history?userId=admin');
            
            if (historyResult.data.success) {
                console.log('✅ 获取申请记录成功');
                console.log('📊 申请记录数量:', historyResult.data.data.length);
                
                historyResult.data.data.forEach((record, index) => {
                    console.log(`📋 记录 ${index + 1}:`);
                    console.log(`   - 申请ID: ${record.id}`);
                    console.log(`   - 申请数量: ${record.quantity}`);
                    console.log(`   - UPC码数量: ${record.codes.length}`);
                    console.log(`   - 数量匹配: ${record.quantity === record.codes.length ? '✅' : '❌'}`);
                });
            } else {
                console.log('❌ 获取申请记录失败:', historyResult.data.message);
            }
            
        } else {
            console.log('❌ UPC码申请失败:', requestResult.data.message);
        }

        console.log('\n🎉 测试完成！');

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error);
    }
}

// 运行测试
runTests();
