# UPC管理系统 V3.0 - CentOS 8 部署包

## 🚀 系统简介

**UPC管理系统 V3.0** 是一套企业级的UPC码管理解决方案，专为电商企业设计，提供完整的UPC码生命周期管理功能。

### ✨ 主要特性
- 🎯 **UPC码管理**: 生成、分配、回收、状态跟踪
- 📊 **数据可视化**: 实时统计图表和使用分析
- 👥 **多用户权限**: 管理员、经理、操作员三级权限
- 📧 **通知服务**: 邮件和短信通知功能
- 🔄 **自动备份**: 定时数据备份和恢复
- 📱 **响应式设计**: 支持桌面端和移动端
- 🔒 **安全可靠**: 完善的权限控制和数据加密

### 🆕 V3.0 更新内容
- ✅ 修复操作记录时间排序问题
- ✅ 完善删除状态显示功能
- ✅ 优化回收记录管理流程
- ✅ 增强系统稳定性和性能
- ✅ 完善部署和运维工具

---

## 📋 系统要求

### 推荐配置
- **操作系统**: CentOS 8 (推荐)
- **CPU**: 2核心以上
- **内存**: 2GB以上
- **磁盘**: 5GB以上可用空间
- **网络**: 稳定的互联网连接

### 最低配置
- **操作系统**: CentOS 7/8, RHEL 8, Rocky Linux 8
- **CPU**: 1核心
- **内存**: 1GB
- **磁盘**: 2GB可用空间

---

## 📦 部署包内容

```
UPC-System-V3.0-CentOS8-Deploy/
├── 📄 README.md                    # 本文件
├── 📄 部署教程-小白版.md             # 详细部署教程
├── 📄 运维管理教程.md               # 运维管理指南
├── 🔧 install-centos8.sh           # 一键安装脚本
├── 🔍 check-environment.sh         # 环境检查脚本
├── 🗑️ uninstall.sh                # 卸载脚本
├── ⚙️ upc-system.service          # 系统服务配置
├── 📊 simple-server.js             # 主服务程序
├── 🌐 public/                      # 前端文件
├── 📁 data/                        # 数据目录
├── 📋 package.json                 # 依赖配置
└── 📚 docs/                        # 文档目录
```

---

## 🎯 快速开始

### 1. 环境检查（推荐）
```bash
# 给脚本执行权限
chmod +x check-environment.sh

# 运行环境检查
./check-environment.sh
```

### 2. 一键安装（推荐）
```bash
# 给安装脚本执行权限
chmod +x install-centos8.sh

# 运行一键安装
./install-centos8.sh
```

### 3. 访问系统
安装完成后，在浏览器中访问：
```
http://您的服务器IP:3001
```

### 4. 默认账户
- **管理员**: admin / admin123
- **经理**: manager / Manager@2025
- **操作员**: operator / Operator@2025

---

## 🔧 手动安装

如果一键安装失败，请参考 `部署教程-小白版.md` 进行手动安装。

---

## 🛠️ 服务管理

### 基本命令
```bash
# 启动服务
systemctl start upc-system

# 停止服务
systemctl stop upc-system

# 重启服务
systemctl restart upc-system

# 查看状态
systemctl status upc-system

# 查看日志
journalctl -u upc-system -f
```

### 配置文件位置
- **安装目录**: `/opt/upc-system`
- **数据目录**: `/opt/upc-system/data`
- **日志目录**: `/opt/upc-system/logs`
- **服务配置**: `/etc/systemd/system/upc-system.service`

---

## 📊 功能模块

### 1. UPC码管理
- UPC码生成和验证
- 批量导入和导出
- 状态管理（可用、已分配、已回收、无效）
- 历史记录追踪

### 2. 申请管理
- UPC码申请流程
- 申请历史记录
- 状态跟踪和审批

### 3. 回收管理
- UPC码回收流程
- 回收记录管理
- 状态更新和验证

### 4. 统计分析
- 实时数据统计
- 使用趋势分析
- 可视化图表展示

### 5. 系统管理
- 用户权限管理
- 系统设置配置
- 邮件短信通知
- 数据备份恢复

---

## 🔒 安全特性

### 1. 用户权限控制
- 三级权限体系
- 功能模块访问控制
- 操作日志记录

### 2. 数据安全
- 数据加密存储
- 定时自动备份
- 数据完整性检查

### 3. 系统安全
- 防火墙配置
- SSL/TLS支持
- 安全审计日志

---

## 📈 性能特性

### 1. 高性能
- Node.js异步处理
- 内存优化管理
- 数据缓存机制

### 2. 高可用
- 服务自动重启
- 错误恢复机制
- 健康状态监控

### 3. 可扩展
- 模块化设计
- API接口支持
- 插件扩展机制

---

## 🔧 配置说明

### 1. 端口配置
默认端口：3001
修改方法：编辑 `/opt/upc-system/simple-server.js` 中的端口设置

### 2. 邮件配置
编辑系统设置中的邮件配置：
- SMTP服务器
- 端口和安全设置
- 用户名和密码

### 3. 短信配置
配置腾讯云短信服务：
- SecretId和SecretKey
- 短信模板ID
- 签名信息

---

## 📚 文档资源

- 📄 **部署教程-小白版.md**: 详细的部署指南，适合初学者
- 📄 **运维管理教程.md**: 系统运维和管理指南
- 🌐 **在线文档**: https://sutuo.net/docs
- 📞 **技术支持**: <EMAIL>

---

## ❓ 常见问题

### Q: 安装失败怎么办？
A: 请先运行环境检查脚本，确保系统满足要求。如果问题持续，请查看详细日志或联系技术支持。

### Q: 如何修改默认端口？
A: 编辑 `/opt/upc-system/simple-server.js` 文件，修改端口配置后重启服务。

### Q: 如何备份数据？
A: 系统提供自动备份功能，也可以手动运行 `/opt/upc-system/backup.sh` 脚本。

### Q: 如何升级系统？
A: 请参考 `运维管理教程.md` 中的升级章节，或联系技术支持。

---

## 🔄 版本历史

### V3.0 (2025-07-08)
- 修复操作记录排序问题
- 完善删除状态显示
- 优化回收记录管理
- 增强系统稳定性
- 完善部署工具

### V2.9 (2025-07-07)
- 完善回收管理数据同步
- 修复统计数据显示
- 优化用户体验

### V2.8 (2025-07-06)
- 新增批量操作功能
- 优化性能和稳定性
- 完善权限控制

---

## 📞 技术支持

**深圳速拓电子商务有限公司**

- 📧 **邮箱**: <EMAIL>
- 🌐 **官网**: https://sutuo.net
- 📱 **电话**: [技术支持电话]
- 💬 **QQ群**: [技术交流群]

### 支持服务
- ✅ 免费技术咨询
- ✅ 安装部署指导
- ✅ 故障排除支持
- ✅ 系统升级服务
- ✅ 定制开发服务

---

## 📄 许可证

本软件采用 MIT 许可证，详情请参考 LICENSE 文件。

---

## 🙏 致谢

感谢所有为本项目贡献代码和建议的开发者和用户！

---

**© 2025 深圳速拓电子商务有限公司 版权所有**

*UPC管理系统 V3.0 - 让UPC码管理更简单、更高效！*
