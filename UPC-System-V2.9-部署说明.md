# UPC管理系统 V2.9 Linux版 部署说明

## 🎉 升级完成摘要

### ✅ 已完成的工作

1. **系统备份**
   - ✅ 完整备份V2.8.4系统到 `backup/V2.8.4-20250707-232958/`
   - ✅ 保留所有原始数据和配置文件

2. **版本升级**
   - ✅ 系统版本从V2.8.4升级到V2.9.0
   - ✅ 更新所有文件中的版本号信息
   - ✅ 更新package.json版本和描述信息
   - ✅ 更新VERSION_LOG.md添加V2.9.0更新记录

3. **数据清理**
   - ✅ 清空UPC管理池数据（upc_codes.json: 1007条 → 0条）
   - ✅ 清空回收记录（recycle_records.json: 239条 → 0条）
   - ✅ 清空申请记录（applications.json: 125条 → 0条）
   - ✅ 清空报表数据（reports.json）
   - ✅ 清空操作日志（operation_logs.json）
   - ✅ 清空性能指标（performance_metrics.json）
   - ✅ 保留用户账户数据（users.json）

4. **文档更新**
   - ✅ 修正登录用户信息文档
   - ✅ 更新README-Linux.md中的用户凭据表
   - ✅ 更新simple-server.js中的启动信息显示

5. **部署包创建**
   - ✅ 创建完整的Linux部署包
   - ✅ 包含所有系统文件和依赖
   - ✅ 创建一键安装脚本（install.sh）
   - ✅ 创建卸载脚本（uninstall.sh）
   - ✅ 创建详细部署指南（DEPLOYMENT_GUIDE.md）
   - ✅ 创建运维教程（MAINTENANCE_GUIDE.md）

## 📦 部署包内容

### 文件结构
```
UPC-System-V2.9-Linux-Deploy.zip (229KB)
├── install.sh                    # 一键安装脚本
├── uninstall.sh                  # 卸载脚本
├── simple-server.js              # 主服务器文件 (V2.9)
├── package.json                  # 项目配置 (V2.9.0)
├── data/                         # 数据目录（已清空）
├── public/                       # 前端文件（响应式设计）
├── logs/                         # 日志目录
├── *.js                         # 服务模块文件
├── README.md                    # 部署包说明
├── README-Linux.md              # Linux使用说明
├── DEPLOYMENT_GUIDE.md          # 详细部署指南
├── MAINTENANCE_GUIDE.md         # 运维教程
└── VERSION_LOG.md               # 版本更新日志
```

### 核心特性
- 🎨 **全新响应式设计**: 完美适配桌面和移动设备
- 🚀 **一键安装部署**: CentOS 8兼容的自动化安装
- 📊 **完善服务集成**: 邮件、短信、备份、日志服务
- 🔧 **详细运维文档**: 小白友好的部署和维护指南
- 🔒 **安全配置**: 完善的权限和安全设置

## 🚀 部署步骤

### 第一步：上传部署包

1. **使用SCP上传**（推荐）
   ```bash
   scp UPC-System-V2.9-Linux-Deploy.zip root@************:/tmp/
   ```

2. **或使用其他方式**
   - FTP/SFTP工具
   - 云服务器控制台上传
   - wget下载（如果有下载链接）

### 第二步：连接服务器

```bash
ssh root@************
```

### 第三步：解压并安装

```bash
# 进入临时目录
cd /tmp

# 解压部署包
unzip UPC-System-V2.9-Linux-Deploy.zip
cd UPC-System-V2.9-Linux-Deploy

# 设置脚本执行权限
chmod +x *.sh

# 执行一键安装
./install.sh
```

### 第四步：等待安装完成

安装过程约需要5-10分钟，脚本会自动：
- 检查系统环境
- 安装Node.js和依赖
- 创建系统用户
- 配置服务和防火墙
- 启动UPC系统

### 第五步：验证安装

安装完成后，访问：
- **外网地址**: http://************:3000
- **健康检查**: http://************:3000/api/health

## 👤 系统账户

| 角色 | 用户名 | 密码 | 权限 |
|------|--------|------|------|
| 系统管理员 | admin | admin123 | 系统管理、用户管理、备份恢复 |
| 业务经理 | manager | Manager@2025 | UPC管理、报表查看 |
| 操作员 | operator | Operator@2025 | UPC申请、回收操作 |

## 🔧 服务状态

安装完成后，以下功能将自动启用：
- ✅ **详细日志已启用**: 完整的操作和错误日志
- ✅ **错误堆栈已显示**: 详细的错误信息显示
- ✅ **调试信息已开启**: 开发和调试信息
- ✅ **邮件服务启用**: 支持邮件通知功能
- ✅ **短信服务启用**: 支持短信通知功能
- ✅ **备份功能启用**: 自动数据备份
- ✅ **日志服务启用**: 系统日志管理

## 🔄 服务管理

```bash
# 查看服务状态
systemctl status upc-system

# 启动服务
systemctl start upc-system

# 停止服务
systemctl stop upc-system

# 重启服务
systemctl restart upc-system

# 查看实时日志
journalctl -u upc-system -f
```

## 📊 系统监控

### 日志位置
- **应用日志**: `/opt/upc-system/logs/`
- **系统日志**: `journalctl -u upc-system`

### 性能监控
- **CPU/内存使用**: `top` 或 `htop`
- **磁盘空间**: `df -h`
- **网络连接**: `ss -tlnp | grep 3000`

### 自动监控
系统已配置自动监控脚本，每5分钟检查：
- 服务运行状态
- 资源使用情况
- HTTP响应状态

## 🔒 安全配置

### 防火墙
```bash
# 查看开放端口
firewall-cmd --list-ports

# 应该显示: 3000/tcp 8080/tcp
```

### 文件权限
```bash
# 检查安装目录权限
ls -la /opt/upc-system/

# 应该显示 upcadmin:upcadmin 权限
```

## 🗑️ 卸载系统

如需完全卸载系统：

```bash
cd /opt/upc-system
./uninstall.sh
```

**注意**: 卸载会删除所有数据，请提前备份重要信息。

## 📖 详细文档

部署包中包含完整的文档：

1. **README.md**: 部署包总体说明
2. **DEPLOYMENT_GUIDE.md**: 详细部署指南（小白友好）
3. **MAINTENANCE_GUIDE.md**: 运维教程和故障排除
4. **README-Linux.md**: 系统使用说明
5. **VERSION_LOG.md**: 版本更新历史

## 🆘 故障排除

### 常见问题

1. **安装失败**
   ```bash
   # 检查系统版本
   cat /etc/redhat-release
   
   # 检查网络连接
   ping -c 3 *******
   
   # 重新运行安装
   ./install.sh
   ```

2. **服务无法启动**
   ```bash
   # 查看详细错误
   journalctl -u upc-system --no-pager
   
   # 检查端口占用
   ss -tlnp | grep 3000
   
   # 重启服务
   systemctl restart upc-system
   ```

3. **无法访问Web界面**
   ```bash
   # 检查防火墙
   firewall-cmd --list-ports
   
   # 测试本地连接
   curl http://localhost:3000
   
   # 检查服务监听
   ss -tlnp | grep 3000
   ```

## 📞 技术支持

### 系统信息
- **版本**: UPC管理系统 V2.9.0 Linux版
- **发布日期**: 2025-07-07
- **目标系统**: CentOS 8 (IP: ************)
- **安装用户**: root
- **服务用户**: upcadmin

### 获取帮助
1. 查看详细文档（DEPLOYMENT_GUIDE.md, MAINTENANCE_GUIDE.md）
2. 检查系统日志和应用日志
3. 确认系统要求是否满足
4. 联系技术支持团队

---

## 🎯 下一步操作

1. **立即部署**: 按照上述步骤在CentOS 8服务器上部署系统
2. **首次登录**: 使用admin/admin123登录系统
3. **修改密码**: 首次登录后立即修改所有默认密码
4. **配置服务**: 根据需要配置邮件和短信服务
5. **开始使用**: 体验全新的响应式UPC管理系统

**🎉 恭喜！UPC管理系统V2.9升级和部署包创建完成！**

现在您拥有了一个完整的、生产就绪的UPC管理系统部署包，包含：
- ✅ 完整的系统备份
- ✅ 版本升级到V2.9
- ✅ 数据清理完成
- ✅ 一键安装脚本
- ✅ 详细的部署和运维文档
- ✅ 所有必需的服务集成

立即开始部署，享受全新的UPC管理体验！
