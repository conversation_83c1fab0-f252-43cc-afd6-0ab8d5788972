#!/usr/bin/env node

// 调试"标记已使用失败: UPC码不存在"错误
// 分析具体的失败原因和数据状态

const http = require('http');

console.log('🔍 调试标记已使用错误');
console.log('=====================================');

// 测试配置
const TEST_CONFIG = {
    baseUrl: 'http://localhost:3001',
    timeout: 10000,
    testUser: { username: 'manager', password: 'Manager@2025' }
};

// HTTP请求工具函数
function makeRequest(options) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const result = {
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data ? JSON.parse(data) : null
                    };
                    resolve(result);
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data,
                        parseError: error.message
                    });
                }
            });
        });
        
        req.on('error', reject);
        req.setTimeout(TEST_CONFIG.timeout, () => {
            req.destroy();
            reject(new Error('请求超时'));
        });
        
        if (options.body) {
            req.write(options.body);
        }
        req.end();
    });
}

// 登录函数
async function login(credentials) {
    console.log(`🔍 用户登录: ${credentials.username}...`);
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(credentials)
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        console.log(`✅ 登录成功: ${response.data.user.name} (${response.data.user.role})`);
        return response.data.sessionId;
    } else {
        throw new Error(`登录失败: ${response.data?.message || '未知错误'}`);
    }
}

// 获取回收历史
async function getRecycleHistory(sessionId) {
    console.log('🔍 获取回收历史...');
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/recycle/history',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${sessionId}`
        }
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        console.log(`✅ 获取回收历史成功，记录数: ${response.data.data.length}`);
        return response.data.data;
    } else {
        throw new Error(`获取回收历史失败: ${response.data?.message || '未知错误'}`);
    }
}

// 尝试标记已使用
async function attemptMarkAsUsed(sessionId, upcId, upcCode) {
    console.log(`🎯 尝试标记已使用: UPC ID ${upcId}, 码 ${upcCode}...`);
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc/update',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${sessionId}`
        },
        body: JSON.stringify({
            upcId: upcId,
            updates: {
                status: 'invalid',
                notes: '标记为已使用'
            }
        })
    };
    
    const response = await makeRequest(options);
    
    console.log(`📊 标记已使用响应:`, {
        statusCode: response.statusCode,
        success: response.data?.success,
        message: response.data?.message
    });
    
    return {
        success: response.statusCode === 200 && response.data?.success,
        response: response
    };
}

// 获取UPC池数据
async function getUPCPoolData(sessionId) {
    console.log('🔍 获取UPC池数据...');
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc/pool-data?page=1&limit=50',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${sessionId}`
        }
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        console.log(`✅ 获取UPC池数据成功，总数: ${response.data.data.total}`);
        return response.data.data.codes || [];
    } else {
        throw new Error(`获取UPC池数据失败: ${response.data?.message || '未知错误'}`);
    }
}

// 主调试函数
async function debugMarkUsedError() {
    try {
        console.log('开始调试标记已使用错误...\n');
        
        // 步骤1: 登录
        const sessionId = await login(TEST_CONFIG.testUser);
        
        // 步骤2: 获取回收历史
        console.log('\n📊 获取回收历史数据...');
        const recycleHistory = await getRecycleHistory(sessionId);
        
        // 步骤3: 获取UPC池数据
        console.log('\n📊 获取UPC池数据...');
        const upcPoolData = await getUPCPoolData(sessionId);
        
        // 步骤4: 分析数据
        console.log('\n🔍 数据分析...');
        console.log(`回收历史记录数: ${recycleHistory.length}`);
        console.log(`UPC池数据记录数: ${upcPoolData.length}`);
        
        // 分析upcId字段情况
        const recordsWithUpcId = recycleHistory.filter(r => r.upcId);
        const recordsWithoutUpcId = recycleHistory.filter(r => !r.upcId);
        console.log(`有upcId的回收记录数: ${recordsWithUpcId.length}`);
        console.log(`没有upcId的回收记录数: ${recordsWithoutUpcId.length}`);

        // 显示没有upcId的记录示例
        if (recordsWithoutUpcId.length > 0) {
            console.log('\n❌ 没有upcId的记录示例:');
            recordsWithoutUpcId.slice(0, 3).forEach((record, index) => {
                console.log(`  ${index + 1}. ${record.code} - upcId: ${record.upcId} (${typeof record.upcId})`);
            });
        }

        if (recordsWithUpcId.length === 0) {
            console.log('❌ 没有找到有upcId的回收记录');
            return;
        }
        
        // 选择一个测试记录
        const testRecord = recordsWithUpcId[0];
        console.log(`\n🎯 选择测试记录:`, {
            id: testRecord.id,
            code: testRecord.code,
            upcId: testRecord.upcId,
            status: testRecord.status
        });
        
        // 检查UPC池中是否存在对应的UPC码
        const correspondingUPC = upcPoolData.find(u => u.id == testRecord.upcId);
        console.log(`\n🔍 UPC池中对应记录:`, correspondingUPC ? {
            id: correspondingUPC.id,
            code: correspondingUPC.code,
            status: correspondingUPC.status,
            assigned_user: correspondingUPC.assigned_user
        } : '❌ 未找到');
        
        // 步骤5: 尝试标记已使用
        console.log('\n🎯 尝试标记已使用...');
        const markResult = await attemptMarkAsUsed(sessionId, testRecord.upcId, testRecord.code);
        
        // 步骤6: 生成调试报告
        console.log('\n📊 调试报告');
        console.log('=====================================');
        console.log(`测试用户: ${TEST_CONFIG.testUser.username}`);
        console.log(`测试记录: ${testRecord.code} (ID: ${testRecord.upcId})`);
        console.log(`回收记录状态: ${testRecord.status}`);
        console.log(`UPC池中存在: ${correspondingUPC ? '✅ 是' : '❌ 否'}`);
        if (correspondingUPC) {
            console.log(`UPC池状态: ${correspondingUPC.status}`);
            console.log(`分配用户: ${correspondingUPC.assigned_user}`);
        }
        console.log(`标记已使用结果: ${markResult.success ? '✅ 成功' : '❌ 失败'}`);
        if (!markResult.success) {
            console.log(`失败原因: ${markResult.response.data?.message}`);
        }
        
        console.log('\n=====================================');
        if (markResult.success) {
            console.log('🎉 标记已使用成功！');
        } else {
            console.log('❌ 标记已使用失败，需要进一步分析。');
            
            // 详细错误分析
            console.log('\n🔍 详细错误分析:');
            if (!correspondingUPC) {
                console.log('- UPC池中不存在对应的UPC码记录');
                console.log('- 可能原因: upcId不匹配或数据同步问题');
            } else {
                console.log('- UPC码存在于UPC池中');
                console.log('- 可能原因: 权限检查或其他业务逻辑问题');
            }
        }
        
    } catch (error) {
        console.error('❌ 调试失败:', error.message);
        process.exit(1);
    }
}

// 运行调试
debugMarkUsedError();
