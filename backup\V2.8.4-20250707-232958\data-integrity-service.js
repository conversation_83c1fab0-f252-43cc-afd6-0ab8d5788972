// 数据完整性检查和修复服务
const fs = require('fs');
const path = require('path');

class DataIntegrityService {
    constructor() {
        this.dataDir = path.join(__dirname, 'data');
        this.backupDir = path.join(this.dataDir, 'backups');
        this.logFile = path.join(__dirname, 'logs', 'data-integrity.log');
        
        this.dataFiles = {
            users: 'users.json',
            upcCodes: 'upc_codes.json',
            applications: 'applications.json',
            recycleRecords: 'recycle_records.json',
            reports: 'reports.json',
            systemSettings: 'system_settings.json',
            operationLogs: 'operation_logs.json'
        };
    }

    // 记录日志
    log(level, message) {
        const timestamp = new Date().toISOString();
        const logEntry = `[${timestamp}] [${level}] ${message}\n`;
        
        console.log(`[${level}] ${message}`);
        
        try {
            const logDir = path.dirname(this.logFile);
            if (!fs.existsSync(logDir)) {
                fs.mkdirSync(logDir, { recursive: true });
            }
            fs.appendFileSync(this.logFile, logEntry);
        } catch (error) {
            console.error('写入日志失败:', error.message);
        }
    }

    // 加载JSON文件
    loadJsonFile(filePath) {
        try {
            if (fs.existsSync(filePath)) {
                const data = fs.readFileSync(filePath, 'utf8');
                return JSON.parse(data);
            }
            return null;
        } catch (error) {
            this.log('ERROR', `加载文件失败 ${filePath}: ${error.message}`);
            return null;
        }
    }

    // 保存JSON文件
    saveJsonFile(filePath, data) {
        try {
            // 创建备份
            if (fs.existsSync(filePath)) {
                const backupPath = filePath + '.backup.' + Date.now();
                fs.copyFileSync(filePath, backupPath);
                this.log('INFO', `创建备份: ${backupPath}`);
            }
            
            fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
            this.log('INFO', `保存文件成功: ${filePath}`);
            return true;
        } catch (error) {
            this.log('ERROR', `保存文件失败 ${filePath}: ${error.message}`);
            return false;
        }
    }

    // 检查数据文件完整性
    checkDataIntegrity() {
        this.log('INFO', '开始数据完整性检查...');
        
        const results = {
            totalFiles: 0,
            validFiles: 0,
            corruptedFiles: [],
            missingFiles: [],
            issues: []
        };

        // 检查每个数据文件
        for (const [key, fileName] of Object.entries(this.dataFiles)) {
            const filePath = path.join(this.dataDir, fileName);
            results.totalFiles++;
            
            if (!fs.existsSync(filePath)) {
                results.missingFiles.push(fileName);
                this.log('WARN', `文件缺失: ${fileName}`);
                continue;
            }

            const data = this.loadJsonFile(filePath);
            if (data === null) {
                results.corruptedFiles.push(fileName);
                this.log('ERROR', `文件损坏: ${fileName}`);
                continue;
            }

            // 验证数据结构
            const validation = this.validateDataStructure(key, data);
            if (!validation.valid) {
                results.issues.push({
                    file: fileName,
                    issues: validation.issues
                });
                this.log('WARN', `数据结构问题 ${fileName}: ${validation.issues.join(', ')}`);
            } else {
                results.validFiles++;
                this.log('INFO', `文件验证通过: ${fileName}`);
            }
        }

        this.log('INFO', `数据完整性检查完成: ${results.validFiles}/${results.totalFiles} 文件正常`);
        return results;
    }

    // 验证数据结构
    validateDataStructure(dataType, data) {
        const result = { valid: true, issues: [] };

        switch (dataType) {
            case 'users':
                if (!Array.isArray(data)) {
                    result.valid = false;
                    result.issues.push('用户数据应该是数组');
                } else {
                    data.forEach((user, index) => {
                        if (!user.id || !user.username || !user.role) {
                            result.issues.push(`用户 ${index} 缺少必要字段`);
                        }
                    });
                }
                break;

            case 'upcCodes':
                if (!Array.isArray(data)) {
                    result.valid = false;
                    result.issues.push('UPC码数据应该是数组');
                } else {
                    data.forEach((upc, index) => {
                        if (!upc.id || !upc.code || !upc.status) {
                            result.issues.push(`UPC码 ${index} 缺少必要字段`);
                        }
                        if (upc.status && !['available', 'allocated', 'recycled', 'invalid'].includes(upc.status)) {
                            result.issues.push(`UPC码 ${index} 状态无效: ${upc.status}`);
                        }
                    });
                }
                break;

            case 'recycleRecords':
                if (!Array.isArray(data)) {
                    result.valid = false;
                    result.issues.push('回收记录数据应该是数组');
                } else {
                    data.forEach((record, index) => {
                        if (!record.id || !record.code || !record.status) {
                            result.issues.push(`回收记录 ${index} 缺少必要字段`);
                        }
                        if (record.status && !['recycled', 'reusable', 'processed', 'completed'].includes(record.status)) {
                            result.issues.push(`回收记录 ${index} 状态无效: ${record.status}`);
                        }
                    });
                }
                break;

            case 'applications':
                if (!Array.isArray(data)) {
                    result.valid = false;
                    result.issues.push('申请记录数据应该是数组');
                } else {
                    data.forEach((app, index) => {
                        if (!app.id || !app.user_id || !app.quantity) {
                            result.issues.push(`申请记录 ${index} 缺少必要字段`);
                        }
                    });
                }
                break;

            default:
                // 对于其他类型，只检查是否是有效的JSON
                if (typeof data !== 'object') {
                    result.valid = false;
                    result.issues.push('数据格式无效');
                }
                break;
        }

        if (result.issues.length > 0) {
            result.valid = false;
        }

        return result;
    }

    // 修复数据一致性问题
    fixDataConsistency() {
        this.log('INFO', '开始修复数据一致性问题...');
        
        const upcCodes = this.loadJsonFile(path.join(this.dataDir, 'upc_codes.json'));
        const recycleRecords = this.loadJsonFile(path.join(this.dataDir, 'recycle_records.json'));
        
        if (!upcCodes || !recycleRecords) {
            this.log('ERROR', '无法加载必要的数据文件');
            return false;
        }

        let fixedCount = 0;
        const issues = [];

        // 修复UPC码和回收记录的状态一致性
        recycleRecords.forEach((record, index) => {
            const upcCode = upcCodes.find(u => u.code === record.code);
            
            if (!upcCode) {
                issues.push(`回收记录 ${record.code} 没有对应的UPC码`);
                return;
            }

            let needsFix = false;
            let newRecordStatus = record.status;

            // 修复规则
            if (upcCode.status === 'available' && record.status === 'recycled') {
                newRecordStatus = 'reusable';
                needsFix = true;
            } else if (upcCode.status === 'invalid' && record.status === 'reusable') {
                newRecordStatus = 'processed';
                needsFix = true;
            }

            if (needsFix) {
                this.log('INFO', `修复回收记录状态: ${record.code} (${record.status} -> ${newRecordStatus})`);
                recycleRecords[index].status = newRecordStatus;
                recycleRecords[index].updated_at = new Date().toISOString();
                fixedCount++;
            }
        });

        if (fixedCount > 0) {
            const saveResult = this.saveJsonFile(
                path.join(this.dataDir, 'recycle_records.json'),
                recycleRecords
            );
            
            if (saveResult) {
                this.log('INFO', `数据一致性修复完成，修复了 ${fixedCount} 条记录`);
                return true;
            } else {
                this.log('ERROR', '数据保存失败');
                return false;
            }
        } else {
            this.log('INFO', '没有发现需要修复的数据一致性问题');
            return true;
        }
    }

    // 清理重复记录
    cleanupDuplicateRecords() {
        this.log('INFO', '开始清理重复记录...');
        
        const recycleRecords = this.loadJsonFile(path.join(this.dataDir, 'recycle_records.json'));
        
        if (!recycleRecords) {
            this.log('ERROR', '无法加载回收记录文件');
            return false;
        }

        const uniqueRecords = [];
        const seenCodes = new Set();
        let removedCount = 0;

        // 保留每个UPC码的最新记录
        recycleRecords
            .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
            .forEach(record => {
                if (!seenCodes.has(record.code)) {
                    uniqueRecords.push(record);
                    seenCodes.add(record.code);
                } else {
                    removedCount++;
                    this.log('INFO', `移除重复记录: ${record.code} (${record.created_at})`);
                }
            });

        if (removedCount > 0) {
            const saveResult = this.saveJsonFile(
                path.join(this.dataDir, 'recycle_records.json'),
                uniqueRecords
            );
            
            if (saveResult) {
                this.log('INFO', `重复记录清理完成，移除了 ${removedCount} 条重复记录`);
                return true;
            } else {
                this.log('ERROR', '数据保存失败');
                return false;
            }
        } else {
            this.log('INFO', '没有发现重复记录');
            return true;
        }
    }

    // 执行完整的数据维护
    performMaintenance() {
        this.log('INFO', '开始执行数据维护...');
        
        const results = {
            integrityCheck: this.checkDataIntegrity(),
            consistencyFix: this.fixDataConsistency(),
            duplicateCleanup: this.cleanupDuplicateRecords()
        };

        this.log('INFO', '数据维护完成');
        return results;
    }
}

module.exports = DataIntegrityService;
