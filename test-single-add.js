// 测试单个UPC码添加功能
const http = require('http');

// 测试添加单个UPC码
function testSingleAdd() {
    console.log('🧪 测试单个UPC码添加功能...');
    
    // 测试数据
    const testUPCCode = 'SINGLE123456789';
    
    // 构建请求数据
    const postData = JSON.stringify({
        code: testUPCCode,
        status: 'available'
    });
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc-codes',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData)
        }
    };
    
    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            console.log(`📊 响应状态码: ${res.statusCode}`);
            
            try {
                const response = JSON.parse(data);
                console.log(`📊 响应数据: ${JSON.stringify(response, null, 2)}`);
                
                if (res.statusCode === 201 && response.success) {
                    const newUPCCode = response.data;
                    console.log('✅ 单个UPC码添加成功！');
                    console.log(`📋 新UPC码信息:`);
                    console.log(`   - ID: ${newUPCCode.id}`);
                    console.log(`   - 码: ${newUPCCode.code}`);
                    console.log(`   - 状态: ${newUPCCode.status}`);
                    
                    // 验证ID格式
                    if (typeof newUPCCode.id === 'string' && newUPCCode.id.startsWith('upc_')) {
                        console.log('✅ ID格式正确（字符串格式：upc_XXX）');
                        
                        // 验证ID是否为最新的
                        setTimeout(() => {
                            verifyLatestId(newUPCCode.id);
                        }, 1000);
                        
                    } else {
                        console.log(`❌ ID格式错误：${newUPCCode.id} (类型: ${typeof newUPCCode.id})`);
                    }
                    
                } else {
                    console.log('❌ 单个UPC码添加失败！');
                    console.log(`错误信息: ${response.message || '未知错误'}`);
                }
            } catch (error) {
                console.log('❌ 解析响应数据失败:', error.message);
                console.log('原始响应:', data);
            }
        });
    });
    
    req.on('error', (error) => {
        console.log('❌ 请求失败:', error.message);
    });
    
    // 发送请求
    req.write(postData);
    req.end();
}

// 验证最新ID是否正确
function verifyLatestId(expectedId) {
    console.log('\n🔍 验证最新ID是否正确...');
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc-codes',
        method: 'GET'
    };
    
    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                
                if (response.success && response.data) {
                    const upcCodes = response.data;
                    console.log(`📊 当前UPC码总数: ${upcCodes.length}`);
                    
                    // 提取所有数字ID并排序
                    const numericIds = upcCodes
                        .map(c => {
                            if (typeof c.id === 'string' && c.id.startsWith('upc_')) {
                                return parseInt(c.id.replace('upc_', ''));
                            }
                            return 0;
                        })
                        .filter(id => !isNaN(id) && id > 0)
                        .sort((a, b) => a - b);
                    
                    const maxId = Math.max(...numericIds);
                    const expectedNumericId = parseInt(expectedId.replace('upc_', ''));
                    
                    console.log(`📋 最大ID号码: ${maxId}`);
                    console.log(`📋 新添加的ID号码: ${expectedNumericId}`);
                    
                    if (expectedNumericId === maxId) {
                        console.log('✅ 新添加的UPC码获得了正确的最新ID！');
                    } else {
                        console.log('❌ 新添加的UPC码ID不是最新的！');
                    }
                    
                    // 检查ID连续性
                    let hasGaps = false;
                    for (let i = 1; i < numericIds.length; i++) {
                        if (numericIds[i] - numericIds[i-1] > 1) {
                            hasGaps = true;
                            break;
                        }
                    }
                    
                    if (!hasGaps) {
                        console.log('✅ ID序列连续，没有跳号！');
                    } else {
                        console.log('⚠️ ID序列存在跳号（这可能是正常的，如果之前删除过UPC码）');
                    }
                    
                    // 显示最后几个UPC码
                    console.log('\n📋 最新的5个UPC码:');
                    const latestCodes = upcCodes.slice(-5);
                    latestCodes.forEach(code => {
                        console.log(`   - ID: ${code.id}, 码: ${code.code}, 状态: ${code.status}`);
                    });
                    
                    console.log('\n🎉 验证完成！');
                }
            } catch (error) {
                console.log('❌ 解析响应数据失败:', error.message);
            }
        });
    });
    
    req.on('error', (error) => {
        console.log('❌ 请求失败:', error.message);
    });
    
    req.end();
}

// 运行测试
testSingleAdd();
