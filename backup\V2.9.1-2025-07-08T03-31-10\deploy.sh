#!/bin/bash

# UPC管理系统 V2.8.0 部署脚本
# 用于生产环境部署和更新

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
APP_NAME="upc-system"
APP_VERSION="2.8.1"
INSTALL_DIR="/opt/upc-system"
SERVICE_NAME="upc-system"
USER_NAME="upc-system"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 备份现有数据
backup_data() {
    if [[ -d "$INSTALL_DIR" ]]; then
        log_step "备份现有数据..."
        
        BACKUP_DIR="/tmp/upc-system-backup-$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$BACKUP_DIR"
        
        # 备份数据文件
        if [[ -d "$INSTALL_DIR/data" ]]; then
            cp -r "$INSTALL_DIR/data" "$BACKUP_DIR/"
            log_info "数据文件已备份到: $BACKUP_DIR/data"
        fi
        
        # 备份日志文件
        if [[ -d "$INSTALL_DIR/logs" ]]; then
            cp -r "$INSTALL_DIR/logs" "$BACKUP_DIR/"
            log_info "日志文件已备份到: $BACKUP_DIR/logs"
        fi
        
        # 备份配置文件
        if [[ -f "$INSTALL_DIR/package.json" ]]; then
            cp "$INSTALL_DIR/package.json" "$BACKUP_DIR/"
        fi
        
        echo "$BACKUP_DIR" > /tmp/upc-system-last-backup
        log_info "备份完成: $BACKUP_DIR"
    fi
}

# 停止服务
stop_service() {
    log_step "停止现有服务..."
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        systemctl stop "$SERVICE_NAME"
        log_info "服务已停止"
    else
        log_info "服务未运行"
    fi
    
    # 确保进程完全停止
    pkill -f "node.*simple-server.js" 2>/dev/null || true
    sleep 2
}

# 部署新版本
deploy_new_version() {
    log_step "部署新版本..."
    
    # 创建临时目录
    TEMP_DIR="/tmp/upc-system-deploy-$$"
    mkdir -p "$TEMP_DIR"
    
    # 复制新文件
    cp -r ./* "$TEMP_DIR/"
    
    # 如果安装目录不存在，创建它
    if [[ ! -d "$INSTALL_DIR" ]]; then
        mkdir -p "$INSTALL_DIR"
        chown "$USER_NAME:$USER_NAME" "$INSTALL_DIR"
    fi
    
    # 保留现有数据
    if [[ -d "$INSTALL_DIR/data" ]]; then
        cp -r "$INSTALL_DIR/data" "$TEMP_DIR/"
    fi
    
    if [[ -d "$INSTALL_DIR/logs" ]]; then
        cp -r "$INSTALL_DIR/logs" "$TEMP_DIR/"
    fi
    
    if [[ -d "$INSTALL_DIR/backups" ]]; then
        cp -r "$INSTALL_DIR/backups" "$TEMP_DIR/"
    fi
    
    # 替换文件
    rm -rf "$INSTALL_DIR"/*
    cp -r "$TEMP_DIR"/* "$INSTALL_DIR/"
    
    # 设置权限
    chown -R "$USER_NAME:$USER_NAME" "$INSTALL_DIR"
    chmod +x "$INSTALL_DIR"/*.sh
    
    # 清理临时目录
    rm -rf "$TEMP_DIR"
    
    log_info "新版本部署完成"
}

# 安装依赖
install_dependencies() {
    log_step "安装依赖包..."
    
    cd "$INSTALL_DIR"
    sudo -u "$USER_NAME" npm install --production --no-optional
    
    log_info "依赖包安装完成"
}

# 更新服务文件
update_service() {
    log_step "更新系统服务..."
    
    if [[ -f "$INSTALL_DIR/upc-system.service" ]]; then
        cp "$INSTALL_DIR/upc-system.service" "/etc/systemd/system/"
        systemctl daemon-reload
        log_info "服务文件已更新"
    fi
}

# 启动服务
start_service() {
    log_step "启动服务..."
    
    systemctl enable "$SERVICE_NAME"
    systemctl start "$SERVICE_NAME"
    
    # 等待服务启动
    sleep 5
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_info "服务启动成功"
    else
        log_error "服务启动失败"
        systemctl status "$SERVICE_NAME"
        exit 1
    fi
}

# 验证部署
verify_deployment() {
    log_step "验证部署..."
    
    # 检查服务状态
    if ! systemctl is-active --quiet "$SERVICE_NAME"; then
        log_error "服务未运行"
        return 1
    fi
    
    # 检查端口
    if ! netstat -tuln | grep -q ":3001 "; then
        log_error "端口3001未监听"
        return 1
    fi
    
    # 简单的HTTP检查
    sleep 3
    if curl -s http://localhost:3001/api/health > /dev/null; then
        log_info "HTTP服务正常"
    else
        log_warn "HTTP服务可能有问题"
    fi
    
    log_info "部署验证完成"
}

# 显示部署结果
show_result() {
    echo ""
    echo "=========================================="
    echo -e "${GREEN}🎉 UPC管理系统 V$APP_VERSION 部署完成！${NC}"
    echo "=========================================="
    echo ""
    echo "📋 部署信息:"
    echo "  - 版本: V$APP_VERSION"
    echo "  - 安装目录: $INSTALL_DIR"
    echo "  - 服务名称: $SERVICE_NAME"
    echo "  - 运行用户: $USER_NAME"
    echo ""
    echo "🌐 访问信息:"
    echo "  - 访问地址: http://$(hostname -I | awk '{print $1}'):3001"
    echo "  - 管理员账户: admin / admin123"
    echo ""
    echo "🔧 管理命令:"
    echo "  - 查看状态: systemctl status $SERVICE_NAME"
    echo "  - 启动服务: systemctl start $SERVICE_NAME"
    echo "  - 停止服务: systemctl stop $SERVICE_NAME"
    echo "  - 重启服务: systemctl restart $SERVICE_NAME"
    echo "  - 查看日志: journalctl -u $SERVICE_NAME -f"
    echo ""
    
    if [[ -f /tmp/upc-system-last-backup ]]; then
        BACKUP_DIR=$(cat /tmp/upc-system-last-backup)
        echo "💾 数据备份: $BACKUP_DIR"
        echo ""
    fi
}

# 主部署流程
main() {
    echo "=========================================="
    echo "🚀 UPC管理系统 V$APP_VERSION 部署程序"
    echo "=========================================="
    echo ""
    
    check_permissions
    backup_data
    stop_service
    deploy_new_version
    install_dependencies
    update_service
    start_service
    verify_deployment
    show_result
}

# 执行主函数
main "$@"
