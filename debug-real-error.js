#!/usr/bin/env node

// 实时监控和调试"标记已使用失败: UPC码不存在"错误
// 通过拦截API调用来捕获真实的错误场景

const http = require('http');
const fs = require('fs');

console.log('🔍 实时错误监控和调试');
console.log('=====================================');

// 监控配置
const MONITOR_CONFIG = {
    baseUrl: 'http://localhost:3001',
    timeout: 10000,
    logFile: 'error-debug.log'
};

// 日志函数
function log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
    console.log(logMessage);
    
    // 写入日志文件
    fs.appendFileSync(MONITOR_CONFIG.logFile, logMessage + '\n');
}

// HTTP请求工具函数
function makeRequest(options) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const result = {
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data ? JSON.parse(data) : null
                    };
                    resolve(result);
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data,
                        parseError: error.message
                    });
                }
            });
        });
        
        req.on('error', reject);
        req.setTimeout(MONITOR_CONFIG.timeout, () => {
            req.destroy();
            reject(new Error('请求超时'));
        });
        
        if (options.body) {
            req.write(options.body);
        }
        req.end();
    });
}

// 登录函数
async function login(credentials) {
    log(`尝试登录: ${credentials.username}`);
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(credentials)
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        log(`登录成功: ${response.data.user.name} (${response.data.user.role})`, 'success');
        return response.data.sessionId;
    } else {
        throw new Error(`登录失败: ${response.data?.message || '未知错误'}`);
    }
}

// 获取回收历史
async function getRecycleHistory(sessionId) {
    log('获取回收历史...');
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/recycle/history',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${sessionId}`
        }
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        log(`获取回收历史成功，记录数: ${response.data.data.length}`, 'success');
        return response.data.data;
    } else {
        throw new Error(`获取回收历史失败: ${response.data?.message || '未知错误'}`);
    }
}

// 测试标记已使用（模拟各种错误场景）
async function testMarkAsUsedScenarios(sessionId, recycleHistory) {
    log('开始测试各种标记已使用场景...', 'info');
    
    const scenarios = [
        {
            name: '正常场景 - 有效的upcId',
            getRecord: () => recycleHistory.find(r => r.upcId && r.status === 'reusable'),
            description: '使用有效的upcId和reusable状态的记录'
        },
        {
            name: '错误场景1 - 无效的upcId',
            getRecord: () => ({ ...recycleHistory[0], upcId: 99999 }),
            description: '使用不存在的upcId'
        },
        {
            name: '错误场景2 - null upcId',
            getRecord: () => ({ ...recycleHistory[0], upcId: null }),
            description: '使用null作为upcId'
        },
        {
            name: '错误场景3 - undefined upcId',
            getRecord: () => ({ ...recycleHistory[0], upcId: undefined }),
            description: '使用undefined作为upcId'
        },
        {
            name: '错误场景4 - 字符串upcId',
            getRecord: () => ({ ...recycleHistory[0], upcId: 'invalid' }),
            description: '使用字符串作为upcId'
        }
    ];
    
    for (const scenario of scenarios) {
        log(`\n🧪 测试场景: ${scenario.name}`, 'info');
        log(`描述: ${scenario.description}`, 'info');
        
        try {
            const testRecord = scenario.getRecord();
            if (!testRecord) {
                log(`跳过场景: 没有找到合适的测试记录`, 'warning');
                continue;
            }
            
            log(`测试记录: ${testRecord.code}, upcId: ${testRecord.upcId} (${typeof testRecord.upcId})`, 'info');
            
            const options = {
                hostname: 'localhost',
                port: 3001,
                path: '/api/upc/update',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${sessionId}`
                },
                body: JSON.stringify({
                    upcId: testRecord.upcId,
                    updates: {
                        status: 'invalid',
                        notes: '调试测试标记为已使用'
                    }
                })
            };
            
            const response = await makeRequest(options);
            
            if (response.statusCode === 200 && response.data?.success) {
                log(`✅ 场景成功: ${scenario.name}`, 'success');
            } else {
                log(`❌ 场景失败: ${scenario.name}`, 'error');
                log(`错误信息: ${response.data?.message || '未知错误'}`, 'error');
                log(`状态码: ${response.statusCode}`, 'error');
                log(`响应数据: ${JSON.stringify(response.data, null, 2)}`, 'error');
                
                // 如果是"UPC码不存在"错误，记录详细信息
                if (response.data?.message?.includes('UPC码不存在')) {
                    log(`🎯 捕获到目标错误！`, 'error');
                    log(`错误场景: ${scenario.name}`, 'error');
                    log(`upcId值: ${testRecord.upcId}`, 'error');
                    log(`upcId类型: ${typeof testRecord.upcId}`, 'error');
                    log(`请求体: ${JSON.stringify({
                        upcId: testRecord.upcId,
                        updates: {
                            status: 'invalid',
                            notes: '调试测试标记为已使用'
                        }
                    }, null, 2)}`, 'error');
                }
            }
            
        } catch (error) {
            log(`❌ 场景异常: ${scenario.name} - ${error.message}`, 'error');
        }
        
        // 等待一下避免请求过快
        await new Promise(resolve => setTimeout(resolve, 500));
    }
}

// 主监控函数
async function startErrorMonitoring() {
    try {
        log('开始实时错误监控...', 'info');
        
        // 清空日志文件
        fs.writeFileSync(MONITOR_CONFIG.logFile, '');
        
        // 登录
        const sessionId = await login({ username: 'manager', password: 'Manager@2025' });
        
        // 获取回收历史
        const recycleHistory = await getRecycleHistory(sessionId);
        
        // 分析数据
        const reusableRecords = recycleHistory.filter(r => r.status === 'reusable');
        const recordsWithUpcId = recycleHistory.filter(r => r.upcId);
        const recordsWithoutUpcId = recycleHistory.filter(r => !r.upcId);
        
        log(`数据分析:`, 'info');
        log(`  总记录数: ${recycleHistory.length}`, 'info');
        log(`  可重用记录: ${reusableRecords.length}`, 'info');
        log(`  有upcId记录: ${recordsWithUpcId.length}`, 'info');
        log(`  无upcId记录: ${recordsWithoutUpcId.length}`, 'info');
        
        // 测试各种场景
        await testMarkAsUsedScenarios(sessionId, recycleHistory);
        
        log('\n📊 监控完成', 'info');
        log(`详细日志已保存到: ${MONITOR_CONFIG.logFile}`, 'info');
        
    } catch (error) {
        log(`监控失败: ${error.message}`, 'error');
        process.exit(1);
    }
}

// 运行监控
startErrorMonitoring();
