// 系统性能监控服务
const fs = require('fs');
const path = require('path');
const os = require('os');

class PerformanceMonitor {
    constructor() {
        this.startTime = Date.now();
        this.metrics = {
            requests: 0,
            errors: 0,
            responseTime: [],
            memoryUsage: [],
            cpuUsage: [],
            activeConnections: 0
        };
        
        this.logFile = path.join(__dirname, 'logs', 'performance.log');
        this.metricsFile = path.join(__dirname, 'data', 'performance_metrics.json');
        
        // 启动监控
        this.startMonitoring();
    }

    // 记录请求
    recordRequest(responseTime, isError = false) {
        this.metrics.requests++;
        if (isError) {
            this.metrics.errors++;
        }
        
        this.metrics.responseTime.push(responseTime);
        
        // 保持最近1000个响应时间记录
        if (this.metrics.responseTime.length > 1000) {
            this.metrics.responseTime = this.metrics.responseTime.slice(-1000);
        }
    }

    // 记录活跃连接数
    updateActiveConnections(count) {
        this.metrics.activeConnections = count;
    }

    // 获取系统信息
    getSystemInfo() {
        const memUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();
        
        return {
            // 内存使用情况
            memory: {
                rss: Math.round(memUsage.rss / 1024 / 1024), // MB
                heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
                heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
                external: Math.round(memUsage.external / 1024 / 1024), // MB
                arrayBuffers: Math.round(memUsage.arrayBuffers / 1024 / 1024) // MB
            },
            
            // CPU使用情况
            cpu: {
                user: cpuUsage.user,
                system: cpuUsage.system
            },
            
            // 系统信息
            system: {
                platform: os.platform(),
                arch: os.arch(),
                nodeVersion: process.version,
                uptime: Math.round((Date.now() - this.startTime) / 1000), // 秒
                loadAverage: os.loadavg(),
                totalMemory: Math.round(os.totalmem() / 1024 / 1024), // MB
                freeMemory: Math.round(os.freemem() / 1024 / 1024) // MB
            }
        };
    }

    // 获取性能统计
    getPerformanceStats() {
        const responseTime = this.metrics.responseTime;
        const errorRate = this.metrics.requests > 0 ? 
            (this.metrics.errors / this.metrics.requests * 100).toFixed(2) : 0;
        
        let avgResponseTime = 0;
        let p95ResponseTime = 0;
        let p99ResponseTime = 0;
        
        if (responseTime.length > 0) {
            const sorted = [...responseTime].sort((a, b) => a - b);
            avgResponseTime = Math.round(responseTime.reduce((a, b) => a + b, 0) / responseTime.length);
            p95ResponseTime = sorted[Math.floor(sorted.length * 0.95)] || 0;
            p99ResponseTime = sorted[Math.floor(sorted.length * 0.99)] || 0;
        }

        return {
            requests: {
                total: this.metrics.requests,
                errors: this.metrics.errors,
                errorRate: parseFloat(errorRate),
                activeConnections: this.metrics.activeConnections
            },
            responseTime: {
                average: avgResponseTime,
                p95: p95ResponseTime,
                p99: p99ResponseTime,
                samples: responseTime.length
            },
            system: this.getSystemInfo()
        };
    }

    // 检查系统健康状态
    checkHealth() {
        const stats = this.getPerformanceStats();
        const health = {
            status: 'healthy',
            issues: [],
            warnings: []
        };

        // 检查内存使用
        const memoryUsagePercent = (stats.system.memory.heapUsed / stats.system.memory.heapTotal) * 100;
        if (memoryUsagePercent > 90) {
            health.status = 'critical';
            health.issues.push(`内存使用率过高: ${memoryUsagePercent.toFixed(1)}%`);
        } else if (memoryUsagePercent > 80) {
            health.status = 'warning';
            health.warnings.push(`内存使用率较高: ${memoryUsagePercent.toFixed(1)}%`);
        }

        // 检查错误率
        if (stats.requests.errorRate > 10) {
            health.status = 'critical';
            health.issues.push(`错误率过高: ${stats.requests.errorRate}%`);
        } else if (stats.requests.errorRate > 5) {
            if (health.status === 'healthy') health.status = 'warning';
            health.warnings.push(`错误率较高: ${stats.requests.errorRate}%`);
        }

        // 检查响应时间
        if (stats.responseTime.average > 5000) {
            health.status = 'critical';
            health.issues.push(`平均响应时间过长: ${stats.responseTime.average}ms`);
        } else if (stats.responseTime.average > 2000) {
            if (health.status === 'healthy') health.status = 'warning';
            health.warnings.push(`平均响应时间较长: ${stats.responseTime.average}ms`);
        }

        // 检查系统负载
        const loadAvg = stats.system.system.loadAverage[0];
        const cpuCount = os.cpus().length;
        if (loadAvg > cpuCount * 2) {
            health.status = 'critical';
            health.issues.push(`系统负载过高: ${loadAvg.toFixed(2)}`);
        } else if (loadAvg > cpuCount) {
            if (health.status === 'healthy') health.status = 'warning';
            health.warnings.push(`系统负载较高: ${loadAvg.toFixed(2)}`);
        }

        return health;
    }

    // 记录性能日志
    logPerformance(level, message, data = null) {
        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            level,
            message,
            data
        };

        try {
            const logDir = path.dirname(this.logFile);
            if (!fs.existsSync(logDir)) {
                fs.mkdirSync(logDir, { recursive: true });
            }
            
            fs.appendFileSync(this.logFile, JSON.stringify(logEntry) + '\n');
        } catch (error) {
            console.error('写入性能日志失败:', error.message);
        }
    }

    // 保存性能指标
    saveMetrics() {
        try {
            const stats = this.getPerformanceStats();
            const metricsData = {
                timestamp: new Date().toISOString(),
                stats: stats,
                health: this.checkHealth()
            };

            const dataDir = path.dirname(this.metricsFile);
            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }

            fs.writeFileSync(this.metricsFile, JSON.stringify(metricsData, null, 2));
        } catch (error) {
            console.error('保存性能指标失败:', error.message);
        }
    }

    // 启动监控
    startMonitoring() {
        // 每分钟记录一次系统状态
        setInterval(() => {
            const systemInfo = this.getSystemInfo();
            this.metrics.memoryUsage.push({
                timestamp: Date.now(),
                ...systemInfo.memory
            });
            
            // 保持最近100个记录
            if (this.metrics.memoryUsage.length > 100) {
                this.metrics.memoryUsage = this.metrics.memoryUsage.slice(-100);
            }
            
            // 检查健康状态
            const health = this.checkHealth();
            if (health.status !== 'healthy') {
                this.logPerformance('WARN', `系统健康状态: ${health.status}`, health);
            }
            
        }, 60000); // 每分钟

        // 每10分钟保存一次指标
        setInterval(() => {
            this.saveMetrics();
        }, 600000); // 每10分钟

        console.log('🔍 性能监控已启动');
    }

    // 获取监控报告
    getMonitoringReport() {
        const stats = this.getPerformanceStats();
        const health = this.checkHealth();
        
        return {
            summary: {
                status: health.status,
                uptime: stats.system.system.uptime,
                totalRequests: stats.requests.total,
                errorRate: stats.requests.errorRate,
                avgResponseTime: stats.responseTime.average
            },
            performance: stats,
            health: health,
            timestamp: new Date().toISOString()
        };
    }

    // 重置统计数据
    resetStats() {
        this.metrics = {
            requests: 0,
            errors: 0,
            responseTime: [],
            memoryUsage: [],
            cpuUsage: [],
            activeConnections: 0
        };
        
        this.logPerformance('INFO', '性能统计数据已重置');
    }
}

module.exports = PerformanceMonitor;
