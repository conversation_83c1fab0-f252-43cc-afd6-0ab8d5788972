// 测试前端显示的脚本
const http = require('http');

let sessionToken = '';

// 先登录获取会话
function login(callback) {
    console.log('🔐 正在登录获取会话...');
    
    const loginData = JSON.stringify({
        username: 'admin',
        password: 'admin123'
    });
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(loginData)
        }
    };
    
    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                if (response.success && response.sessionId) {
                    sessionToken = response.sessionId;
                    console.log('✅ 登录成功，获取到会话令牌');
                    callback();
                } else {
                    console.log('❌ 登录失败:', response.message);
                }
            } catch (error) {
                console.log('❌ 解析登录响应失败:', error.message);
            }
        });
    });
    
    req.on('error', (error) => {
        console.log('❌ 登录请求失败:', error.message);
    });
    
    req.write(loginData);
    req.end();
}

// 创建一个新的测试操作记录
function createNewTestRecord() {
    console.log('\n🔧 创建新的测试操作记录...');
    
    const timestamp = new Date().toISOString();
    const description = `前端显示测试 - ${new Date().toLocaleString('zh-CN')}`;
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/operation-log',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${sessionToken}`
        }
    };
    
    const postData = JSON.stringify({
        type: '测试',
        description: description,
        user: 'admin',
        timestamp: timestamp
    });
    
    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                if (response.success) {
                    console.log(`✅ 创建测试记录成功: ${description}`);
                    console.log(`⏰ 时间戳: ${timestamp}`);
                    console.log('\n📢 请刷新浏览器页面，查看操作记录是否显示在最上面！');
                    console.log('🔍 如果还是显示在下面，请按 Ctrl+F5 强制刷新清除缓存');
                } else {
                    console.log('❌ 创建测试记录失败:', response.message);
                }
            } catch (error) {
                console.log('❌ 解析创建记录响应失败:', error.message);
            }
        });
    });
    
    req.on('error', (error) => {
        console.log('❌ 创建测试记录请求失败:', error.message);
    });
    
    req.write(postData);
    req.end();
}

// 运行测试
console.log('🚀 开始测试前端显示...');
login(() => {
    createNewTestRecord();
});
