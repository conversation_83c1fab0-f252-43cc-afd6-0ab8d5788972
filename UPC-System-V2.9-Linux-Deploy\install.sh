#!/bin/bash

# UPC管理系统 V2.9 Linux版 一键安装脚本
# 适用于 CentOS 8 系统
# 作者: UPC管理系统开发团队
# 版本: V2.9.0
# 日期: 2025-07-07

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo bash install.sh"
        exit 1
    fi
}

# 检查系统版本
check_system() {
    log_step "检查系统版本..."
    
    if [[ -f /etc/redhat-release ]]; then
        local version=$(cat /etc/redhat-release)
        log_info "检测到系统: $version"
        
        if [[ $version == *"CentOS"* ]] && [[ $version == *"8"* ]]; then
            log_info "✓ 系统版本兼容"
        else
            log_warn "警告: 此脚本专为CentOS 8设计，其他版本可能存在兼容性问题"
            read -p "是否继续安装? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                exit 1
            fi
        fi
    else
        log_error "不支持的操作系统"
        exit 1
    fi
}

# 安装Node.js
install_nodejs() {
    log_step "安装Node.js..."
    
    # 检查是否已安装Node.js
    if command -v node &> /dev/null; then
        local node_version=$(node --version)
        log_info "Node.js已安装: $node_version"
        
        # 检查版本是否满足要求 (>= 14.0.0)
        local major_version=$(echo $node_version | cut -d'.' -f1 | sed 's/v//')
        if [[ $major_version -ge 14 ]]; then
            log_info "✓ Node.js版本满足要求"
            return 0
        else
            log_warn "Node.js版本过低，需要升级"
        fi
    fi
    
    # 安装Node.js 18.x LTS
    log_info "正在安装Node.js 18.x LTS..."
    curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
    dnf install -y nodejs
    
    # 验证安装
    if command -v node &> /dev/null && command -v npm &> /dev/null; then
        log_info "✓ Node.js安装成功: $(node --version)"
        log_info "✓ npm版本: $(npm --version)"
    else
        log_error "Node.js安装失败"
        exit 1
    fi
}

# 安装系统依赖
install_dependencies() {
    log_step "安装系统依赖..."
    
    # 更新系统
    log_info "更新系统包..."
    dnf update -y
    
    # 安装基础工具
    log_info "安装基础工具..."
    dnf install -y curl wget git unzip zip tar gzip
    
    # 安装开发工具
    log_info "安装开发工具..."
    dnf groupinstall -y "Development Tools"
    
    # 安装Python3 (某些npm包需要)
    log_info "安装Python3..."
    dnf install -y python3 python3-pip
    
    log_info "✓ 系统依赖安装完成"
}

# 创建系统用户
create_user() {
    log_step "创建系统用户..."
    
    local username="upcadmin"
    local homedir="/home/<USER>"
    
    # 检查用户是否存在
    if id "$username" &>/dev/null; then
        log_info "用户 $username 已存在"
    else
        log_info "创建用户: $username"
        useradd -m -s /bin/bash "$username"
        log_info "✓ 用户创建成功"
    fi
    
    # 设置用户权限
    usermod -aG wheel "$username"
    log_info "✓ 用户权限设置完成"
    
    echo "export UPC_USER=$username" >> /etc/environment
    echo "export UPC_HOME=$homedir" >> /etc/environment
}

# 安装UPC系统
install_upc_system() {
    log_step "安装UPC管理系统..."
    
    local install_dir="/opt/upc-system"
    local service_user="upcadmin"
    
    # 创建安装目录
    log_info "创建安装目录: $install_dir"
    mkdir -p "$install_dir"
    
    # 复制系统文件
    log_info "复制系统文件..."
    cp -r ./* "$install_dir/"
    
    # 设置文件权限
    log_info "设置文件权限..."
    chown -R "$service_user:$service_user" "$install_dir"
    chmod +x "$install_dir"/*.sh
    
    # 安装npm依赖
    log_info "安装npm依赖..."
    cd "$install_dir"
    sudo -u "$service_user" npm install --production
    
    log_info "✓ UPC系统安装完成"
    
    echo "export UPC_INSTALL_DIR=$install_dir" >> /etc/environment
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙..."
    
    # 检查firewalld状态
    if systemctl is-active --quiet firewalld; then
        log_info "配置firewalld规则..."
        firewall-cmd --permanent --add-port=3000/tcp
        firewall-cmd --permanent --add-port=8080/tcp
        firewall-cmd --reload
        log_info "✓ 防火墙配置完成"
    else
        log_warn "firewalld未运行，跳过防火墙配置"
    fi
}

# 创建systemd服务
create_systemd_service() {
    log_step "创建systemd服务..."
    
    local service_file="/etc/systemd/system/upc-system.service"
    local install_dir="/opt/upc-system"
    local service_user="upcadmin"
    
    cat > "$service_file" << EOF
[Unit]
Description=UPC管理系统 V2.9 Linux版
Documentation=https://github.com/upc-system/upc-system
After=network.target

[Service]
Type=simple
User=$service_user
Group=$service_user
WorkingDirectory=$install_dir
ExecStart=/usr/bin/node simple-server.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=UPC_LOG_LEVEL=info
Environment=UPC_ENABLE_DEBUG=true
Environment=UPC_ENABLE_EMAIL=true
Environment=UPC_ENABLE_SMS=true
Environment=UPC_ENABLE_BACKUP=true
Environment=UPC_ENABLE_LOGGING=true

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=upc-system

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=$install_dir

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载systemd配置
    systemctl daemon-reload
    
    # 启用服务
    systemctl enable upc-system.service
    
    log_info "✓ systemd服务创建完成"
}

# 启动服务
start_services() {
    log_step "启动服务..."
    
    # 启动UPC系统服务
    log_info "启动UPC系统服务..."
    systemctl start upc-system.service
    
    # 检查服务状态
    sleep 3
    if systemctl is-active --quiet upc-system.service; then
        log_info "✓ UPC系统服务启动成功"
    else
        log_error "UPC系统服务启动失败"
        log_info "查看服务日志: journalctl -u upc-system.service -f"
        exit 1
    fi
}

# 验证安装
verify_installation() {
    log_step "验证安装..."
    
    local max_attempts=30
    local attempt=1
    
    log_info "等待服务完全启动..."
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -s http://localhost:3000/api/health > /dev/null 2>&1; then
            log_info "✓ 服务健康检查通过"
            break
        fi
        
        log_info "等待服务启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        log_error "服务启动超时"
        log_info "请检查服务日志: journalctl -u upc-system.service -f"
        exit 1
    fi
    
    # 显示系统信息
    log_info "✓ 安装验证完成"
}

# 显示安装完成信息
show_completion_info() {
    echo
    echo "=================================================================="
    echo -e "${GREEN}🎉 UPC管理系统 V2.9 Linux版 安装完成！${NC}"
    echo "=================================================================="
    echo
    echo -e "${BLUE}📋 系统信息:${NC}"
    echo "   版本: V2.9.0"
    echo "   安装目录: /opt/upc-system"
    echo "   服务用户: upcadmin"
    echo "   访问地址: http://$(hostname -I | awk '{print $1}'):3000"
    echo "   本地访问: http://localhost:3000"
    echo
    echo -e "${BLUE}👤 默认账户:${NC}"
    echo "   系统管理员: admin / admin123"
    echo "   业务经理: manager / Manager@2025"
    echo "   操作员: operator / Operator@2025"
    echo
    echo -e "${BLUE}🔧 服务管理:${NC}"
    echo "   启动服务: systemctl start upc-system"
    echo "   停止服务: systemctl stop upc-system"
    echo "   重启服务: systemctl restart upc-system"
    echo "   查看状态: systemctl status upc-system"
    echo "   查看日志: journalctl -u upc-system -f"
    echo
    echo -e "${BLUE}📊 功能状态:${NC}"
    echo "   ✓ 详细日志已启用"
    echo "   ✓ 错误堆栈已显示"
    echo "   ✓ 调试信息已开启"
    echo "   ✓ 邮件服务启用"
    echo "   ✓ 短信服务启用"
    echo "   ✓ 备份功能启用"
    echo "   ✓ 日志服务启用"
    echo
    echo -e "${YELLOW}📖 文档位置:${NC}"
    echo "   部署文档: /opt/upc-system/README-Linux.md"
    echo "   版本日志: /opt/upc-system/VERSION_LOG.md"
    echo
    echo "=================================================================="
}

# 主函数
main() {
    echo "=================================================================="
    echo -e "${BLUE}🚀 UPC管理系统 V2.9 Linux版 一键安装脚本${NC}"
    echo "=================================================================="
    echo
    
    check_root
    check_system
    install_dependencies
    install_nodejs
    create_user
    install_upc_system
    configure_firewall
    create_systemd_service
    start_services
    verify_installation
    show_completion_info
    
    echo -e "${GREEN}安装完成！请访问系统开始使用。${NC}"
}

# 执行主函数
main "$@"
