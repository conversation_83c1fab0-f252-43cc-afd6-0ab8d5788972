// 修复重复ID问题的脚本
const fs = require('fs');
const path = require('path');

// 文件路径
const UPC_CODES_FILE = path.join(__dirname, 'data', 'upc_codes.json');

function fixDuplicateIds() {
    console.log('🔧 开始修复重复ID问题...');
    
    try {
        // 读取UPC码数据
        const upcCodes = JSON.parse(fs.readFileSync(UPC_CODES_FILE, 'utf8'));
        console.log(`📊 总共 ${upcCodes.length} 个UPC码`);
        
        // 找到重复的ID
        const idCounts = {};
        upcCodes.forEach(code => {
            idCounts[code.id] = (idCounts[code.id] || 0) + 1;
        });
        
        const duplicateIds = Object.entries(idCounts).filter(([id, count]) => count > 1);
        console.log(`❌ 发现 ${duplicateIds.length} 个重复的ID`);
        
        if (duplicateIds.length === 0) {
            console.log('✅ 没有重复ID，无需修复');
            return;
        }
        
        // 获取当前最大的ID号码
        const numericIds = upcCodes
            .map(c => {
                if (typeof c.id === 'string' && c.id.startsWith('upc_')) {
                    return parseInt(c.id.replace('upc_', ''));
                }
                return 0;
            })
            .filter(id => !isNaN(id) && id > 0);
        
        let maxId = Math.max(...numericIds);
        console.log(`📋 当前最大ID号码: ${maxId}`);
        
        // 修复重复ID
        let fixedCount = 0;
        
        duplicateIds.forEach(([duplicateId, count]) => {
            console.log(`\n🔧 修复重复ID: ${duplicateId} (重复 ${count} 次)`);
            
            // 找到所有使用这个ID的UPC码
            const duplicateCodes = upcCodes.filter(code => code.id === duplicateId);
            
            // 保留第一个，修复其他的
            for (let i = 1; i < duplicateCodes.length; i++) {
                maxId++;
                const newId = `upc_${String(maxId).padStart(3, '0')}`;
                const oldId = duplicateCodes[i].id;
                duplicateCodes[i].id = newId;
                
                console.log(`   - UPC码 ${duplicateCodes[i].code}: ${oldId} -> ${newId}`);
                fixedCount++;
            }
        });
        
        // 备份原文件
        const backupFile = UPC_CODES_FILE + '.backup.' + Date.now();
        fs.copyFileSync(UPC_CODES_FILE, backupFile);
        console.log(`\n💾 原文件已备份到: ${backupFile}`);
        
        // 保存修复后的数据
        fs.writeFileSync(UPC_CODES_FILE, JSON.stringify(upcCodes, null, 2), 'utf8');
        console.log(`✅ 修复完成！共修复 ${fixedCount} 个重复ID`);
        
        // 验证修复结果
        const verifyData = JSON.parse(fs.readFileSync(UPC_CODES_FILE, 'utf8'));
        const verifyIdCounts = {};
        verifyData.forEach(code => {
            verifyIdCounts[code.id] = (verifyIdCounts[code.id] || 0) + 1;
        });
        
        const stillDuplicateIds = Object.entries(verifyIdCounts).filter(([id, count]) => count > 1);
        
        if (stillDuplicateIds.length === 0) {
            console.log('🎉 验证通过！所有ID现在都是唯一的');
        } else {
            console.log(`⚠️ 警告：仍有 ${stillDuplicateIds.length} 个重复ID`);
        }
        
        // 显示修复后的统计信息
        console.log(`\n📊 修复后统计:`);
        console.log(`   - 总UPC码数量: ${verifyData.length}`);
        console.log(`   - 唯一ID数量: ${Object.keys(verifyIdCounts).length}`);
        console.log(`   - 最大ID号码: ${Math.max(...verifyData.map(c => {
            if (typeof c.id === 'string' && c.id.startsWith('upc_')) {
                return parseInt(c.id.replace('upc_', ''));
            }
            return 0;
        }).filter(id => !isNaN(id) && id > 0))}`);
        
    } catch (error) {
        console.error('❌ 修复过程中发生错误:', error);
    }
}

// 运行修复
fixDuplicateIds();
