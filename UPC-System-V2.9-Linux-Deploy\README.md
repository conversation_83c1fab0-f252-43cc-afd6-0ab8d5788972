# UPC管理系统 V2.9 Linux版 部署包

## 📦 包内容

```
UPC-System-V2.9-Linux-Deploy/
├── install.sh                    # 一键安装脚本
├── uninstall.sh                  # 卸载脚本
├── simple-server.js              # 主服务器文件
├── package.json                  # 项目配置文件
├── data/                         # 数据目录
│   ├── users.json               # 用户数据
│   ├── upc_codes.json           # UPC码数据（已清空）
│   ├── applications.json        # 申请记录（已清空）
│   ├── recycle_records.json     # 回收记录（已清空）
│   ├── reports.json             # 报表数据（已清空）
│   ├── operation_logs.json      # 操作日志（已清空）
│   └── performance_metrics.json # 性能指标（已清空）
├── public/                       # 静态文件目录
│   ├── index.html               # 主页面
│   ├── login.html               # 登录页面
│   ├── admin.html               # 管理页面
│   ├── manager.html             # 经理页面
│   ├── operator.html            # 操作员页面
│   ├── css/                     # 样式文件
│   ├── js/                      # JavaScript文件
│   └── images/                  # 图片资源
├── logs/                         # 日志目录（空）
├── *.js                         # 其他服务模块
├── README.md                    # 本文件
├── README-Linux.md              # Linux部署说明
├── VERSION_LOG.md               # 版本更新日志
├── DEPLOYMENT_GUIDE.md          # 详细部署指南
└── MAINTENANCE_GUIDE.md         # 运维教程
```

## 🚀 快速开始

### 方法一：一键安装（推荐）

1. **上传部署包到服务器**
   ```bash
   # 使用scp上传（在本地执行）
   scp UPC-System-V2.9-Linux-Deploy.tar.gz root@************:/tmp/
   
   # 或使用其他方式上传到服务器
   ```

2. **在服务器上解压并安装**
   ```bash
   # 连接到服务器
   ssh root@************
   
   # 解压部署包
   cd /tmp
   tar -xzf UPC-System-V2.9-Linux-Deploy.tar.gz
   cd UPC-System-V2.9-Linux-Deploy
   
   # 设置脚本执行权限
   chmod +x *.sh
   
   # 执行一键安装
   ./install.sh
   ```

3. **等待安装完成**
   - 安装过程约需要5-10分钟
   - 脚本会自动安装所有依赖
   - 安装完成后显示访问信息

### 方法二：手动安装

详细步骤请参考 `DEPLOYMENT_GUIDE.md` 文档。

## 🔧 系统要求

- **操作系统**: CentOS 8 / RHEL 8 / Rocky Linux 8
- **CPU**: 2核心以上
- **内存**: 4GB RAM以上
- **存储**: 20GB可用空间
- **网络**: 稳定的互联网连接
- **权限**: root用户权限

## 📋 默认配置

### 服务配置
- **端口**: 3000
- **安装目录**: `/opt/upc-system`
- **服务用户**: `upcadmin`
- **服务名称**: `upc-system`

### 默认账户
| 角色 | 用户名 | 密码 | 权限 |
|------|--------|------|------|
| 系统管理员 | admin | admin123 | 系统管理、用户管理、备份恢复 |
| 业务经理 | manager | Manager@2025 | UPC管理、报表查看 |
| 操作员 | operator | Operator@2025 | UPC申请、回收操作 |

### 功能状态
安装完成后，以下功能将自动启用：
- ✅ 详细日志已启用
- ✅ 错误堆栈已显示
- ✅ 调试信息已开启
- ✅ 邮件服务启用
- ✅ 短信服务启用
- ✅ 备份功能启用
- ✅ 日志服务启用

## 🌐 访问系统

安装完成后，可通过以下方式访问：

- **外网访问**: `http://************:3000`
- **内网访问**: `http://localhost:3000`
- **健康检查**: `http://************:3000/api/health`

## 🔄 服务管理

```bash
# 启动服务
systemctl start upc-system

# 停止服务
systemctl stop upc-system

# 重启服务
systemctl restart upc-system

# 查看状态
systemctl status upc-system

# 查看日志
journalctl -u upc-system -f
```

## 📊 监控和维护

### 日志位置
- **应用日志**: `/opt/upc-system/logs/`
- **系统日志**: `journalctl -u upc-system`

### 数据备份
- **备份目录**: `/opt/upc-system/backup/`
- **自动备份**: 每日凌晨2点自动备份
- **手动备份**: `/opt/upc-system/scripts/backup.sh`

### 性能监控
- **监控脚本**: `/opt/upc-system/scripts/monitor.sh`
- **监控频率**: 每5分钟检查一次
- **告警阈值**: CPU>80%, 内存>85%, 磁盘>90%

## 🔒 安全配置

### 防火墙
安装脚本会自动配置防火墙规则：
```bash
# 查看防火墙状态
firewall-cmd --list-ports

# 手动开放端口（如需要）
firewall-cmd --permanent --add-port=3000/tcp
firewall-cmd --reload
```

### SSL/TLS（可选）
如需启用HTTPS，请参考 `DEPLOYMENT_GUIDE.md` 中的SSL配置章节。

## 🗑️ 卸载系统

如需完全卸载系统：

```bash
cd /opt/upc-system
./uninstall.sh
```

**注意**: 卸载会删除所有数据，请提前备份重要信息。

## 📖 文档说明

- **README-Linux.md**: 基础使用说明
- **DEPLOYMENT_GUIDE.md**: 详细部署指南（小白友好）
- **MAINTENANCE_GUIDE.md**: 运维教程和故障排除
- **VERSION_LOG.md**: 版本更新历史

## 🆘 故障排除

### 常见问题

1. **安装失败**
   ```bash
   # 检查系统版本
   cat /etc/redhat-release
   
   # 检查网络连接
   ping -c 3 *******
   
   # 查看安装日志
   tail -f /var/log/messages
   ```

2. **服务无法启动**
   ```bash
   # 查看服务状态
   systemctl status upc-system
   
   # 查看详细日志
   journalctl -u upc-system --no-pager
   
   # 检查端口占用
   ss -tlnp | grep 3000
   ```

3. **无法访问Web界面**
   ```bash
   # 检查服务监听
   ss -tlnp | grep 3000
   
   # 检查防火墙
   firewall-cmd --list-ports
   
   # 测试本地连接
   curl http://localhost:3000
   ```

### 获取帮助

如果遇到问题：
1. 查看相关文档
2. 检查日志文件
3. 确认系统要求
4. 联系技术支持

## 📞 技术支持

- **版本**: V2.9.0
- **发布日期**: 2025-07-07
- **支持系统**: CentOS 8 / RHEL 8 / Rocky Linux 8
- **最低要求**: Node.js 14+, 4GB RAM, 20GB存储

## 🔄 版本特性

### V2.9.0 新特性
- 🎨 全新响应式设计，完美适配移动端
- 🚀 优化系统性能，提升用户体验
- 🔧 完善的Linux部署方案
- 📊 增强的监控和日志功能
- 🔒 加强的安全配置
- 📖 详细的部署和运维文档
- 🛠️ 一键安装和卸载脚本

### 系统清理
- 所有UPC管理池数据已清空
- 操作记录和日志已重置
- 保留用户账户和系统配置
- 提供全新的使用环境

---

**🎉 感谢选择UPC管理系统 V2.9！**

开始您的UPC管理之旅，体验全新的响应式设计和强大功能！
