#!/bin/bash

# UPC管理系统 V3.0 卸载脚本
# 用于完全卸载系统和清理相关文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 系统配置
SYSTEM_NAME="UPC管理系统"
VERSION="V3.0"
INSTALL_DIR="/opt/upc-system"
SERVICE_NAME="upc-system"
SERVICE_USER="upc"
BACKUP_DIR="/tmp/upc-system-backup-$(date +%Y%m%d-%H%M%S)"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 显示警告信息
show_warning() {
    clear
    echo -e "${RED}"
    echo "=================================================================="
    echo "                    ⚠️  卸载警告  ⚠️"
    echo "=================================================================="
    echo -e "${NC}"
    echo ""
    echo -e "${YELLOW}此操作将完全卸载 $SYSTEM_NAME $VERSION${NC}"
    echo ""
    echo -e "${RED}⚠️  以下数据将被删除:${NC}"
    echo -e "   📁 系统文件: $INSTALL_DIR"
    echo -e "   👤 系统用户: $SERVICE_USER"
    echo -e "   🚀 系统服务: $SERVICE_NAME"
    echo -e "   🔧 防火墙规则: 端口3001"
    echo ""
    echo -e "${GREEN}✅ 数据备份将保存到: $BACKUP_DIR${NC}"
    echo ""
    echo -e "${YELLOW}请确保您真的要卸载系统！${NC}"
    echo ""
}

# 确认卸载
confirm_uninstall() {
    read -p "确定要卸载系统吗？(输入 'YES' 确认): " -r
    if [[ $REPLY != "YES" ]]; then
        log_info "卸载已取消"
        exit 0
    fi
    
    echo ""
    read -p "是否备份数据？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        BACKUP_DATA=true
    else
        BACKUP_DATA=false
    fi
}

# 检查权限
check_privileges() {
    log_step "检查权限..."
    
    if [[ $EUID -ne 0 ]]; then
        log_error "请以 root 用户运行此脚本"
        exit 1
    fi
    
    log_success "权限检查通过"
}

# 备份数据
backup_data() {
    if [[ $BACKUP_DATA == true ]]; then
        log_step "备份数据..."
        
        if [[ -d $INSTALL_DIR ]]; then
            mkdir -p $BACKUP_DIR
            
            # 备份数据文件
            if [[ -d "$INSTALL_DIR/data" ]]; then
                cp -r "$INSTALL_DIR/data" "$BACKUP_DIR/"
                log_success "数据文件已备份"
            fi
            
            # 备份配置文件
            if [[ -f "$INSTALL_DIR/package.json" ]]; then
                cp "$INSTALL_DIR/package.json" "$BACKUP_DIR/"
            fi
            
            # 备份日志文件
            if [[ -d "$INSTALL_DIR/logs" ]]; then
                cp -r "$INSTALL_DIR/logs" "$BACKUP_DIR/"
                log_success "日志文件已备份"
            fi
            
            log_success "数据备份完成: $BACKUP_DIR"
        else
            log_warning "安装目录不存在，跳过备份"
        fi
    else
        log_info "跳过数据备份"
    fi
}

# 停止服务
stop_service() {
    log_step "停止系统服务..."
    
    if systemctl is-active --quiet $SERVICE_NAME; then
        systemctl stop $SERVICE_NAME
        log_success "服务已停止"
    else
        log_info "服务未运行"
    fi
    
    if systemctl is-enabled --quiet $SERVICE_NAME; then
        systemctl disable $SERVICE_NAME
        log_success "服务已禁用"
    else
        log_info "服务未启用"
    fi
}

# 删除服务文件
remove_service() {
    log_step "删除系统服务..."
    
    if [[ -f "/etc/systemd/system/$SERVICE_NAME.service" ]]; then
        rm -f "/etc/systemd/system/$SERVICE_NAME.service"
        systemctl daemon-reload
        log_success "服务文件已删除"
    else
        log_info "服务文件不存在"
    fi
}

# 删除系统文件
remove_files() {
    log_step "删除系统文件..."
    
    if [[ -d $INSTALL_DIR ]]; then
        rm -rf $INSTALL_DIR
        log_success "系统文件已删除: $INSTALL_DIR"
    else
        log_info "安装目录不存在"
    fi
}

# 删除系统用户
remove_user() {
    log_step "删除系统用户..."
    
    if id "$SERVICE_USER" &>/dev/null; then
        userdel -r $SERVICE_USER 2>/dev/null || userdel $SERVICE_USER
        log_success "用户已删除: $SERVICE_USER"
    else
        log_info "用户不存在: $SERVICE_USER"
    fi
}

# 清理防火墙规则
cleanup_firewall() {
    log_step "清理防火墙规则..."
    
    if systemctl is-active --quiet firewalld; then
        if firewall-cmd --list-ports | grep -q "3001/tcp"; then
            firewall-cmd --permanent --remove-port=3001/tcp
            firewall-cmd --reload
            log_success "防火墙规则已清理"
        else
            log_info "防火墙规则不存在"
        fi
    else
        log_info "防火墙未运行"
    fi
}

# 清理临时文件
cleanup_temp() {
    log_step "清理临时文件..."
    
    # 清理日志文件
    if [[ -f "/var/log/$SERVICE_NAME.log" ]]; then
        rm -f "/var/log/$SERVICE_NAME.log"
        log_success "日志文件已清理"
    fi
    
    # 清理缓存文件
    if [[ -d "/tmp/upc-system-*" ]]; then
        rm -rf /tmp/upc-system-*
        log_success "缓存文件已清理"
    fi
    
    log_success "临时文件清理完成"
}

# 显示卸载结果
show_result() {
    clear
    echo -e "${CYAN}"
    echo "=================================================================="
    echo "           $SYSTEM_NAME $VERSION 卸载完成"
    echo "=================================================================="
    echo -e "${NC}"
    echo ""
    echo -e "${GREEN}✅ 卸载状态: 成功${NC}"
    echo -e "${GREEN}🗑️  已删除: 系统文件、服务、用户${NC}"
    echo -e "${GREEN}🧹 已清理: 防火墙规则、临时文件${NC}"
    
    if [[ $BACKUP_DATA == true ]]; then
        echo -e "${GREEN}💾 数据备份: $BACKUP_DIR${NC}"
        echo ""
        echo -e "${YELLOW}📋 备份内容:${NC}"
        if [[ -d $BACKUP_DIR ]]; then
            ls -la $BACKUP_DIR
        fi
    fi
    
    echo ""
    echo -e "${YELLOW}📞 技术支持: 深圳速拓电子商务有限公司${NC}"
    echo ""
    echo -e "${BLUE}如需重新安装，请运行安装脚本${NC}"
    echo ""
}

# 主卸载流程
main() {
    show_warning
    confirm_uninstall
    check_privileges
    backup_data
    stop_service
    remove_service
    remove_files
    remove_user
    cleanup_firewall
    cleanup_temp
    show_result
}

# 运行主函数
main "$@"
