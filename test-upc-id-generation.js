// 测试UPC码ID生成的脚本
const http = require('http');

// 测试添加UPC码功能
function testAddUPCCode() {
    console.log('🧪 开始测试UPC码ID自动生成功能...');
    
    // 测试数据 - 添加一个新的UPC码
    const testUPCCode = '999888777666'; // 使用一个不存在的UPC码
    
    // 构建请求数据
    const postData = JSON.stringify({
        code: testUPCCode,
        status: 'available'
    });
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc-codes',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData)
        }
    };
    
    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            console.log(`📊 响应状态码: ${res.statusCode}`);
            
            try {
                const response = JSON.parse(data);
                console.log(`📊 响应数据: ${JSON.stringify(response, null, 2)}`);
                
                if (res.statusCode === 201 && response.success) {
                    const newUPCCode = response.data;
                    console.log('✅ UPC码添加成功！');
                    console.log(`📋 新UPC码信息:`);
                    console.log(`   - ID: ${newUPCCode.id}`);
                    console.log(`   - 码: ${newUPCCode.code}`);
                    console.log(`   - 状态: ${newUPCCode.status}`);
                    
                    // 验证ID格式
                    if (typeof newUPCCode.id === 'string' && newUPCCode.id.startsWith('upc_')) {
                        console.log('✅ ID格式正确（字符串格式：upc_XXX）');
                    } else {
                        console.log(`❌ ID格式错误：${newUPCCode.id} (类型: ${typeof newUPCCode.id})`);
                    }
                    
                    // 测试导入功能
                    setTimeout(() => {
                        testImportUPCCodes();
                    }, 1000);
                    
                } else {
                    console.log('❌ UPC码添加失败！');
                    console.log(`错误信息: ${response.message || '未知错误'}`);
                }
            } catch (error) {
                console.log('❌ 解析响应数据失败:', error.message);
                console.log('原始响应:', data);
            }
        });
    });
    
    req.on('error', (error) => {
        console.log('❌ 请求失败:', error.message);
    });
    
    // 发送请求
    req.write(postData);
    req.end();
}

// 测试批量导入UPC码功能
function testImportUPCCodes() {
    console.log('\n🧪 开始测试UPC码批量导入功能...');
    
    // 测试数据 - 批量导入UPC码
    const testCodes = ['111222333444', '555666777888', '999000111222'];
    
    // 构建请求数据
    const postData = JSON.stringify({
        codes: testCodes
    });
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc/import',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData)
        }
    };
    
    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            console.log(`📊 响应状态码: ${res.statusCode}`);
            
            try {
                const response = JSON.parse(data);
                console.log(`📊 响应数据: ${JSON.stringify(response, null, 2)}`);
                
                if (res.statusCode === 200 && response.success) {
                    console.log('✅ UPC码批量导入成功！');
                    console.log(`📋 导入数量: ${response.importedCount}`);
                    
                    // 验证导入结果
                    setTimeout(() => {
                        verifyImportedCodes();
                    }, 1000);
                    
                } else {
                    console.log('❌ UPC码批量导入失败！');
                    console.log(`错误信息: ${response.message || '未知错误'}`);
                }
            } catch (error) {
                console.log('❌ 解析响应数据失败:', error.message);
                console.log('原始响应:', data);
            }
        });
    });
    
    req.on('error', (error) => {
        console.log('❌ 请求失败:', error.message);
    });
    
    // 发送请求
    req.write(postData);
    req.end();
}

// 验证导入的UPC码
function verifyImportedCodes() {
    console.log('\n🔍 验证导入的UPC码...');
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc-codes',
        method: 'GET'
    };
    
    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                
                if (response.success && response.data) {
                    const upcCodes = response.data;
                    console.log(`📊 当前UPC码总数: ${upcCodes.length}`);
                    
                    // 检查最新添加的UPC码
                    const recentCodes = upcCodes.slice(-10); // 最后10个
                    console.log('\n📋 最新添加的UPC码:');
                    recentCodes.forEach(code => {
                        console.log(`   - ID: ${code.id}, 码: ${code.code}, 状态: ${code.status}`);
                    });
                    
                    // 检查ID格式
                    const invalidIds = upcCodes.filter(code => 
                        !code.id || 
                        (typeof code.id !== 'string' || !code.id.startsWith('upc_'))
                    );
                    
                    if (invalidIds.length === 0) {
                        console.log('\n✅ 所有UPC码都有正确的ID格式！');
                    } else {
                        console.log(`\n❌ 发现 ${invalidIds.length} 个UPC码的ID格式不正确:`);
                        invalidIds.forEach(code => {
                            console.log(`   - 码: ${code.code}, ID: ${code.id} (类型: ${typeof code.id})`);
                        });
                    }
                    
                    console.log('\n🎉 测试完成！');
                }
            } catch (error) {
                console.log('❌ 解析响应数据失败:', error.message);
            }
        });
    });
    
    req.on('error', (error) => {
        console.log('❌ 请求失败:', error.message);
    });
    
    req.end();
}

// 运行测试
testAddUPCCode();
