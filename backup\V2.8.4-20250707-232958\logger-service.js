// UPC管理系统日志服务 V2.6.0
const fs = require('fs');
const path = require('path');

class LoggerService {
    constructor() {
        this.logDir = path.join(__dirname, 'logs');
        this.ensureLogDirectory();
        this.maxLogSize = 10 * 1024 * 1024; // 10MB
        this.maxLogFiles = 10;
    }

    // 确保日志目录存在
    ensureLogDirectory() {
        if (!fs.existsSync(this.logDir)) {
            fs.mkdirSync(this.logDir, { recursive: true });
        }
    }

    // 获取当前日期字符串
    getCurrentDateString() {
        const now = new Date();
        return now.toISOString().split('T')[0]; // YYYY-MM-DD
    }

    // 获取时间戳
    getTimestamp() {
        return new Date().toISOString();
    }

    // 写入日志文件
    writeToFile(level, category, message, data = null) {
        try {
            const timestamp = this.getTimestamp();
            const dateStr = this.getCurrentDateString();
            const logFile = path.join(this.logDir, `${dateStr}.log`);
            
            const logEntry = {
                timestamp,
                level,
                category,
                message,
                data
            };

            const logLine = JSON.stringify(logEntry) + '\n';
            
            // 检查文件大小并轮转
            this.rotateLogIfNeeded(logFile);
            
            fs.appendFileSync(logFile, logLine, 'utf8');
        } catch (error) {
            console.error('写入日志文件失败:', error.message);
        }
    }

    // 日志轮转
    rotateLogIfNeeded(logFile) {
        try {
            if (fs.existsSync(logFile)) {
                const stats = fs.statSync(logFile);
                if (stats.size > this.maxLogSize) {
                    const timestamp = Date.now();
                    const rotatedFile = logFile.replace('.log', `_${timestamp}.log`);
                    fs.renameSync(logFile, rotatedFile);
                    
                    // 清理旧日志文件
                    this.cleanupOldLogs();
                }
            }
        } catch (error) {
            console.error('日志轮转失败:', error.message);
        }
    }

    // 清理旧日志文件
    cleanupOldLogs() {
        try {
            const files = fs.readdirSync(this.logDir)
                .filter(file => file.endsWith('.log'))
                .map(file => ({
                    name: file,
                    path: path.join(this.logDir, file),
                    mtime: fs.statSync(path.join(this.logDir, file)).mtime
                }))
                .sort((a, b) => b.mtime - a.mtime);

            // 保留最新的文件，删除多余的
            if (files.length > this.maxLogFiles) {
                const filesToDelete = files.slice(this.maxLogFiles);
                filesToDelete.forEach(file => {
                    try {
                        fs.unlinkSync(file.path);
                        console.log(`已删除旧日志文件: ${file.name}`);
                    } catch (error) {
                        console.error(`删除日志文件失败: ${file.name}`, error.message);
                    }
                });
            }
        } catch (error) {
            console.error('清理旧日志失败:', error.message);
        }
    }

    // 通用日志方法
    info(message, category = 'SYSTEM', data = null) {
        console.log(`[INFO] [${category}] ${message}`);
        this.writeToFile('INFO', category, message, data);
    }

    warn(message, category = 'SYSTEM', data = null) {
        console.warn(`[WARN] [${category}] ${message}`);
        this.writeToFile('WARN', category, message, data);
    }

    error(message, category = 'SYSTEM', data = null) {
        console.error(`[ERROR] [${category}] ${message}`);
        this.writeToFile('ERROR', category, message, data);
    }

    debug(message, category = 'SYSTEM', data = null) {
        console.log(`[DEBUG] [${category}] ${message}`);
        this.writeToFile('DEBUG', category, message, data);
    }

    // 专用日志方法
    operation(action, details, user, data = null) {
        const message = `${user}: ${action} - ${details}`;
        console.log(`[OPERATION] ${message}`);
        this.writeToFile('OPERATION', 'USER_ACTION', message, { action, details, user, ...data });
    }

    loginLog(user, success, ip, details, data = null) {
        const status = success ? 'SUCCESS' : 'FAILED';
        const message = `${user} ${status} from ${ip} ${details}`;
        console.log(`[LOGIN] ${message}`);
        this.writeToFile('LOGIN', 'AUTH', message, { user, success, ip, details, ...data });
    }

    upcLog(action, code, user, details, data = null) {
        const message = `${action} ${code} by ${user} ${details}`;
        console.log(`[UPC] ${message}`);
        this.writeToFile('UPC', 'UPC_OPERATION', message, { action, code, user, details, ...data });
    }

    systemLog(event, details, data = null) {
        const message = `${event} ${details}`;
        console.log(`[SYSTEM] ${message}`);
        this.writeToFile('SYSTEM', 'SYSTEM_EVENT', message, { event, details, ...data });
    }

    // 获取日志统计
    getLogStats() {
        try {
            const files = fs.readdirSync(this.logDir)
                .filter(file => file.endsWith('.log'))
                .map(file => {
                    const filePath = path.join(this.logDir, file);
                    const stats = fs.statSync(filePath);
                    return {
                        name: file,
                        size: stats.size,
                        created: stats.birthtime,
                        modified: stats.mtime
                    };
                });

            return {
                totalFiles: files.length,
                totalSize: files.reduce((sum, file) => sum + file.size, 0),
                files: files.sort((a, b) => b.modified - a.modified)
            };
        } catch (error) {
            console.error('获取日志统计失败:', error.message);
            return { totalFiles: 0, totalSize: 0, files: [] };
        }
    }

    // 读取最近的日志
    getRecentLogs(lines = 100) {
        try {
            const dateStr = this.getCurrentDateString();
            const logFile = path.join(this.logDir, `${dateStr}.log`);

            if (!fs.existsSync(logFile)) {
                return [];
            }

            const content = fs.readFileSync(logFile, 'utf8');
            const logLines = content.trim().split('\n').filter(line => line.trim());

            return logLines
                .slice(-lines)
                .map(line => {
                    try {
                        return JSON.parse(line);
                    } catch (error) {
                        return { message: line, timestamp: new Date().toISOString(), level: 'UNKNOWN' };
                    }
                })
                .reverse();
        } catch (error) {
            console.error('读取日志失败:', error.message);
            return [];
        }
    }

    // 获取日志文件列表
    getLogFiles() {
        try {
            const files = fs.readdirSync(this.logDir)
                .filter(file => file.endsWith('.log'))
                .map(file => {
                    const filePath = path.join(this.logDir, file);
                    const stats = fs.statSync(filePath);
                    return {
                        name: file,
                        size: stats.size,
                        created: stats.birthtime,
                        modified: stats.mtime,
                        path: filePath
                    };
                })
                .sort((a, b) => b.modified - a.modified);

            return {
                totalFiles: files.length,
                totalSize: files.reduce((sum, file) => sum + file.size, 0),
                files: files
            };
        } catch (error) {
            console.error('获取日志文件列表失败:', error.message);
            return { totalFiles: 0, totalSize: 0, files: [] };
        }
    }

    // 读取日志文件内容
    readLogFile(fileName, maxLines = 100) {
        try {
            const filePath = path.join(this.logDir, fileName);

            // 检查文件是否存在
            if (!fs.existsSync(filePath)) {
                throw new Error(`日志文件不存在: ${fileName}`);
            }

            // 读取文件内容
            const content = fs.readFileSync(filePath, 'utf8');
            const lines = content.split('\n').filter(line => line.trim() !== '');

            // 返回最后N行
            const startIndex = Math.max(0, lines.length - maxLines);
            return lines.slice(startIndex);

        } catch (error) {
            console.error('读取日志文件失败:', error.message);
            throw error;
        }
    }

    // 检查日志服务状态
    isEnabled() {
        try {
            // 检查日志目录是否存在
            if (!fs.existsSync(this.logDir)) {
                return false;
            }

            // 检查是否能写入日志
            const testFile = path.join(this.logDir, 'test.log');
            fs.writeFileSync(testFile, 'test\n');
            fs.unlinkSync(testFile);

            return true;
        } catch (error) {
            console.error('日志服务状态检查失败:', error.message);
            return false;
        }
    }
}

// 创建单例实例
const logger = new LoggerService();

module.exports = logger;
