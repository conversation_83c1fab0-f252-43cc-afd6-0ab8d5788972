# UPC管理系统 V3.0 部署教程（小白版）

## 📋 目录
1. [系统要求](#系统要求)
2. [准备工作](#准备工作)
3. [环境检查](#环境检查)
4. [一键安装](#一键安装)
5. [手动安装](#手动安装)
6. [配置验证](#配置验证)
7. [常见问题](#常见问题)
8. [维护管理](#维护管理)

---

## 🖥️ 系统要求

### 推荐配置
- **操作系统**: CentOS 8 (推荐)
- **CPU**: 2核心以上
- **内存**: 2GB以上
- **磁盘**: 5GB以上可用空间
- **网络**: 稳定的互联网连接

### 最低配置
- **操作系统**: CentOS 7/8, RHEL 8, Rocky Linux 8
- **CPU**: 1核心
- **内存**: 1GB
- **磁盘**: 2GB可用空间
- **网络**: 互联网连接

---

## 🚀 准备工作

### 1. 获取服务器信息
确保您有以下信息：
- **服务器IP**: ************
- **用户名**: root
- **密码**: [您的root密码]

### 2. 连接到服务器
使用SSH工具连接到服务器：

#### Windows用户（推荐使用PuTTY）
1. 下载并安装 [PuTTY](https://www.putty.org/)
2. 打开PuTTY
3. 在"Host Name"中输入：`************`
4. 端口保持默认：`22`
5. 点击"Open"
6. 输入用户名：`root`
7. 输入密码

#### macOS/Linux用户
打开终端，执行：
```bash
ssh root@************
```

### 3. 上传部署包
将下载的部署包上传到服务器：

#### 方法1: 使用SCP（推荐）
在您的电脑上执行：
```bash
scp UPC-System-V3.0-CentOS8-Deploy.zip root@************:/tmp/
```

#### 方法2: 使用WinSCP（Windows用户）
1. 下载并安装 [WinSCP](https://winscp.net/)
2. 连接到服务器（IP: ************, 用户: root）
3. 将部署包拖拽到 `/tmp/` 目录

#### 方法3: 在服务器上直接操作
```bash
# 创建临时目录
mkdir -p /tmp/upc-deploy
cd /tmp/upc-deploy

# 如果您有直接下载链接，可以使用wget
# wget [您的下载链接]

# 解压部署包
unzip UPC-System-V3.0-CentOS8-Deploy.zip
cd UPC-System-V3.0-CentOS8-Deploy
```

---

## 🔍 环境检查

在安装前，建议先检查系统环境：

```bash
# 给检查脚本执行权限
chmod +x check-environment.sh

# 运行环境检查
./check-environment.sh
```

### 检查结果说明
- **✅ 通过检查**: 满足安装要求
- **⚠️ 警告项目**: 可以安装，但可能影响性能
- **❌ 失败检查**: 必须解决后才能安装

---

## 🎯 一键安装（推荐）

### 1. 运行安装脚本
```bash
# 给安装脚本执行权限
chmod +x install-centos8.sh

# 运行一键安装
./install-centos8.sh
```

### 2. 安装过程
安装脚本会自动完成以下步骤：
1. ✅ 检查系统要求
2. ✅ 更新系统软件包
3. ✅ 安装Node.js
4. ✅ 创建系统用户
5. ✅ 创建安装目录
6. ✅ 复制系统文件
7. ✅ 安装依赖包
8. ✅ 配置防火墙
9. ✅ 创建系统服务
10. ✅ 启动服务

### 3. 安装完成
看到以下信息表示安装成功：
```
==================================================================
           UPC管理系统 V3.0 安装完成
==================================================================

✅ 安装状态: 成功
📁 安装目录: /opt/upc-system
👤 运行用户: upc
🚀 服务名称: upc-system
🌐 访问地址: http://************:3001
```

---

## 🔧 手动安装（高级用户）

如果一键安装失败，可以尝试手动安装：

### 1. 更新系统
```bash
dnf update -y
dnf install -y wget curl unzip tar
```

### 2. 安装Node.js
```bash
# 添加NodeSource仓库
curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -

# 安装Node.js
dnf install -y nodejs

# 验证安装
node --version
npm --version
```

### 3. 创建用户和目录
```bash
# 创建系统用户
useradd -r -s /bin/false -d /opt/upc-system upc

# 创建目录
mkdir -p /opt/upc-system
mkdir -p /opt/upc-system/data
mkdir -p /opt/upc-system/logs
mkdir -p /opt/upc-system/backups

# 设置权限
chown -R upc:upc /opt/upc-system
```

### 4. 复制文件
```bash
# 复制所有文件
cp -r ./* /opt/upc-system/

# 设置权限
chown -R upc:upc /opt/upc-system
chmod +x /opt/upc-system/*.sh
```

### 5. 安装依赖
```bash
cd /opt/upc-system
sudo -u upc npm install --production
```

### 6. 配置服务
```bash
# 复制服务文件
cp upc-system.service /etc/systemd/system/

# 重新加载systemd
systemctl daemon-reload

# 启用并启动服务
systemctl enable upc-system
systemctl start upc-system
```

### 7. 配置防火墙
```bash
# 开放端口
firewall-cmd --permanent --add-port=3001/tcp
firewall-cmd --reload
```

---

## ✅ 配置验证

### 1. 检查服务状态
```bash
# 查看服务状态
systemctl status upc-system

# 查看服务日志
journalctl -u upc-system -f
```

### 2. 检查端口监听
```bash
# 检查端口是否监听
netstat -tuln | grep 3001
```

### 3. 测试网络访问
```bash
# 本地测试
curl http://localhost:3001

# 远程测试（在您的电脑上）
# 打开浏览器访问：http://************:3001
```

### 4. 登录系统
使用以下默认账户登录：
- **管理员**: admin / admin123
- **经理**: manager / Manager@2025
- **操作员**: operator / Operator@2025

### 5. 配置库存预警（重要）
安装完成后，建议立即配置库存预警设置：

#### 方法1: 使用管理工具（推荐）
```bash
cd /opt/upc-system
node manage-stock-alert.js
```

#### 方法2: 通过网页界面
1. 登录系统后，进入"系统设置"
2. 找到"库存预警设置"
3. 根据实际需求调整以下参数：
   - **预警阈值**: 建议设置为实际需要的最低库存数量
   - **预警频率**: 建议设置为"daily"（每天一次）
   - **通知方式**: 根据需要启用邮件和短信通知

#### 推荐配置
- **预警阈值**: 10-20个（根据业务需求）
- **预警频率**: daily（每天一次）
- **邮件通知**: 启用
- **短信通知**: 启用（可选）

### 5. 配置库存预警（重要）
安装完成后，建议立即配置库存预警设置：

#### 使用管理工具配置
```bash
cd /opt/upc-system
node manage-stock-alert.js
```

#### 推荐设置
- **预警阈值**: 根据实际需求设置（建议10-20个）
- **预警频率**: daily（每天一次）
- **通知方式**: 启用邮件和短信
- **接收人**: 设置管理员邮箱和手机号

#### 快速配置
在管理工具中选择"8. 快速修复（推荐设置）"即可应用合理的默认配置。

---

## ❓ 常见问题

### Q1: 安装失败，提示权限不足
**A**: 确保使用root用户运行安装脚本
```bash
# 切换到root用户
sudo su -

# 重新运行安装脚本
./install-centos8.sh
```

### Q2: 服务启动失败
**A**: 检查日志找出原因
```bash
# 查看详细日志
journalctl -u upc-system -n 50

# 检查端口占用
netstat -tuln | grep 3001

# 手动启动测试
cd /opt/upc-system
sudo -u upc node simple-server.js
```

### Q3: 无法访问网页
**A**: 检查防火墙和网络配置
```bash
# 检查防火墙状态
systemctl status firewalld

# 检查端口规则
firewall-cmd --list-ports

# 重新开放端口
firewall-cmd --permanent --add-port=3001/tcp
firewall-cmd --reload
```

### Q4: Node.js版本过低
**A**: 升级Node.js版本
```bash
# 卸载旧版本
dnf remove -y nodejs npm

# 重新安装最新版本
curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
dnf install -y nodejs
```

### Q5: 依赖安装失败
**A**: 清理缓存重新安装
```bash
cd /opt/upc-system

# 清理npm缓存
npm cache clean --force

# 删除node_modules
rm -rf node_modules

# 重新安装
sudo -u upc npm install --production
```

### Q6: 系统频繁发送预警通知
**A**: 调整库存预警配置
```bash
# 使用管理工具调整配置
cd /opt/upc-system
node manage-stock-alert.js

# 选择选项8应用推荐设置
# 或者手动调整预警阈值和频率
```

**常见原因**:
- 当前库存低于预警阈值
- 预警频率设置过高（如hourly）
- 建议设置合理的阈值和每日预警频率

### Q6: 系统频繁发送预警通知
**A**: 检查和调整库存预警配置
```bash
cd /opt/upc-system

# 使用管理工具检查配置
node manage-stock-alert.js

# 选择"8. 快速修复（推荐设置）"
# 或手动调整预警阈值和频率
```

**常见原因**:
- 当前库存低于预警阈值
- 预警频率设置过高（如hourly）
- 建议设置合理的阈值和daily频率

---

## 🛠️ 维护管理

### 服务管理命令
```bash
# 启动服务
systemctl start upc-system

# 停止服务
systemctl stop upc-system

# 重启服务
systemctl restart upc-system

# 查看状态
systemctl status upc-system

# 查看日志
journalctl -u upc-system -f
```

### 数据备份
```bash
# 手动备份
cd /opt/upc-system
tar -czf backup-$(date +%Y%m%d).tar.gz data/

# 查看备份
ls -la backup-*.tar.gz
```

### 库存预警管理
```bash
# 查看当前预警配置
cd /opt/upc-system
node manage-stock-alert.js

# 常用操作：
# 1. 查看当前配置
# 2. 启用/禁用预警
# 3. 设置预警阈值
# 4. 设置预警频率
# 8. 快速修复（推荐设置）
```

### 系统更新
```bash
# 停止服务
systemctl stop upc-system

# 备份数据
cp -r /opt/upc-system/data /tmp/upc-backup

# 更新系统文件
# （上传新版本文件到服务器）

# 重新安装依赖
cd /opt/upc-system
sudo -u upc npm install --production

# 启动服务
systemctl start upc-system
```

### 卸载系统
```bash
# 运行卸载脚本
./uninstall.sh

# 按提示操作，选择是否备份数据
```

---

## 📞 技术支持

如果遇到问题，请联系技术支持：

**深圳速拓电子商务有限公司**
- 📧 邮箱: <EMAIL>
- 🌐 网站: https://sutuo.net
- 📱 电话: [技术支持电话]

---

## 📝 更新日志

### V3.0 (2025-07-08)
- ✅ 修复操作记录排序问题
- ✅ 完善删除状态显示
- ✅ 优化回收记录管理
- ✅ 增强系统稳定性
- ✅ 完善部署脚本

---

**© 2025 深圳速拓电子商务有限公司 版权所有**
