// UPC管理系统备份服务 V2.6.0
const fs = require('fs');
const path = require('path');
const archiver = require('archiver');
const cron = require('node-cron');

class BackupService {
    constructor() {
        this.dataDir = path.join(__dirname, 'data');
        this.backupDir = path.join(this.dataDir, 'backups');
        this.maxBackups = 30; // 保留最近30个备份
        this.autoBackupEnabled = false;
        this.autoBackupSchedule = '0 2 * * *'; // 默认每天凌晨2点
        this.cronJob = null;
        
        this.ensureDirectories();
        this.loadConfig();
    }

    // 确保目录存在
    ensureDirectories() {
        if (!fs.existsSync(this.dataDir)) {
            fs.mkdirSync(this.dataDir, { recursive: true });
        }
        if (!fs.existsSync(this.backupDir)) {
            fs.mkdirSync(this.backupDir, { recursive: true });
        }
    }

    // 加载配置
    loadConfig() {
        try {
            const settingsFile = path.join(this.dataDir, 'system_settings.json');
            if (fs.existsSync(settingsFile)) {
                const settings = JSON.parse(fs.readFileSync(settingsFile, 'utf8'));
                const backupConfig = settings.backup || {};
                
                this.autoBackupEnabled = backupConfig.autoBackupEnabled || false;
                this.autoBackupSchedule = backupConfig.autoBackupSchedule || '0 2 * * *';
                this.maxBackups = backupConfig.maxBackups || 30;
                
                if (this.autoBackupEnabled) {
                    this.startAutoBackup();
                }
            }
        } catch (error) {
            console.log('备份配置加载失败，使用默认配置:', error.message);
        }
    }

    // 重新加载配置
    reloadConfig() {
        this.stopAutoBackup();
        this.loadConfig();
    }

    // 启动自动备份
    startAutoBackup() {
        if (this.cronJob) {
            this.cronJob.stop();
        }

        try {
            this.cronJob = cron.schedule(this.autoBackupSchedule, async () => {
                console.log('🕐 执行自动备份...');
                try {
                    await this.performBackup();
                    console.log('✅ 自动备份完成');
                } catch (error) {
                    console.error('❌ 自动备份失败:', error.message);
                }
            }, {
                scheduled: false,
                timezone: 'Asia/Shanghai'
            });

            this.cronJob.start();
            console.log(`📅 自动备份已启动，计划: ${this.autoBackupSchedule}`);
        } catch (error) {
            console.error('启动自动备份失败:', error.message);
        }
    }

    // 停止自动备份
    stopAutoBackup() {
        if (this.cronJob) {
            this.cronJob.stop();
            this.cronJob = null;
            console.log('⏹️ 自动备份已停止');
        }
    }

    // 执行备份
    async performBackup() {
        return new Promise((resolve, reject) => {
            try {
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0] + '_' + 
                                new Date().toTimeString().split(' ')[0].replace(/:/g, '');
                const backupFileName = `backup_${timestamp}.zip`;
                const backupPath = path.join(this.backupDir, backupFileName);

                const output = fs.createWriteStream(backupPath);
                const archive = archiver('zip', {
                    zlib: { level: 9 } // 最高压缩级别
                });

                output.on('close', () => {
                    const backupSize = archive.pointer();
                    console.log(`✅ 备份完成: ${backupFileName} (${(backupSize / 1024 / 1024).toFixed(2)} MB)`);
                    
                    // 清理旧备份
                    this.cleanupOldBackups();
                    
                    resolve({
                        fileName: backupFileName,
                        filePath: backupPath,
                        size: backupSize,
                        timestamp: new Date().toISOString()
                    });
                });

                output.on('error', (error) => {
                    console.error('备份文件写入失败:', error.message);
                    reject(error);
                });

                archive.on('error', (error) => {
                    console.error('备份压缩失败:', error.message);
                    reject(error);
                });

                archive.pipe(output);

                // 添加数据文件到备份
                const dataFiles = [
                    'users.json',
                    'system_settings.json',
                    'upc_codes.json',
                    'applications.json',
                    'recycle_records.json',
                    'reports.json'
                ];

                dataFiles.forEach(fileName => {
                    const filePath = path.join(this.dataDir, fileName);
                    if (fs.existsSync(filePath)) {
                        archive.file(filePath, { name: fileName });
                        console.log(`📁 添加到备份: ${fileName}`);
                    }
                });

                // 添加日志文件（如果存在）
                const logsDir = path.join(__dirname, 'logs');
                if (fs.existsSync(logsDir)) {
                    archive.directory(logsDir, 'logs');
                    console.log('📁 添加到备份: logs/');
                }

                archive.finalize();

            } catch (error) {
                console.error('备份过程失败:', error.message);
                reject(error);
            }
        });
    }

    // 清理旧备份
    cleanupOldBackups() {
        try {
            const backupFiles = fs.readdirSync(this.backupDir)
                .filter(file => file.startsWith('backup_') && file.endsWith('.zip'))
                .map(file => ({
                    name: file,
                    path: path.join(this.backupDir, file),
                    mtime: fs.statSync(path.join(this.backupDir, file)).mtime
                }))
                .sort((a, b) => b.mtime - a.mtime);

            if (backupFiles.length > this.maxBackups) {
                const filesToDelete = backupFiles.slice(this.maxBackups);
                filesToDelete.forEach(file => {
                    try {
                        fs.unlinkSync(file.path);
                        console.log(`🗑️ 删除旧备份: ${file.name}`);
                    } catch (error) {
                        console.error(`删除备份文件失败: ${file.name}`, error.message);
                    }
                });
            }
        } catch (error) {
            console.error('清理旧备份失败:', error.message);
        }
    }

    // 获取备份列表
    getBackupList() {
        try {
            const backupFiles = fs.readdirSync(this.backupDir)
                .filter(file => file.startsWith('backup_') && file.endsWith('.zip'))
                .map(file => {
                    const filePath = path.join(this.backupDir, file);
                    const stats = fs.statSync(filePath);
                    return {
                        name: file,
                        size: stats.size,
                        created: stats.birthtime,
                        modified: stats.mtime,
                        path: filePath
                    };
                })
                .sort((a, b) => b.modified - a.modified);

            return backupFiles;
        } catch (error) {
            console.error('获取备份列表失败:', error.message);
            return [];
        }
    }

    // 准备恢复备份
    async prepareRestore(backupFileName) {
        try {
            const backupPath = path.join(this.backupDir, backupFileName);
            
            if (!fs.existsSync(backupPath)) {
                throw new Error(`备份文件不存在: ${backupFileName}`);
            }

            const stats = fs.statSync(backupPath);
            
            return {
                backupFile: backupFileName,
                backupPath: backupPath,
                backupSize: stats.size,
                createTime: stats.birthtime,
                modifyTime: stats.mtime
            };
        } catch (error) {
            console.error('准备恢复备份失败:', error.message);
            throw error;
        }
    }

    // 获取备份统计
    getBackupStats() {
        try {
            const backupList = this.getBackupList();
            const totalSize = backupList.reduce((sum, backup) => sum + backup.size, 0);
            
            return {
                totalBackups: backupList.length,
                totalSize: totalSize,
                latestBackup: backupList.length > 0 ? backupList[0] : null,
                autoBackupEnabled: this.autoBackupEnabled,
                autoBackupSchedule: this.autoBackupSchedule,
                maxBackups: this.maxBackups
            };
        } catch (error) {
            console.error('获取备份统计失败:', error.message);
            return {
                totalBackups: 0,
                totalSize: 0,
                latestBackup: null,
                autoBackupEnabled: false,
                autoBackupSchedule: this.autoBackupSchedule,
                maxBackups: this.maxBackups
            };
        }
    }
}

module.exports = BackupService;
