// 测试批量激活和标记已用功能的修复
const http = require('http');

// 测试配置
const BASE_URL = 'http://localhost:3001';
let sessionCookie = '';

// 发送HTTP请求的辅助函数
function makeRequest(path, options = {}) {
    return new Promise((resolve, reject) => {
        const url = new URL(path, BASE_URL);
        const requestOptions = {
            hostname: url.hostname,
            port: url.port,
            path: url.pathname + url.search,
            method: options.method || 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Cookie': sessionCookie,
                ...options.headers
            }
        };

        const req = http.request(requestOptions, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                // 保存会话cookie
                if (res.headers['set-cookie']) {
                    sessionCookie = res.headers['set-cookie'].join('; ');
                }
                
                try {
                    const jsonData = JSON.parse(data);
                    resolve({ status: res.statusCode, data: jsonData });
                } catch (e) {
                    resolve({ status: res.statusCode, data: data });
                }
            });
        });

        req.on('error', reject);
        
        if (options.body) {
            req.write(options.body);
        }
        
        req.end();
    });
}

// 测试函数
async function runTests() {
    console.log('🧪 开始测试批量激活和标记已用功能的修复...\n');

    try {
        // 1. 登录
        console.log('1️⃣ 测试登录...');
        const loginResult = await makeRequest('/api/auth/login', {
            method: 'POST',
            body: JSON.stringify({
                username: 'admin',
                password: 'admin123'
            })
        });
        
        if (loginResult.data.success) {
            console.log('✅ 登录成功');
        } else {
            console.log('❌ 登录失败:', loginResult.data.message);
            return;
        }

        // 2. 申请一些UPC码用于测试
        console.log('\n2️⃣ 申请测试UPC码...');
        const requestResult = await makeRequest('/api/upc/request', {
            method: 'POST',
            body: JSON.stringify({
                quantity: 3,
                purpose: '测试批量激活和标记已用功能'
            })
        });
        
        if (requestResult.data.success) {
            console.log('✅ UPC码申请成功');
            console.log('🏷️ 分配的UPC码:', requestResult.data.upcCodes);
            
            const testUPCCodes = requestResult.data.upcCodes;
            
            // 3. 回收这些UPC码
            console.log('\n3️⃣ 回收UPC码...');
            for (let i = 0; i < testUPCCodes.length; i++) {
                const code = testUPCCodes[i];
                console.log(`♻️ 回收UPC码: ${code}`);
                
                const recycleResult = await makeRequest('/api/upc/recycle', {
                    method: 'POST',
                    body: JSON.stringify({
                        code: code,
                        reason: `测试回收 ${i + 1}`,
                        autoReactivateEnabled: false
                    })
                });
                
                if (recycleResult.data.success) {
                    console.log(`✅ UPC码 ${code} 回收成功`);
                } else {
                    console.log(`❌ UPC码 ${code} 回收失败:`, recycleResult.data.message);
                }
            }
            
            // 等待一下让数据更新
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 4. 测试批量重新激活
            console.log('\n4️⃣ 测试批量重新激活...');
            const reactivateResult = await makeRequest('/api/upc/reactivate', {
                method: 'POST',
                body: JSON.stringify({
                    upcCodes: testUPCCodes
                })
            });
            
            if (reactivateResult.data.success) {
                console.log('✅ 批量重新激活成功');
                console.log('📊 成功数量:', reactivateResult.data.successCount);
                console.log('📊 失败数量:', reactivateResult.data.errors?.length || 0);
                if (reactivateResult.data.errors && reactivateResult.data.errors.length > 0) {
                    console.log('❌ 错误信息:', reactivateResult.data.errors);
                }
            } else {
                console.log('❌ 批量重新激活失败:', reactivateResult.data.message);
            }
            
            // 5. 测试标记已用功能
            console.log('\n5️⃣ 测试标记已用功能...');
            
            // 获取UPC池信息
            const poolResult = await makeRequest('/api/upc/pool');
            if (poolResult.data.success) {
                // 找到一个可用的UPC码进行测试
                const availableUPC = poolResult.data.codes.find(c => 
                    testUPCCodes.includes(c.code) && c.status === 'available'
                );
                
                if (availableUPC) {
                    console.log(`🔍 找到可用UPC码进行测试: ${availableUPC.code} (ID: ${availableUPC.id})`);
                    
                    const markResult = await makeRequest('/api/upc/update', {
                        method: 'POST',
                        body: JSON.stringify({
                            upcId: availableUPC.id,
                            updates: {
                                status: 'invalid',
                                notes: '测试标记已用功能'
                            }
                        })
                    });
                    
                    if (markResult.data.success) {
                        console.log('✅ 标记已用功能测试成功');
                        console.log(`📋 UPC码 ${availableUPC.code} 已标记为已使用`);
                    } else {
                        console.log('❌ 标记已用功能测试失败:', markResult.data.message);
                    }
                } else {
                    console.log('⚠️ 没有找到可用的UPC码进行标记已用测试');
                }
            } else {
                console.log('❌ 获取UPC池信息失败:', poolResult.data.message);
            }
            
        } else {
            console.log('❌ UPC码申请失败:', requestResult.data.message);
        }

        console.log('\n🎉 测试完成！');

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error);
    }
}

// 运行测试
runTests();
