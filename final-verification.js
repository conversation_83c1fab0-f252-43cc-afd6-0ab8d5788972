// 最终验证UPC码ID生成修复的脚本
const fs = require('fs');
const path = require('path');

// 文件路径
const UPC_CODES_FILE = path.join(__dirname, 'data', 'upc_codes.json');

function finalVerification() {
    console.log('🔍 最终验证UPC码ID生成修复...');
    
    try {
        // 读取UPC码数据
        const upcCodes = JSON.parse(fs.readFileSync(UPC_CODES_FILE, 'utf8'));
        console.log(`📊 总共 ${upcCodes.length} 个UPC码`);
        
        // 1. 检查ID格式
        console.log('\n📋 1. 检查ID格式...');
        const invalidIds = upcCodes.filter(code => 
            !code.id || 
            (typeof code.id !== 'string' || !code.id.startsWith('upc_'))
        );
        
        if (invalidIds.length === 0) {
            console.log('✅ 所有UPC码都有正确的ID格式（upc_XXX）');
        } else {
            console.log(`❌ 发现 ${invalidIds.length} 个UPC码的ID格式不正确:`);
            invalidIds.forEach(code => {
                console.log(`   - 码: ${code.code}, ID: ${code.id} (类型: ${typeof code.id})`);
            });
        }
        
        // 2. 检查ID重复
        console.log('\n📋 2. 检查ID重复...');
        const idCounts = {};
        upcCodes.forEach(code => {
            idCounts[code.id] = (idCounts[code.id] || 0) + 1;
        });
        
        const duplicateIds = Object.entries(idCounts).filter(([id, count]) => count > 1);
        
        if (duplicateIds.length === 0) {
            console.log('✅ 所有UPC码的ID都是唯一的');
        } else {
            console.log(`❌ 发现 ${duplicateIds.length} 个重复的ID:`);
            duplicateIds.forEach(([id, count]) => {
                console.log(`   - ID: ${id} 重复 ${count} 次`);
                const duplicateCodes = upcCodes.filter(code => code.id === id);
                duplicateCodes.forEach(code => {
                    console.log(`     * 码: ${code.code}`);
                });
            });
        }
        
        // 3. 检查ID序列
        console.log('\n📋 3. 检查ID序列...');
        const numericIds = upcCodes
            .map(c => {
                if (typeof c.id === 'string' && c.id.startsWith('upc_')) {
                    return parseInt(c.id.replace('upc_', ''));
                }
                return 0;
            })
            .filter(id => !isNaN(id) && id > 0)
            .sort((a, b) => a - b);
        
        const uniqueNumericIds = [...new Set(numericIds)];
        console.log(`📊 ID范围: ${Math.min(...uniqueNumericIds)} - ${Math.max(...uniqueNumericIds)}`);
        console.log(`📊 唯一ID数量: ${uniqueNumericIds.length}`);
        
        // 检查序列连续性
        let gaps = [];
        for (let i = 1; i < uniqueNumericIds.length; i++) {
            if (uniqueNumericIds[i] - uniqueNumericIds[i-1] > 1) {
                gaps.push(`${uniqueNumericIds[i-1]} -> ${uniqueNumericIds[i]}`);
            }
        }
        
        if (gaps.length === 0) {
            console.log('✅ ID序列连续，没有跳号');
        } else {
            console.log(`⚠️ ID序列存在 ${gaps.length} 个跳号:`);
            gaps.forEach(gap => console.log(`   - ${gap}`));
        }
        
        // 4. 检查最新添加的UPC码
        console.log('\n📋 4. 检查最新添加的UPC码...');
        const recentCodes = upcCodes.slice(-10);
        console.log('最新的10个UPC码:');
        recentCodes.forEach(code => {
            console.log(`   - ID: ${code.id}, 码: ${code.code}, 状态: ${code.status}`);
        });
        
        // 5. 验证测试添加的UPC码
        console.log('\n📋 5. 验证测试添加的UPC码...');
        const testCodes = [
            'TEST001111111', 'TEST002222222', 'TEST003333333', 
            'TEST004444444', 'TEST005555555', 'SINGLE123456789'
        ];
        
        const foundTestCodes = upcCodes.filter(code => testCodes.includes(code.code));
        console.log(`📊 找到测试UPC码: ${foundTestCodes.length}/${testCodes.length}`);
        
        foundTestCodes.forEach(code => {
            console.log(`   - ID: ${code.id}, 码: ${code.code}`);
        });
        
        // 6. 总结
        console.log('\n📊 6. 修复效果总结...');
        const totalCodes = upcCodes.length;
        const validIdCodes = upcCodes.filter(code => 
            code.id && typeof code.id === 'string' && code.id.startsWith('upc_')
        ).length;
        const uniqueIdCodes = uniqueNumericIds.length;
        
        console.log(`📋 总UPC码数量: ${totalCodes}`);
        console.log(`📋 有效ID数量: ${validIdCodes}`);
        console.log(`📋 唯一ID数量: ${uniqueIdCodes}`);
        
        if (validIdCodes === totalCodes && uniqueIdCodes === validIdCodes) {
            console.log('\n🎉 修复完全成功！所有UPC码都有唯一且正确的ID！');
        } else {
            console.log('\n⚠️ 修复部分成功，仍有问题需要解决');
        }
        
    } catch (error) {
        console.error('❌ 验证过程中发生错误:', error);
    }
}

// 运行验证
finalVerification();
