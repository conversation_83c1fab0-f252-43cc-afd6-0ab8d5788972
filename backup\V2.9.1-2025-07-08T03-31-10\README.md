# UPC管理系统 V2.9.1

## 📋 版本信息
- **版本**: V2.9.1
- **构建时间**: 2025/7/8 11:31:10
- **构建时间戳**: 2025-07-08T03-31-10

## 🔧 最新修复
- ✅ UPC码ID自动生成修复
- ✅ UPC管理池批量操作修复  
- ✅ 回收管理页面位置保持修复
- ✅ 数据完整性检查和修复
- ✅ 性能监控和优化

## 🚀 部署说明

### 快速部署
```bash
# 1. 解压文件
unzip UPC-System-V2.9.1-*.zip
cd UPC-System-V2.9.1-*

# 2. 安装依赖
npm install

# 3. 启动服务
npm start
# 或
node simple-server.js
```

### Linux服务部署
```bash
# 1. 运行安装脚本
chmod +x install.sh
sudo ./install.sh

# 2. 启动服务
sudo systemctl start upc-system
sudo systemctl enable upc-system
```

## 📊 系统要求
- Node.js >= 14.0.0
- NPM >= 6.0.0
- 内存 >= 512MB
- 磁盘空间 >= 1GB

## 🔗 访问地址
- 系统地址: http://localhost:3001
- 默认账户: admin / admin123

## 📞 技术支持
如有问题请联系系统管理员。
