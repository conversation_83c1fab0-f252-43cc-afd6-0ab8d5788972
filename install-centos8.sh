#!/bin/bash

# UPC管理系统 V3.0 CentOS 8 一键安装脚本
# 适用于 CentOS 8 系统
# 作者: 深圳速拓电子商务有限公司
# 版本: V3.0

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 系统信息
SYSTEM_NAME="UPC管理系统"
VERSION="V3.0"
COMPANY="深圳速拓电子商务有限公司"
INSTALL_DIR="/opt/upc-system"
SERVICE_NAME="upc-system"
SERVICE_USER="upc"
DEFAULT_PORT="3001"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    clear
    echo -e "${CYAN}"
    echo "=================================================================="
    echo "           $SYSTEM_NAME $VERSION 一键安装脚本"
    echo "=================================================================="
    echo -e "${NC}"
    echo -e "${GREEN}🏢 公司: $COMPANY${NC}"
    echo -e "${GREEN}🚀 版本: $VERSION${NC}"
    echo -e "${GREEN}🐧 系统: CentOS 8${NC}"
    echo -e "${GREEN}📅 日期: $(date '+%Y-%m-%d %H:%M:%S')${NC}"
    echo ""
    echo -e "${YELLOW}⚠️  请确保以 root 用户运行此脚本${NC}"
    echo -e "${YELLOW}⚠️  安装过程需要网络连接${NC}"
    echo ""
}

# 检查系统要求
check_system() {
    log_step "检查系统要求..."
    
    # 检查是否为 root 用户
    if [[ $EUID -ne 0 ]]; then
        log_error "请以 root 用户运行此脚本"
        exit 1
    fi
    
    # 检查系统版本
    if ! grep -q "CentOS Linux 8" /etc/os-release 2>/dev/null; then
        log_warning "此脚本专为 CentOS 8 设计，其他系统可能存在兼容性问题"
        read -p "是否继续安装？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "安装已取消"
            exit 0
        fi
    fi
    
    # 检查网络连接
    if ! ping -c 1 ******* &> /dev/null; then
        log_error "网络连接失败，请检查网络设置"
        exit 1
    fi
    
    log_success "系统检查通过"
}

# 更新系统
update_system() {
    log_step "更新系统软件包..."
    
    # 更新 yum 缓存
    dnf clean all
    dnf makecache
    
    # 更新系统
    dnf update -y
    
    # 安装基础工具
    dnf install -y wget curl unzip tar gzip
    
    log_success "系统更新完成"
}

# 安装 Node.js
install_nodejs() {
    log_step "安装 Node.js..."
    
    # 检查是否已安装 Node.js
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        log_info "Node.js 已安装: $NODE_VERSION"
        
        # 检查版本是否满足要求
        MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$MAJOR_VERSION" -ge 14 ]; then
            log_success "Node.js 版本满足要求"
            return
        else
            log_warning "Node.js 版本过低，需要升级"
        fi
    fi
    
    # 安装 NodeSource 仓库
    curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
    
    # 安装 Node.js
    dnf install -y nodejs
    
    # 验证安装
    if command -v node &> /dev/null && command -v npm &> /dev/null; then
        log_success "Node.js 安装成功: $(node --version)"
        log_success "npm 版本: $(npm --version)"
    else
        log_error "Node.js 安装失败"
        exit 1
    fi
}

# 创建系统用户
create_user() {
    log_step "创建系统用户..."
    
    # 检查用户是否已存在
    if id "$SERVICE_USER" &>/dev/null; then
        log_info "用户 $SERVICE_USER 已存在"
    else
        # 创建系统用户
        useradd -r -s /bin/false -d $INSTALL_DIR $SERVICE_USER
        log_success "用户 $SERVICE_USER 创建成功"
    fi
}

# 创建安装目录
create_directories() {
    log_step "创建安装目录..."
    
    # 创建主目录
    mkdir -p $INSTALL_DIR
    mkdir -p $INSTALL_DIR/data
    mkdir -p $INSTALL_DIR/logs
    mkdir -p $INSTALL_DIR/backups
    
    # 设置权限
    chown -R $SERVICE_USER:$SERVICE_USER $INSTALL_DIR
    chmod -R 755 $INSTALL_DIR
    
    log_success "目录创建完成: $INSTALL_DIR"
}

# 复制系统文件
copy_files() {
    log_step "复制系统文件..."
    
    # 复制所有文件到安装目录
    cp -r ./* $INSTALL_DIR/
    
    # 设置权限
    chown -R $SERVICE_USER:$SERVICE_USER $INSTALL_DIR
    chmod +x $INSTALL_DIR/*.sh
    
    log_success "文件复制完成"
}

# 安装依赖
install_dependencies() {
    log_step "安装 Node.js 依赖..."
    
    cd $INSTALL_DIR
    
    # 安装生产依赖
    sudo -u $SERVICE_USER npm install --production
    
    log_success "依赖安装完成"
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙..."
    
    # 检查防火墙状态
    if systemctl is-active --quiet firewalld; then
        # 开放端口
        firewall-cmd --permanent --add-port=$DEFAULT_PORT/tcp
        firewall-cmd --reload
        log_success "防火墙配置完成，已开放端口 $DEFAULT_PORT"
    else
        log_warning "防火墙未运行，请手动配置端口访问"
    fi
}

# 创建系统服务
create_service() {
    log_step "创建系统服务..."
    
    cat > /etc/systemd/system/$SERVICE_NAME.service << EOF
[Unit]
Description=$SYSTEM_NAME $VERSION
Documentation=https://sutuo.net
After=network.target

[Service]
Type=simple
User=$SERVICE_USER
WorkingDirectory=$INSTALL_DIR
ExecStart=/usr/bin/node simple-server.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=$DEFAULT_PORT

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=$INSTALL_DIR

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载 systemd
    systemctl daemon-reload
    
    # 启用服务
    systemctl enable $SERVICE_NAME
    
    log_success "系统服务创建完成"
}

# 启动服务
start_service() {
    log_step "启动服务..."
    
    # 启动服务
    systemctl start $SERVICE_NAME
    
    # 检查服务状态
    sleep 3
    if systemctl is-active --quiet $SERVICE_NAME; then
        log_success "服务启动成功"
    else
        log_error "服务启动失败"
        systemctl status $SERVICE_NAME
        exit 1
    fi
}

# 显示安装结果
show_result() {
    clear
    echo -e "${CYAN}"
    echo "=================================================================="
    echo "           $SYSTEM_NAME $VERSION 安装完成"
    echo "=================================================================="
    echo -e "${NC}"
    echo ""
    echo -e "${GREEN}✅ 安装状态: 成功${NC}"
    echo -e "${GREEN}📁 安装目录: $INSTALL_DIR${NC}"
    echo -e "${GREEN}👤 运行用户: $SERVICE_USER${NC}"
    echo -e "${GREEN}🚀 服务名称: $SERVICE_NAME${NC}"
    echo -e "${GREEN}🌐 访问地址: http://$(hostname -I | awk '{print $1}'):$DEFAULT_PORT${NC}"
    echo ""
    echo -e "${YELLOW}📋 管理命令:${NC}"
    echo -e "  启动服务: ${CYAN}systemctl start $SERVICE_NAME${NC}"
    echo -e "  停止服务: ${CYAN}systemctl stop $SERVICE_NAME${NC}"
    echo -e "  重启服务: ${CYAN}systemctl restart $SERVICE_NAME${NC}"
    echo -e "  查看状态: ${CYAN}systemctl status $SERVICE_NAME${NC}"
    echo -e "  查看日志: ${CYAN}journalctl -u $SERVICE_NAME -f${NC}"
    echo ""
    echo -e "${YELLOW}🔑 默认账户:${NC}"
    echo -e "  管理员: ${CYAN}admin / admin123${NC}"
    echo -e "  经理: ${CYAN}manager / Manager@2025${NC}"
    echo -e "  操作员: ${CYAN}operator / Operator@2025${NC}"
    echo ""
    echo -e "${YELLOW}📞 技术支持: $COMPANY${NC}"
    echo ""
}

# 主安装流程
main() {
    show_welcome
    
    # 确认安装
    read -p "是否开始安装？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "安装已取消"
        exit 0
    fi
    
    # 执行安装步骤
    check_system
    update_system
    install_nodejs
    create_user
    create_directories
    copy_files
    install_dependencies
    configure_firewall
    create_service
    start_service
    
    # 显示结果
    show_result
}

# 运行主函数
main "$@"
