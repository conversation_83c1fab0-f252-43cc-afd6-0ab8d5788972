#!/bin/bash

# UPC管理系统 V2.8.1 Linux启动脚本
# 适用于CentOS 8, Ubuntu 18.04+, RHEL 8+

echo "========================================"
echo "    UPC管理系统 V2.8.1 Linux版"
echo "        Linux服务器启动脚本"
echo "========================================"
echo

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ 错误：Node.js未安装！"
    echo "请先运行安装脚本：./install.sh"
    exit 1
fi

# 检查Node.js版本
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 14 ]; then
    echo "❌ 错误：Node.js版本过低！"
    echo "当前版本：$(node --version)"
    echo "需要版本：>= 14.0.0"
    exit 1
fi

# 显示Node.js版本
echo "✅ Node.js版本：$(node --version)"
echo

# 检查依赖包
echo "🔍 检查依赖包..."
if [ ! -f "package.json" ]; then
    echo "❌ 错误：package.json文件不存在！"
    exit 1
fi

# 检查node_modules
if [ ! -d "node_modules" ]; then
    echo "📦 依赖包未安装，正在安装..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖包安装失败！"
        exit 1
    fi
fi

# 检查关键依赖
MISSING_DEPS=()
if ! npm list nodemailer &>/dev/null; then
    MISSING_DEPS+=("nodemailer")
fi
if ! npm list node-cron &>/dev/null; then
    MISSING_DEPS+=("node-cron")
fi

if [ ${#MISSING_DEPS[@]} -gt 0 ]; then
    echo "📦 安装缺失的依赖包: ${MISSING_DEPS[*]}"
    npm install "${MISSING_DEPS[@]}"
fi

echo "✅ 依赖包检查完成"
echo

# 检查端口3001是否被占用
if netstat -tuln 2>/dev/null | grep -q ":3001 "; then
    echo "⚠️  警告：端口3001已被占用！"
    echo "正在尝试停止占用进程..."
    pkill -f "node.*simple-server.js" 2>/dev/null || true
    sleep 2
    
    # 再次检查
    if netstat -tuln 2>/dev/null | grep -q ":3001 "; then
        echo "❌ 端口3001仍被占用，请手动处理"
        echo "查看占用进程：lsof -i :3001"
        exit 1
    fi
fi

# 运行系统初始化（如果需要）
if [ ! -d "data" ] || [ ! -f "data/system_settings.json" ]; then
    echo "⚙️  运行系统初始化..."
    if [ -f "setup.js" ]; then
        node setup.js
    else
        mkdir -p data logs backup
        echo "✅ 创建基础目录"
    fi
fi

# 设置环境变量
export NODE_ENV=production
export PORT=3001

# 创建必要的目录
mkdir -p logs backup

# 设置文件权限
chmod 644 *.js *.json 2>/dev/null || true
chmod 755 *.sh 2>/dev/null || true

# 检查防火墙
if command -v firewall-cmd &> /dev/null; then
    if systemctl is-active --quiet firewalld; then
        if ! firewall-cmd --list-ports | grep -q "3001/tcp"; then
            echo "⚠️  端口3001未在防火墙中开放"
            echo "💡 运行以下命令开放端口："
            echo "   firewall-cmd --permanent --add-port=3001/tcp"
            echo "   firewall-cmd --reload"
        fi
    fi
fi

echo "🚀 正在启动UPC管理系统..."
echo "📍 外网访问：http://*************:3001"
echo "📍 本地访问：http://localhost:3001"
echo "⚠️  按Ctrl+C停止服务"
echo

# 显示启动信息
echo "🎯 系统信息："
echo "   版本：V2.8.1 Linux版"
echo "   环境：$NODE_ENV"
echo "   端口：$PORT"
echo "   进程ID：$$"
echo

echo "👤 默认账户："
echo "   管理员：admin / admin123"
echo "   业务经理：manager / Manager@2025"
echo "   操作员：operator / Operator@2025"
echo

echo "⚙️  服务状态："
echo "   📧 邮件服务：已启用 (支持多种邮箱)"
echo "   📱 短信服务：已启用 (腾讯云SMS)"
echo "   📋 日志服务：已启用 (完整日志记录)"
echo "   💾 备份服务：已启用 (自动备份)"
echo

# 启动服务器
node simple-server.js
