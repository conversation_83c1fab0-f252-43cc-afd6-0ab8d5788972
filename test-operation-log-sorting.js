// 测试操作记录时间排序的脚本
const http = require('http');

let sessionToken = '';

// 先登录获取会话
function login(callback) {
    console.log('🔐 正在登录获取会话...');
    
    const loginData = JSON.stringify({
        username: 'admin',
        password: 'admin123'
    });
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(loginData)
        }
    };
    
    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                if (response.success && response.sessionId) {
                    sessionToken = response.sessionId;
                    console.log('✅ 登录成功，获取到会话令牌');
                    callback();
                } else {
                    console.log('❌ 登录失败:', response.message);
                }
            } catch (error) {
                console.log('❌ 解析登录响应失败:', error.message);
            }
        });
    });
    
    req.on('error', (error) => {
        console.log('❌ 登录请求失败:', error.message);
    });
    
    req.write(loginData);
    req.end();
}

// 测试操作记录排序
function testOperationLogSorting() {
    console.log('\n🧪 测试操作记录时间排序...');
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/operation-log',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${sessionToken}`
        }
    };
    
    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                if (response.success && response.data) {
                    const logs = response.data;
                    console.log(`📊 获取到 ${logs.length} 条操作记录`);
                    
                    if (logs.length === 0) {
                        console.log('⚠️ 没有操作记录');
                        return;
                    }
                    
                    console.log('\n📋 操作记录列表（按API返回顺序）:');
                    logs.forEach((log, index) => {
                        const timestamp = log.timestamp || log.created_at || '无时间戳';
                        const time = new Date(timestamp);
                        const timeStr = isNaN(time.getTime()) ? '无效时间' : time.toLocaleString('zh-CN');
                        
                        console.log(`   ${index + 1}. [${timeStr}] ${log.type || '未知类型'} - ${log.description || '无描述'}`);
                    });
                    
                    // 检查时间排序
                    console.log('\n🔍 检查时间排序...');
                    let isSortedCorrectly = true;
                    let previousTime = null;
                    
                    for (let i = 0; i < logs.length; i++) {
                        const log = logs[i];
                        const timestamp = log.timestamp || log.created_at;
                        
                        if (!timestamp) {
                            console.log(`⚠️ 记录 ${i + 1} 没有时间戳`);
                            continue;
                        }
                        
                        const currentTime = new Date(timestamp).getTime();
                        
                        if (isNaN(currentTime)) {
                            console.log(`⚠️ 记录 ${i + 1} 时间戳无效: ${timestamp}`);
                            continue;
                        }
                        
                        if (previousTime !== null && currentTime > previousTime) {
                            console.log(`❌ 排序错误: 记录 ${i + 1} (${new Date(currentTime).toLocaleString('zh-CN')}) 比前一条记录 (${new Date(previousTime).toLocaleString('zh-CN')}) 更新，但排在后面`);
                            isSortedCorrectly = false;
                        }
                        
                        previousTime = currentTime;
                    }
                    
                    if (isSortedCorrectly) {
                        console.log('✅ 操作记录时间排序正确：最新的在前面');
                    } else {
                        console.log('❌ 操作记录时间排序错误：需要修复');
                    }
                    
                    // 显示最新的5条记录
                    console.log('\n📋 最新的5条操作记录:');
                    const recentLogs = logs.slice(0, 5);
                    recentLogs.forEach((log, index) => {
                        const timestamp = log.timestamp || log.created_at || '无时间戳';
                        const time = new Date(timestamp);
                        const timeStr = isNaN(time.getTime()) ? '无效时间' : time.toLocaleString('zh-CN');
                        
                        console.log(`   ${index + 1}. [${timeStr}] ${log.type || '未知类型'} - ${log.description || '无描述'}`);
                    });
                    
                    console.log('\n🎉 操作记录排序测试完成！');
                    
                } else {
                    console.log('❌ 获取操作记录失败:', response.message);
                }
            } catch (error) {
                console.log('❌ 解析操作记录响应失败:', error.message);
            }
        });
    });
    
    req.on('error', (error) => {
        console.log('❌ 获取操作记录请求失败:', error.message);
    });
    
    req.end();
}

// 创建一些测试操作记录
function createTestOperationLogs(callback) {
    console.log('\n🔧 创建测试操作记录...');
    
    const testLogs = [
        { type: '测试', description: '测试操作记录排序 - 第1条' },
        { type: '测试', description: '测试操作记录排序 - 第2条' },
        { type: '测试', description: '测试操作记录排序 - 第3条' }
    ];
    
    let completedCount = 0;
    
    testLogs.forEach((logData, index) => {
        setTimeout(() => {
            const options = {
                hostname: 'localhost',
                port: 3001,
                path: '/api/operation-log',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${sessionToken}`
                }
            };
            
            const postData = JSON.stringify({
                type: logData.type,
                description: logData.description,
                user: 'admin',
                timestamp: new Date().toISOString()
            });
            
            const req = http.request(options, (res) => {
                let data = '';
                
                res.on('data', (chunk) => {
                    data += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const response = JSON.parse(data);
                        if (response.success) {
                            console.log(`✅ 创建测试记录 ${index + 1}: ${logData.description}`);
                        } else {
                            console.log(`❌ 创建测试记录 ${index + 1} 失败:`, response.message);
                        }
                    } catch (error) {
                        console.log(`❌ 解析创建记录 ${index + 1} 响应失败:`, error.message);
                    }
                    
                    completedCount++;
                    if (completedCount === testLogs.length) {
                        console.log('✅ 所有测试记录创建完成');
                        setTimeout(callback, 1000); // 等待1秒后继续
                    }
                });
            });
            
            req.on('error', (error) => {
                console.log(`❌ 创建测试记录 ${index + 1} 请求失败:`, error.message);
                completedCount++;
                if (completedCount === testLogs.length) {
                    callback();
                }
            });
            
            req.write(postData);
            req.end();
        }, index * 500); // 每条记录间隔500ms
    });
}

// 运行测试
console.log('🚀 开始测试操作记录时间排序...');
login(() => {
    createTestOperationLogs(() => {
        testOperationLogSorting();
    });
});
