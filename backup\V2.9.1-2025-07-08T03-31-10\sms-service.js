// UPC管理系统短信服务 V2.6.0
const tencentcloud = require('tencentcloud-sdk-nodejs');
const fs = require('fs');
const path = require('path');

class SMSService {
    constructor() {
        this.client = null;
        this.config = null;
        this.loadConfig();
    }

    // 加载短信配置
    loadConfig() {
        try {
            const settingsFile = path.join(__dirname, 'data', 'system_settings.json');
            if (fs.existsSync(settingsFile)) {
                const settings = JSON.parse(fs.readFileSync(settingsFile, 'utf8'));
                this.config = settings.notification?.sms || {};
                
                if (this.config.enabled) {
                    this.createClient();
                }
            } else {
                console.log('短信配置文件不存在，使用默认配置');
                this.config = {
                    enabled: false,
                    secretId: '',
                    secretKey: '',
                    region: 'ap-beijing',
                    appId: '',
                    signName: '',
                    templateId: ''
                };
            }
        } catch (error) {
            console.error('加载短信配置失败:', error.message);
            this.config = {
                enabled: false,
                secretId: '',
                secretKey: '',
                region: 'ap-beijing',
                appId: '',
                signName: '',
                templateId: ''
            };
        }
    }

    // 重新加载配置
    reloadConfig() {
        this.loadConfig();
    }

    // 创建腾讯云短信客户端
    createClient() {
        try {
            // 支持两种配置字段名
            const secretId = this.config.smsAccessKey || this.config.secretId;
            const secretKey = this.config.smsAccessSecret || this.config.secretKey;

            if (!this.config.enabled || !secretId || !secretKey) {
                console.log('短信服务未启用或配置不完整');
                return;
            }

            const SmsClient = tencentcloud.sms.v20210111.Client;
            const clientConfig = {
                credential: {
                    secretId: secretId,
                    secretKey: secretKey,
                },
                region: this.config.region || 'ap-beijing',
                profile: {
                    httpProfile: {
                        endpoint: 'sms.tencentcloudapi.com',
                    },
                },
            };

            this.client = new SmsClient(clientConfig);
            console.log('📱 短信客户端创建成功');
        } catch (error) {
            console.error('创建短信客户端失败:', error.message);
            this.client = null;
        }
    }

    // 验证短信配置
    async verifyConfig() {
        try {
            if (!this.client) {
                return {
                    success: false,
                    message: '短信客户端未初始化'
                };
            }

            // 这里可以添加配置验证逻辑
            // 由于腾讯云SMS没有直接的验证接口，我们检查必要的配置项
            const requiredFields = ['secretId', 'secretKey', 'appId', 'signName'];
            const missingFields = requiredFields.filter(field => !this.config[field]);
            
            if (missingFields.length > 0) {
                return {
                    success: false,
                    message: `缺少必要配置: ${missingFields.join(', ')}`
                };
            }

            return {
                success: true,
                message: '短信配置验证成功'
            };
        } catch (error) {
            console.error('短信配置验证失败:', error.message);
            return {
                success: false,
                message: `短信配置验证失败: ${error.message}`
            };
        }
    }

    // 发送短信
    async sendSMS(phoneNumber, message, templateParams = []) {
        try {
            if (!this.config.enabled) {
                throw new Error('短信服务未启用');
            }

            if (!this.client) {
                this.createClient();
                if (!this.client) {
                    throw new Error('短信客户端创建失败');
                }
            }

            // 格式化手机号
            const formattedPhone = this.formatPhoneNumber(phoneNumber);
            
            const params = {
                PhoneNumberSet: [formattedPhone],
                SmsSdkAppId: this.config.smsAppId || this.config.appId,
                SignName: this.config.smsSignature || this.config.signName,
                TemplateId: this.config.smsTemplateId || this.config.templateId,
                TemplateParamSet: templateParams
            };

            console.log(`📱 发送短信到: ${phoneNumber}, 模板参数:`, templateParams);
            
            const response = await this.client.SendSms(params);
            
            if (response.SendStatusSet && response.SendStatusSet[0]) {
                const status = response.SendStatusSet[0];
                if (status.Code === 'Ok') {
                    console.log(`✅ 短信发送成功: ${status.SerialNo}`);
                    return {
                        success: true,
                        serialNo: status.SerialNo,
                        fee: status.Fee
                    };
                } else {
                    console.error(`❌ 短信发送失败: ${status.Code} - ${status.Message}`);
                    return {
                        success: false,
                        error: `${status.Code}: ${status.Message}`
                    };
                }
            } else {
                throw new Error('短信发送响应格式异常');
            }
        } catch (error) {
            console.error('短信发送失败:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // 发送测试短信
    async sendTestSMS(phoneNumber) {
        try {
            // 使用预设的测试模板参数
            const templateParams = [
                'UPC管理系统',
                new Date().toLocaleString('zh-CN'),
                'V2.6.0'
            ];

            return await this.sendSMS(phoneNumber, '测试短信', templateParams);
        } catch (error) {
            console.error('发送测试短信失败:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // 发送库存预警短信
    async sendStockAlert(phoneNumber, alertData) {
        try {
            const templateParams = [
                alertData.currentStock.toString(),
                alertData.threshold.toString(),
                new Date(alertData.alertTime).toLocaleDateString('zh-CN')
            ];

            return await this.sendSMS(phoneNumber, '库存预警', templateParams);
        } catch (error) {
            console.error('发送库存预警短信失败:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // 格式化手机号
    formatPhoneNumber(phoneNumber) {
        // 移除所有非数字字符
        let cleaned = phoneNumber.replace(/\D/g, '');
        
        // 如果是11位数字且以1开头，添加+86前缀
        if (cleaned.length === 11 && cleaned.startsWith('1')) {
            return '+86' + cleaned;
        }
        
        // 如果已经有+86前缀，直接返回
        if (phoneNumber.startsWith('+86')) {
            return phoneNumber;
        }
        
        // 其他情况，假设是中国手机号
        return '+86' + cleaned;
    }

    // 验证手机号格式
    validatePhoneNumber(phoneNumber) {
        const cleaned = phoneNumber.replace(/\D/g, '');
        
        // 中国手机号验证：11位数字，以1开头
        const chinaPattern = /^1[3-9]\d{9}$/;
        
        return chinaPattern.test(cleaned);
    }

    // 批量发送短信
    async sendBatchSMS(phoneNumbers, message, templateParams = []) {
        const results = [];
        
        for (const phoneNumber of phoneNumbers) {
            try {
                const result = await this.sendSMS(phoneNumber, message, templateParams);
                results.push({
                    phoneNumber,
                    ...result
                });
                
                // 添加延迟以避免频率限制
                await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (error) {
                results.push({
                    phoneNumber,
                    success: false,
                    error: error.message
                });
            }
        }
        
        return results;
    }

    // 获取短信服务状态
    getServiceStatus() {
        return {
            enabled: this.config?.enabled || false,
            configured: !!(this.config?.secretId && this.config?.secretKey && this.config?.appId),
            clientReady: !!this.client,
            config: {
                region: this.config?.region || 'ap-beijing',
                appId: this.config?.appId || '',
                signName: this.config?.signName || '',
                templateId: this.config?.templateId || ''
            }
        };
    }

    // 获取短信发送统计（模拟数据，实际需要调用腾讯云API）
    async getSMSStats() {
        try {
            // 这里可以调用腾讯云的统计API
            // 目前返回模拟数据
            return {
                todaySent: 0,
                monthSent: 0,
                totalSent: 0,
                successRate: 100,
                lastSentTime: null
            };
        } catch (error) {
            console.error('获取短信统计失败:', error.message);
            return {
                todaySent: 0,
                monthSent: 0,
                totalSent: 0,
                successRate: 0,
                lastSentTime: null
            };
        }
    }
}

module.exports = SMSService;
