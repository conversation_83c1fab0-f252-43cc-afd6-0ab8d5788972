// 测试申请历史状态映射修复的脚本
const http = require('http');

let sessionToken = '';

// 先登录获取会话
function login(callback) {
    console.log('🔐 正在登录获取会话...');
    
    const loginData = JSON.stringify({
        username: 'admin',
        password: 'admin123'
    });
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(loginData)
        }
    };
    
    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                if (response.success && response.sessionId) {
                    sessionToken = response.sessionId;
                    console.log('✅ 登录成功，获取到会话令牌');
                    callback();
                } else {
                    console.log('❌ 登录失败:', response.message);
                }
            } catch (error) {
                console.log('❌ 解析登录响应失败:', error.message);
            }
        });
    });
    
    req.on('error', (error) => {
        console.log('❌ 登录请求失败:', error.message);
    });
    
    req.write(loginData);
    req.end();
}

// 测试申请历史状态映射
function testApplicationStatusMapping() {
    console.log('\n🧪 测试申请历史状态映射修复...');
    
    // 获取申请历史
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/applications',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${sessionToken}`
        }
    };
    
    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                
                if (response.success && response.data) {
                    const applications = response.data;
                    console.log(`📊 获取到 ${applications.length} 个申请记录`);
                    
                    // 分析申请记录中的状态
                    console.log('\n📋 申请记录状态分析:');
                    
                    const statusCounts = {};
                    applications.forEach(app => {
                        const status = app.status || '未知';
                        statusCounts[status] = (statusCounts[status] || 0) + 1;
                    });
                    
                    console.log('📊 状态统计:');
                    Object.entries(statusCounts).forEach(([status, count]) => {
                        console.log(`   - ${status}: ${count} 个`);
                    });
                    
                    // 检查是否有无效状态的申请记录
                    const invalidApplications = applications.filter(app => 
                        app.status === 'invalid' || 
                        (app.allocated_upcs && app.allocated_upcs.some(upc => upc.status === 'invalid'))
                    );
                    
                    if (invalidApplications.length > 0) {
                        console.log('\n⚠️ 发现包含无效状态的申请记录:');
                        invalidApplications.forEach(app => {
                            console.log(`📋 申请ID: ${app.id}`);
                            console.log(`   申请状态: ${app.status}`);
                            console.log(`   申请时间: ${app.created_at}`);
                            console.log(`   申请用户: ${app.user_id}`);
                            
                            if (app.allocated_upcs) {
                                console.log(`   分配的UPC码:`);
                                app.allocated_upcs.forEach(upc => {
                                    console.log(`     - ${upc.code}: ${upc.status}`);
                                });
                            }
                        });
                    } else {
                        console.log('\n✅ 没有发现包含无效状态的申请记录');
                    }
                    
                    // 获取UPC池数据进行对比
                    getUPCPoolData(applications);
                    
                } else {
                    console.log('❌ 获取申请历史失败:', response.message || '未知错误');
                }
            } catch (error) {
                console.log('❌ 解析申请历史响应失败:', error.message);
                console.log('原始响应:', data);
            }
        });
    });
    
    req.on('error', (error) => {
        console.log('❌ 获取申请历史请求失败:', error.message);
    });
    
    req.end();
}

// 获取UPC池数据进行对比
function getUPCPoolData(applications) {
    console.log('\n🔍 获取UPC池数据进行状态对比...');
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc-codes',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${sessionToken}`
        }
    };
    
    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                
                if (response.success && response.data) {
                    const upcCodes = response.data;
                    console.log(`📊 获取到 ${upcCodes.length} 个UPC码`);
                    
                    // 统计UPC池中的状态
                    const poolStatusCounts = {};
                    upcCodes.forEach(upc => {
                        const status = upc.status || '未知';
                        poolStatusCounts[status] = (poolStatusCounts[status] || 0) + 1;
                    });
                    
                    console.log('\n📊 UPC池状态统计:');
                    Object.entries(poolStatusCounts).forEach(([status, count]) => {
                        console.log(`   - ${status}: ${count} 个`);
                    });
                    
                    // 查找invalid状态的UPC码
                    const invalidUPCs = upcCodes.filter(upc => upc.status === 'invalid');
                    
                    if (invalidUPCs.length > 0) {
                        console.log(`\n🔍 发现 ${invalidUPCs.length} 个invalid状态的UPC码:`);
                        invalidUPCs.forEach(upc => {
                            console.log(`   - ${upc.code} (ID: ${upc.id}): ${upc.status}`);
                        });
                        
                        // 检查这些invalid UPC码在申请历史中的状态映射
                        console.log('\n🔍 检查invalid UPC码在申请历史中的状态映射:');
                        
                        applications.forEach(app => {
                            if (app.allocated_upcs) {
                                app.allocated_upcs.forEach(allocatedUPC => {
                                    const poolUPC = invalidUPCs.find(upc => upc.code === allocatedUPC.code);
                                    if (poolUPC) {
                                        console.log(`📋 申请记录 ${app.id}:`);
                                        console.log(`   UPC码: ${allocatedUPC.code}`);
                                        console.log(`   申请历史中状态: ${allocatedUPC.status || '未设置'}`);
                                        console.log(`   UPC池中状态: ${poolUPC.status}`);
                                        
                                        if (allocatedUPC.status === 'invalid') {
                                            console.log('   ✅ 状态映射正确 (invalid -> invalid)');
                                        } else if (allocatedUPC.status === 'completed') {
                                            console.log('   ❌ 状态映射错误 (invalid -> completed)');
                                        } else {
                                            console.log(`   ⚠️ 状态映射异常 (invalid -> ${allocatedUPC.status})`);
                                        }
                                    }
                                });
                            }
                        });
                    } else {
                        console.log('\n✅ UPC池中没有invalid状态的UPC码');
                    }
                    
                    console.log('\n🎉 申请历史状态映射检查完成！');
                    
                } else {
                    console.log('❌ 获取UPC池数据失败:', response.message || '未知错误');
                }
            } catch (error) {
                console.log('❌ 解析UPC池响应失败:', error.message);
            }
        });
    });
    
    req.on('error', (error) => {
        console.log('❌ 获取UPC池请求失败:', error.message);
    });
    
    req.end();
}

// 运行测试
console.log('🚀 开始测试申请历史状态映射修复...');
login(() => {
    testApplicationStatusMapping();
});
