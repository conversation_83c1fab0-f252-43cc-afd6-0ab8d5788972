// UPC管理系统邮件服务 V2.6.0
const nodemailer = require('nodemailer');
const fs = require('fs');
const path = require('path');

class EmailService {
    constructor() {
        this.transporter = null;
        this.config = null;
        this.loadConfig();
    }

    // 加载邮件配置
    loadConfig() {
        try {
            const settingsFile = path.join(__dirname, 'data', 'system_settings.json');
            if (fs.existsSync(settingsFile)) {
                const settings = JSON.parse(fs.readFileSync(settingsFile, 'utf8'));
                this.config = settings.notification?.email || {};
                
                if (this.config.enabled) {
                    this.createTransporter();
                }
            } else {
                console.log('邮件配置文件不存在，使用默认配置');
                this.config = {
                    enabled: false,
                    smtpServer: '',
                    smtpPort: 587,
                    senderEmail: '',
                    emailPassword: '',
                    senderName: 'UPC管理系统',
                    enableSSL: true
                };
            }
        } catch (error) {
            console.error('加载邮件配置失败:', error.message);
            this.config = {
                enabled: false,
                smtpServer: '',
                smtpPort: 587,
                senderEmail: '',
                emailPassword: '',
                senderName: 'UPC管理系统',
                enableSSL: true
            };
        }
    }

    // 重新加载配置
    reloadConfig() {
        this.loadConfig();
    }

    // 获取邮件服务商预设配置
    getProviderConfig(provider, smtpServer) {
        const configs = {
            'qq': {
                host: 'smtp.qq.com',
                port: 465,
                secure: true,
                requireTLS: false,
                tls: { rejectUnauthorized: false }
            },
            '163': {
                host: 'smtp.163.com',
                port: 25,
                secure: false,
                requireTLS: false,
                tls: { rejectUnauthorized: false }
            },
            'gmail': {
                host: 'smtp.gmail.com',
                port: 587,
                secure: false,
                requireTLS: true,
                tls: { rejectUnauthorized: false }
            },
            'outlook': {
                host: 'smtp-mail.outlook.com',
                port: 587,
                secure: false,
                requireTLS: true,
                tls: { rejectUnauthorized: false }
            },
            'aliyun': {
                host: 'smtpdm.aliyun.com',
                port: 80,
                secure: false,
                requireTLS: false,
                tls: { rejectUnauthorized: false }
            },
            'sendgrid': {
                host: 'smtp.sendgrid.net',
                port: 587,
                secure: false,
                requireTLS: true,
                tls: { rejectUnauthorized: false }
            }
        };

        // 如果有预设配置，使用预设
        if (configs[provider]) {
            return configs[provider];
        }

        // 根据SMTP服务器自动识别
        if (smtpServer) {
            if (smtpServer.includes('qq.com')) return configs.qq;
            if (smtpServer.includes('163.com')) return configs['163'];
            if (smtpServer.includes('gmail.com')) return configs.gmail;
            if (smtpServer.includes('outlook.com')) return configs.outlook;
            if (smtpServer.includes('aliyun.com')) return configs.aliyun;
            if (smtpServer.includes('sendgrid.net')) return configs.sendgrid;
        }

        // 默认配置
        return {
            host: smtpServer,
            port: 587,
            secure: false,
            requireTLS: true,
            tls: { rejectUnauthorized: false }
        };
    }

    // 创建邮件传输器
    createTransporter() {
        try {
            if (!this.config.enabled || !this.config.smtpServer || !this.config.senderEmail) {
                console.log('邮件服务未启用或配置不完整');
                return;
            }

            const port = parseInt(this.config.smtpPort) || 587;
            const provider = this.config.provider || 'custom';

            console.log('📧 邮件配置信息:', {
                provider: provider,
                host: this.config.smtpServer,
                port: port,
                user: this.config.senderEmail,
                hasPassword: !!this.config.emailPassword,
                security: this.config.smtpSecurity
            });

            // 获取服务商预设配置
            const providerConfig = this.getProviderConfig(provider, this.config.smtpServer);

            // 构建传输器配置
            const transportConfig = {
                host: this.config.smtpServer,
                port: port,
                secure: providerConfig.secure,
                auth: {
                    user: this.config.senderEmail,
                    pass: this.config.emailPassword
                },
                tls: providerConfig.tls,
                requireTLS: providerConfig.requireTLS,
                connectionTimeout: 60000,
                greetingTimeout: 30000,
                socketTimeout: 60000
            };

            // 特殊处理163邮箱
            if (provider === '163' || this.config.smtpServer.includes('163.com')) {
                transportConfig.port = 25;
                transportConfig.secure = false;
                transportConfig.ignoreTLS = true;
                transportConfig.tls = {
                    rejectUnauthorized: false,
                    secureProtocol: 'TLSv1_method'
                };
            }

            console.log('📧 最终传输器配置:', {
                host: transportConfig.host,
                port: transportConfig.port,
                secure: transportConfig.secure,
                requireTLS: transportConfig.requireTLS
            });

            this.transporter = nodemailer.createTransport(transportConfig);
            console.log('📧 邮件传输器创建成功');

        } catch (error) {
            console.error('创建邮件传输器失败:', error.message);
            this.transporter = null;
        }
    }

    // 验证邮件配置
    async verifyConfig() {
        try {
            if (!this.transporter) {
                return {
                    success: false,
                    message: '邮件传输器未初始化'
                };
            }

            await this.transporter.verify();
            return {
                success: true,
                message: '邮件配置验证成功'
            };
        } catch (error) {
            console.error('邮件配置验证失败:', error.message);
            return {
                success: false,
                message: `邮件配置验证失败: ${error.message}`
            };
        }
    }

    // 发送邮件
    async sendEmail(to, subject, content, isHtml = false) {
        try {
            if (!this.config.enabled) {
                throw new Error('邮件服务未启用');
            }

            if (!this.transporter) {
                this.createTransporter();
                if (!this.transporter) {
                    throw new Error('邮件传输器创建失败');
                }
            }

            const mailOptions = {
                from: `"${this.config.senderName}" <${this.config.senderEmail}>`,
                to: to,
                subject: subject,
                [isHtml ? 'html' : 'text']: content
            };

            console.log(`📧 发送邮件到: ${to}, 主题: ${subject}`);
            
            const info = await this.transporter.sendMail(mailOptions);
            
            console.log(`✅ 邮件发送成功: ${info.messageId}`);
            
            return {
                success: true,
                messageId: info.messageId,
                response: info.response
            };
        } catch (error) {
            console.error('邮件发送失败:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // 发送测试邮件
    async sendTestEmail(to) {
        try {
            const subject = '【UPC管理系统】邮件服务测试';
            const content = `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">
                        📧 UPC管理系统邮件服务测试
                    </h2>
                    
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                        <p style="margin: 0; color: #666;">
                            <strong>测试时间:</strong> ${new Date().toLocaleString('zh-CN')}
                        </p>
                        <p style="margin: 10px 0 0 0; color: #666;">
                            <strong>系统版本:</strong> V2.6.0
                        </p>
                    </div>
                    
                    <p style="color: #333; line-height: 1.6;">
                        恭喜！您的邮件服务配置正确，系统可以正常发送邮件通知。
                    </p>
                    
                    <div style="background: #e7f3ff; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0;">
                        <p style="margin: 0; color: #0056b3;">
                            <strong>📋 邮件服务功能:</strong>
                        </p>
                        <ul style="color: #0056b3; margin: 10px 0 0 20px;">
                            <li>库存预警通知</li>
                            <li>用户操作通知</li>
                            <li>系统状态报告</li>
                            <li>定期数据报表</li>
                        </ul>
                    </div>
                    
                    <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                    
                    <p style="color: #999; font-size: 12px; text-align: center;">
                        此邮件由 UPC管理系统 V2.6.0 自动发送，请勿回复。<br>
                        如有问题，请联系系统管理员。
                    </p>
                </div>
            `;

            return await this.sendEmail(to, subject, content, true);
        } catch (error) {
            console.error('发送测试邮件失败:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // 发送库存预警邮件
    async sendStockAlert(to, alertData) {
        try {
            const subject = '【UPC管理系统】库存预警通知';
            const content = `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2 style="color: #dc3545; border-bottom: 2px solid #dc3545; padding-bottom: 10px;">
                        🚨 库存预警通知
                    </h2>
                    
                    <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 5px; margin: 20px 0;">
                        <h3 style="color: #856404; margin-top: 0;">预警详情</h3>
                        <p style="margin: 5px 0; color: #856404;">
                            <strong>当前库存:</strong> ${alertData.currentStock} 个
                        </p>
                        <p style="margin: 5px 0; color: #856404;">
                            <strong>预警阈值:</strong> ${alertData.threshold} 个
                        </p>
                        <p style="margin: 5px 0; color: #856404;">
                            <strong>缺货率:</strong> ${alertData.shortageRate}%
                        </p>
                        <p style="margin: 5px 0; color: #856404;">
                            <strong>预警时间:</strong> ${new Date(alertData.alertTime).toLocaleString('zh-CN')}
                        </p>
                    </div>
                    
                    <div style="background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <p style="margin: 0; color: #721c24;">
                            <strong>⚠️ 建议操作:</strong>
                        </p>
                        <ul style="color: #721c24; margin: 10px 0 0 20px;">
                            <li>及时补充UPC码库存</li>
                            <li>检查申请和回收流程</li>
                            <li>联系相关负责人处理</li>
                        </ul>
                    </div>
                    
                    <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                    
                    <p style="color: #999; font-size: 12px; text-align: center;">
                        此邮件由 UPC管理系统 V2.6.0 自动发送，请勿回复。<br>
                        如有问题，请联系系统管理员。
                    </p>
                </div>
            `;

            return await this.sendEmail(to, subject, content, true);
        } catch (error) {
            console.error('发送库存预警邮件失败:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // 获取邮件服务状态
    getServiceStatus() {
        return {
            enabled: this.config?.enabled || false,
            configured: !!(this.config?.smtpServer && this.config?.senderEmail),
            transporterReady: !!this.transporter,
            config: {
                smtpServer: this.config?.smtpServer || '',
                smtpPort: this.config?.smtpPort || 587,
                senderEmail: this.config?.senderEmail || '',
                senderName: this.config?.senderName || 'UPC管理系统',
                enableSSL: this.config?.enableSSL || false
            }
        };
    }
}

module.exports = EmailService;
