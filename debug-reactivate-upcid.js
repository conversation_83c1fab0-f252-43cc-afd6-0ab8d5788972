#!/usr/bin/env node

// 调试重新激活后upcId丢失问题
// 模拟管理端重新激活 -> 用户端标记已使用的完整流程

const http = require('http');

console.log('🔍 调试重新激活后upcId丢失问题');
console.log('=====================================');

// HTTP请求工具函数
function makeRequest(options) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const result = {
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data ? JSON.parse(data) : null
                    };
                    resolve(result);
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data,
                        parseError: error.message
                    });
                }
            });
        });
        
        req.on('error', reject);
        req.setTimeout(10000, () => {
            req.destroy();
            reject(new Error('请求超时'));
        });
        
        if (options.body) {
            req.write(options.body);
        }
        req.end();
    });
}

// 登录函数
async function login(credentials) {
    console.log(`🔍 登录: ${credentials.username}`);
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(credentials)
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        console.log(`✅ 登录成功: ${response.data.user.name} (${response.data.user.role})`);
        return response.data.sessionId;
    } else {
        throw new Error(`登录失败: ${response.data?.message || '未知错误'}`);
    }
}

// 获取回收历史
async function getRecycleHistory(sessionId) {
    console.log('📊 获取回收历史...');
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/recycle/history',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${sessionId}`
        }
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        console.log(`✅ 获取回收历史成功，记录数: ${response.data.data.length}`);
        return response.data.data;
    } else {
        throw new Error(`获取回收历史失败: ${response.data?.message || '未知错误'}`);
    }
}

// 重新激活UPC码
async function reactivateUPC(sessionId, upcCode) {
    console.log(`🔄 重新激活UPC码: ${upcCode}`);
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc/reactivate',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${sessionId}`
        },
        body: JSON.stringify({ upcCodes: [upcCode] })
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        console.log(`✅ 重新激活成功: ${upcCode}`);
        return response.data;
    } else {
        throw new Error(`重新激活失败: ${response.data?.message || '未知错误'}`);
    }
}

// 分析upcId字段
function analyzeUpcId(records, phase) {
    console.log(`\n📊 分析upcId字段 (${phase}):`);
    
    const withUpcId = records.filter(r => r.upcId);
    const withoutUpcId = records.filter(r => !r.upcId);
    
    console.log(`   有upcId: ${withUpcId.length}`);
    console.log(`   无upcId: ${withoutUpcId.length}`);
    console.log(`   总记录: ${records.length}`);
    
    if (withoutUpcId.length > 0) {
        console.log(`\n❌ 缺少upcId的记录:`);
        withoutUpcId.slice(0, 5).forEach(record => {
            console.log(`   ${record.code} - 状态: ${record.status} - upcId: ${record.upcId}`);
        });
    }
    
    return { withUpcId: withUpcId.length, withoutUpcId: withoutUpcId.length };
}

// 主测试函数
async function runTest() {
    try {
        console.log('开始调试重新激活后upcId丢失问题...\n');
        
        // 步骤1: 管理员登录
        const adminSession = await login({ username: 'manager', password: 'Manager@2025' });
        
        // 步骤2: 获取初始回收历史
        console.log('\n📋 步骤1: 获取初始回收历史');
        const initialHistory = await getRecycleHistory(adminSession);
        const initialStats = analyzeUpcId(initialHistory, '初始状态');
        
        // 找一个可以重新激活的UPC码
        const reusableRecord = initialHistory.find(r => r.status === 'reusable' && r.upcId);
        if (!reusableRecord) {
            console.log('❌ 没有找到可重新激活的记录');
            return;
        }
        
        console.log(`\n🎯 选择测试UPC码: ${reusableRecord.code} (ID: ${reusableRecord.upcId})`);
        
        // 步骤3: 执行重新激活
        console.log('\n📋 步骤2: 执行重新激活');
        await reactivateUPC(adminSession, reusableRecord.code);
        
        // 等待一下让数据同步
        console.log('⏰ 等待2秒让数据同步...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 步骤4: 重新获取回收历史
        console.log('\n📋 步骤3: 重新获取回收历史');
        const afterHistory = await getRecycleHistory(adminSession);
        const afterStats = analyzeUpcId(afterHistory, '重新激活后');
        
        // 步骤5: 查找特定记录的变化
        console.log('\n📋 步骤4: 分析特定记录的变化');
        const afterRecord = afterHistory.find(r => r.code === reusableRecord.code);
        
        if (afterRecord) {
            console.log(`🔍 记录变化对比:`);
            console.log(`   UPC码: ${reusableRecord.code}`);
            console.log(`   重新激活前:`);
            console.log(`     状态: ${reusableRecord.status}`);
            console.log(`     upcId: ${reusableRecord.upcId} (${typeof reusableRecord.upcId})`);
            console.log(`   重新激活后:`);
            console.log(`     状态: ${afterRecord.status}`);
            console.log(`     upcId: ${afterRecord.upcId} (${typeof afterRecord.upcId})`);
            
            if (!afterRecord.upcId) {
                console.log(`❌ 问题确认: 重新激活后upcId丢失！`);
            } else {
                console.log(`✅ upcId保持正常`);
            }
        } else {
            console.log(`❌ 重新激活后找不到记录: ${reusableRecord.code}`);
        }
        
        // 步骤6: 尝试用户端标记已使用
        console.log('\n📋 步骤5: 模拟用户端标记已使用');
        if (afterRecord && afterRecord.upcId) {
            console.log(`🎯 尝试标记已使用: ${afterRecord.code} (ID: ${afterRecord.upcId})`);
            
            const options = {
                hostname: 'localhost',
                port: 3001,
                path: '/api/upc/update',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${adminSession}`
                },
                body: JSON.stringify({
                    upcId: afterRecord.upcId,
                    updates: {
                        status: 'invalid',
                        notes: '调试测试标记为已使用'
                    }
                })
            };
            
            const markResponse = await makeRequest(options);
            
            if (markResponse.statusCode === 200 && markResponse.data?.success) {
                console.log(`✅ 标记已使用成功`);
            } else {
                console.log(`❌ 标记已使用失败: ${markResponse.data?.message || '未知错误'}`);
            }
        } else {
            console.log(`❌ 无法测试标记已使用: upcId缺失`);
        }
        
        // 总结
        console.log('\n📊 测试总结');
        console.log('=====================================');
        console.log(`初始状态: 有upcId ${initialStats.withUpcId}, 无upcId ${initialStats.withoutUpcId}`);
        console.log(`重新激活后: 有upcId ${afterStats.withUpcId}, 无upcId ${afterStats.withoutUpcId}`);
        
        const upcIdLost = afterStats.withoutUpcId > initialStats.withoutUpcId;
        console.log(`upcId丢失情况: ${upcIdLost ? '❌ 有丢失' : '✅ 无丢失'}`);
        
        if (upcIdLost) {
            console.log('\n🔧 建议修复方案:');
            console.log('1. 检查重新激活API是否正确更新了UPC池数据');
            console.log('2. 检查回收历史API的upcId查找逻辑');
            console.log('3. 确保重新激活后UPC码状态与回收记录状态一致');
        }
        
    } catch (error) {
        console.log(`❌ 测试失败: ${error.message}`);
        process.exit(1);
    }
}

// 运行测试
runTest();
