#!/usr/bin/env node

// 模拟用户端完整流程：管理端重新激活 -> 用户端刷新 -> 标记已使用

const http = require('http');

console.log('🔍 模拟用户端完整流程');
console.log('=====================================');

// HTTP请求工具函数
function makeRequest(options) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const result = {
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data ? JSON.parse(data) : null
                    };
                    resolve(result);
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data,
                        parseError: error.message
                    });
                }
            });
        });
        
        req.on('error', reject);
        req.setTimeout(10000, () => {
            req.destroy();
            reject(new Error('请求超时'));
        });
        
        if (options.body) {
            req.write(options.body);
        }
        req.end();
    });
}

// 登录函数
async function login(credentials) {
    console.log(`🔍 登录: ${credentials.username}`);
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(credentials)
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        console.log(`✅ 登录成功: ${response.data.user.name} (${response.data.user.role})`);
        return response.data.sessionId;
    } else {
        throw new Error(`登录失败: ${response.data?.message || '未知错误'}`);
    }
}

// 获取回收历史
async function getRecycleHistory(sessionId, userType) {
    console.log(`📊 获取回收历史 (${userType})...`);
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/recycle/history',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${sessionId}`
        }
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        console.log(`✅ 获取回收历史成功，记录数: ${response.data.data.length}`);
        return response.data.data;
    } else {
        throw new Error(`获取回收历史失败: ${response.data?.message || '未知错误'}`);
    }
}

// 重新激活UPC码
async function reactivateUPC(sessionId, upcCode) {
    console.log(`🔄 重新激活UPC码: ${upcCode}`);
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc/reactivate',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${sessionId}`
        },
        body: JSON.stringify({ upcCodes: [upcCode] })
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        console.log(`✅ 重新激活成功: ${upcCode}`);
        return response.data;
    } else {
        throw new Error(`重新激活失败: ${response.data?.message || '未知错误'}`);
    }
}

// 标记已使用
async function markAsUsed(sessionId, upcId, upcCode) {
    console.log(`🎯 标记已使用: ${upcCode} (ID: ${upcId})`);
    
    // 先验证upcId（模拟前端验证）
    if (!upcId || upcId === null || upcId === undefined) {
        console.log(`❌ 前端验证失败: upcId无效`);
        return {
            success: false,
            error: `无法标记已使用：UPC码 ${upcCode} 缺少有效的ID信息，请刷新页面后重试`
        };
    }
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc/update',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${sessionId}`
        },
        body: JSON.stringify({
            upcId: upcId,
            updates: {
                status: 'invalid',
                notes: '用户端测试标记为已使用'
            }
        })
    };
    
    try {
        const response = await makeRequest(options);
        
        if (response.statusCode === 200 && response.data?.success) {
            console.log(`✅ 标记已使用成功`);
            return { success: true };
        } else {
            console.log(`❌ 标记已使用失败: ${response.data?.message || '未知错误'}`);
            return { success: false, error: response.data?.message || '未知错误' };
        }
    } catch (error) {
        console.log(`❌ 标记已使用异常: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// 主测试流程
async function runUserFlow() {
    try {
        console.log('开始模拟用户端完整流程...\n');
        
        // 步骤1: 管理员登录
        console.log('📋 步骤1: 管理员登录');
        const adminSession = await login({ username: 'manager', password: 'Manager@2025' });
        
        // 步骤2: 获取管理员视角的回收历史
        console.log('\n📋 步骤2: 获取管理员视角的回收历史');
        const adminHistory = await getRecycleHistory(adminSession, '管理员');
        
        // 找一个可重新激活的记录
        const reusableRecord = adminHistory.find(r => r.status === 'reusable' && r.upcId);
        if (!reusableRecord) {
            console.log('❌ 没有找到可重新激活的记录');
            return;
        }
        
        console.log(`\n🎯 选择测试UPC码: ${reusableRecord.code} (ID: ${reusableRecord.upcId})`);
        
        // 步骤3: 管理员重新激活
        console.log('\n📋 步骤3: 管理员重新激活');
        await reactivateUPC(adminSession, reusableRecord.code);
        
        // 步骤4: 等待数据同步
        console.log('\n📋 步骤4: 等待数据同步');
        console.log('⏰ 等待3秒让数据同步...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 步骤5: 用户登录（模拟用户端）
        console.log('\n📋 步骤5: 用户登录（模拟用户端）');
        const userSession = await login({ username: 'operator', password: 'Operator@2025' });
        
        // 步骤6: 用户获取回收历史（模拟用户端刷新）
        console.log('\n📋 步骤6: 用户获取回收历史（模拟用户端刷新）');
        const userHistory = await getRecycleHistory(userSession, '用户');
        
        // 查找对应的记录
        const userRecord = userHistory.find(r => r.code === reusableRecord.code);
        
        if (!userRecord) {
            console.log(`❌ 用户端没有找到记录: ${reusableRecord.code}`);
            console.log('这可能是权限问题，用户只能看到自己的记录');
            
            // 尝试用管理员身份再次获取，看看记录状态
            console.log('\n🔍 用管理员身份再次检查记录状态:');
            const adminHistoryAfter = await getRecycleHistory(adminSession, '管理员（重新激活后）');
            const adminRecordAfter = adminHistoryAfter.find(r => r.code === reusableRecord.code);
            
            if (adminRecordAfter) {
                console.log(`📊 管理员视角的记录状态:`);
                console.log(`   代码: ${adminRecordAfter.code}`);
                console.log(`   状态: ${adminRecordAfter.status}`);
                console.log(`   upcId: ${adminRecordAfter.upcId} (${typeof adminRecordAfter.upcId})`);
                console.log(`   用户: ${adminRecordAfter.user}`);
                
                // 尝试用管理员身份标记已使用
                console.log('\n🧪 用管理员身份测试标记已使用:');
                const markResult = await markAsUsed(adminSession, adminRecordAfter.upcId, adminRecordAfter.code);
                
                if (markResult.success) {
                    console.log('✅ 管理员标记已使用成功');
                } else {
                    console.log(`❌ 管理员标记已使用失败: ${markResult.error}`);
                }
            }
            
            return;
        }
        
        console.log(`\n📊 用户端看到的记录:`);
        console.log(`   代码: ${userRecord.code}`);
        console.log(`   状态: ${userRecord.status}`);
        console.log(`   upcId: ${userRecord.upcId} (${typeof userRecord.upcId})`);
        console.log(`   用户: ${userRecord.user}`);
        
        // 步骤7: 用户尝试标记已使用
        console.log('\n📋 步骤7: 用户尝试标记已使用');
        const markResult = await markAsUsed(userSession, userRecord.upcId, userRecord.code);
        
        if (markResult.success) {
            console.log('✅ 用户标记已使用成功');
        } else {
            console.log(`❌ 用户标记已使用失败: ${markResult.error}`);
            
            // 这里就是用户遇到的问题
            if (markResult.error && markResult.error.includes('缺少有效的ID信息')) {
                console.log('\n🔍 问题确认: 这就是用户遇到的问题！');
                console.log('   用户端看到的upcId:', userRecord.upcId);
                console.log('   upcId类型:', typeof userRecord.upcId);
                console.log('   前端验证结果: upcId无效');
            }
        }
        
        // 总结
        console.log('\n📊 流程总结');
        console.log('=====================================');
        console.log(`管理员重新激活: ✅ 成功`);
        console.log(`用户端数据同步: ${userRecord ? '✅ 成功' : '❌ 失败'}`);
        console.log(`用户端upcId状态: ${userRecord?.upcId ? '✅ 有效' : '❌ 无效'}`);
        console.log(`用户标记已使用: ${markResult.success ? '✅ 成功' : '❌ 失败'}`);
        
        if (!markResult.success) {
            console.log('\n🔧 问题诊断:');
            if (!userRecord) {
                console.log('- 用户端无法看到重新激活的记录（权限问题）');
            } else if (!userRecord.upcId) {
                console.log('- 用户端记录缺少upcId字段');
            } else {
                console.log('- 其他API调用问题');
            }
        }
        
    } catch (error) {
        console.log(`❌ 流程失败: ${error.message}`);
        process.exit(1);
    }
}

// 运行测试
runUserFlow();
