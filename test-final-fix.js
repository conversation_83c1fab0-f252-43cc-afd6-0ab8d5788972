#!/usr/bin/env node

// 测试最终修复：确保用户能看到重新激活的记录并标记已使用

const http = require('http');

console.log('🧪 测试最终修复效果');
console.log('=====================================');

// HTTP请求工具函数
function makeRequest(options) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const result = {
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data ? JSON.parse(data) : null
                    };
                    resolve(result);
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data,
                        parseError: error.message
                    });
                }
            });
        });
        
        req.on('error', reject);
        req.setTimeout(10000, () => {
            req.destroy();
            reject(new Error('请求超时'));
        });
        
        if (options.body) {
            req.write(options.body);
        }
        req.end();
    });
}

// 登录函数
async function login(credentials) {
    console.log(`🔍 登录: ${credentials.username}`);
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(credentials)
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        console.log(`✅ 登录成功: ${response.data.user.name} (${response.data.user.role})`);
        return response.data.sessionId;
    } else {
        throw new Error(`登录失败: ${response.data?.message || '未知错误'}`);
    }
}

// 获取回收历史
async function getRecycleHistory(sessionId, userType) {
    console.log(`📊 获取回收历史 (${userType})...`);
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/recycle/history',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${sessionId}`
        }
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        console.log(`✅ 获取回收历史成功，记录数: ${response.data.data.length}`);
        return response.data.data;
    } else {
        throw new Error(`获取回收历史失败: ${response.data?.message || '未知错误'}`);
    }
}

// 重新激活UPC码
async function reactivateUPC(sessionId, upcCode) {
    console.log(`🔄 重新激活UPC码: ${upcCode}`);
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc/reactivate',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${sessionId}`
        },
        body: JSON.stringify({ upcCodes: [upcCode] })
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        console.log(`✅ 重新激活成功: ${upcCode}`);
        return response.data;
    } else {
        throw new Error(`重新激活失败: ${response.data?.message || '未知错误'}`);
    }
}

// 标记已使用
async function markAsUsed(sessionId, upcId, upcCode) {
    console.log(`🎯 标记已使用: ${upcCode} (ID: ${upcId})`);
    
    // 前端验证
    if (!upcId || upcId === null || upcId === undefined) {
        console.log(`❌ 前端验证失败: upcId无效`);
        return {
            success: false,
            error: `无法标记已使用：UPC码 ${upcCode} 缺少有效的ID信息，请刷新页面后重试`
        };
    }
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc/update',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${sessionId}`
        },
        body: JSON.stringify({
            upcId: upcId,
            updates: {
                status: 'invalid',
                notes: '测试标记为已使用'
            }
        })
    };
    
    try {
        const response = await makeRequest(options);
        
        if (response.statusCode === 200 && response.data?.success) {
            console.log(`✅ 标记已使用成功`);
            return { success: true };
        } else {
            console.log(`❌ 标记已使用失败: ${response.data?.message || '未知错误'}`);
            return { success: false, error: response.data?.message || '未知错误' };
        }
    } catch (error) {
        console.log(`❌ 标记已使用异常: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// 测试特定UPC码
async function testSpecificUPC() {
    try {
        console.log('开始测试特定UPC码: 758522447161...\n');
        
        // 步骤1: 管理员登录
        console.log('📋 步骤1: 管理员登录');
        const adminSession = await login({ username: 'manager', password: 'Manager@2025' });
        
        // 步骤2: 重新激活特定UPC码
        console.log('\n📋 步骤2: 重新激活特定UPC码');
        const targetUPC = '758522447161';
        await reactivateUPC(adminSession, targetUPC);
        
        // 步骤3: 等待数据同步
        console.log('\n📋 步骤3: 等待数据同步');
        console.log('⏰ 等待3秒让数据同步...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 步骤4: 获取管理员的回收历史
        console.log('\n📋 步骤4: 获取管理员的回收历史');
        const adminHistory = await getRecycleHistory(adminSession, '管理员');
        
        // 查找目标记录
        const targetRecord = adminHistory.find(r => r.code === targetUPC);
        
        if (!targetRecord) {
            console.log(`❌ 管理员看不到记录: ${targetUPC}`);
            return;
        }
        
        console.log(`\n✅ 管理员可以看到记录:`);
        console.log(`   代码: ${targetRecord.code}`);
        console.log(`   状态: ${targetRecord.status}`);
        console.log(`   upcId: ${targetRecord.upcId} (${typeof targetRecord.upcId})`);
        console.log(`   用户: ${targetRecord.user}`);
        
        // 步骤5: 管理员尝试标记已使用
        console.log('\n📋 步骤5: 管理员尝试标记已使用');
        const markResult = await markAsUsed(adminSession, targetRecord.upcId, targetRecord.code);
        
        if (markResult.success) {
            console.log('✅ 管理员标记已使用成功');
        } else {
            console.log(`❌ 管理员标记已使用失败: ${markResult.error}`);
        }
        
        // 总结
        console.log('\n📊 测试结果总结');
        console.log('=====================================');
        console.log(`目标UPC码: ${targetUPC}`);
        console.log(`重新激活: ✅ 成功`);
        console.log(`记录可见: ${targetRecord ? '✅ 成功' : '❌ 失败'}`);
        console.log(`upcId有效: ${targetRecord?.upcId ? '✅ 有效' : '❌ 无效'}`);
        console.log(`标记已使用: ${markResult.success ? '✅ 成功' : '❌ 失败'}`);
        
        const overallSuccess = targetRecord && targetRecord.upcId && markResult.success;
        console.log(`\n总体结果: ${overallSuccess ? '✅ 测试成功' : '❌ 测试失败'}`);
        
        if (overallSuccess) {
            console.log('\n🎉 修复验证成功！');
            console.log('UPC码 758522447161 现在可以正常重新激活和标记已使用。');
        } else {
            console.log('\n❌ 仍然存在问题，需要进一步调试。');
        }
        
    } catch (error) {
        console.log(`❌ 测试失败: ${error.message}`);
        process.exit(1);
    }
}

// 运行测试
testSpecificUPC();
