# UPC管理系统 V2.9 运维教程

## 📋 目录
- [日常运维](#日常运维)
- [监控检查](#监控检查)
- [备份管理](#备份管理)
- [日志管理](#日志管理)
- [性能监控](#性能监控)
- [故障处理](#故障处理)
- [系统更新](#系统更新)
- [安全维护](#安全维护)

## 🔄 日常运维

### 每日检查清单

1. **服务状态检查**
   ```bash
   # 检查服务运行状态
   sudo systemctl status upc-system
   
   # 检查服务是否正常响应
   curl -s http://localhost:3000/api/health
   
   # 检查端口监听
   sudo ss -tlnp | grep 3000
   ```

2. **系统资源检查**
   ```bash
   # 检查CPU使用率
   top -bn1 | grep "Cpu(s)"
   
   # 检查内存使用
   free -h
   
   # 检查磁盘空间
   df -h
   
   # 检查系统负载
   uptime
   ```

3. **日志检查**
   ```bash
   # 检查应用日志
   tail -n 50 /opt/upc-system/logs/app.log
   
   # 检查系统日志
   sudo journalctl -u upc-system --since "24 hours ago" | grep -i error
   
   # 检查系统消息
   sudo tail -n 20 /var/log/messages
   ```

### 每周检查清单

1. **数据备份验证**
   ```bash
   # 检查备份文件
   ls -la /opt/upc-system/backup/
   
   # 验证最新备份
   cd /opt/upc-system/backup/
   ls -lt | head -5
   
   # 测试备份文件完整性
   tar -tzf latest-backup.tar.gz > /dev/null && echo "备份文件完整"
   ```

2. **系统更新检查**
   ```bash
   # 检查可用更新
   sudo dnf check-update
   
   # 检查安全更新
   sudo dnf updateinfo list security
   ```

3. **性能趋势分析**
   ```bash
   # 查看系统负载历史
   sar -u 1 1
   
   # 查看内存使用趋势
   sar -r 1 1
   
   # 查看磁盘I/O
   sar -d 1 1
   ```

### 每月检查清单

1. **安全审计**
   ```bash
   # 检查登录日志
   sudo last | head -20
   
   # 检查失败登录
   sudo lastb | head -10
   
   # 检查用户权限
   sudo cat /etc/passwd | grep upcadmin
   ```

2. **数据清理**
   ```bash
   # 清理旧日志
   sudo find /opt/upc-system/logs/ -name "*.log.*" -mtime +30 -delete
   
   # 清理临时文件
   sudo find /tmp -name "*upc*" -mtime +7 -delete
   
   # 清理旧备份
   sudo find /opt/upc-system/backup/ -name "*.tar.gz" -mtime +90 -delete
   ```

## 📊 监控检查

### 服务监控脚本

创建监控脚本 `/opt/upc-system/scripts/monitor.sh`：

```bash
#!/bin/bash

# UPC系统监控脚本
LOG_FILE="/opt/upc-system/logs/monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# 检查服务状态
check_service() {
    if systemctl is-active --quiet upc-system; then
        echo "[$DATE] 服务状态: 正常" >> $LOG_FILE
        return 0
    else
        echo "[$DATE] 服务状态: 异常" >> $LOG_FILE
        # 尝试重启服务
        sudo systemctl restart upc-system
        sleep 10
        if systemctl is-active --quiet upc-system; then
            echo "[$DATE] 服务重启: 成功" >> $LOG_FILE
        else
            echo "[$DATE] 服务重启: 失败" >> $LOG_FILE
            # 发送告警（可配置邮件或短信）
        fi
        return 1
    fi
}

# 检查HTTP响应
check_http() {
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/health)
    if [ "$HTTP_CODE" = "200" ]; then
        echo "[$DATE] HTTP检查: 正常 (200)" >> $LOG_FILE
        return 0
    else
        echo "[$DATE] HTTP检查: 异常 ($HTTP_CODE)" >> $LOG_FILE
        return 1
    fi
}

# 检查资源使用
check_resources() {
    # CPU使用率
    CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    
    # 内存使用率
    MEM_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    
    # 磁盘使用率
    DISK_USAGE=$(df -h /opt/upc-system | awk 'NR==2 {print $5}' | cut -d'%' -f1)
    
    echo "[$DATE] 资源使用: CPU=${CPU_USAGE}% MEM=${MEM_USAGE}% DISK=${DISK_USAGE}%" >> $LOG_FILE
    
    # 告警阈值检查
    if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
        echo "[$DATE] 告警: CPU使用率过高 (${CPU_USAGE}%)" >> $LOG_FILE
    fi
    
    if (( $(echo "$MEM_USAGE > 85" | bc -l) )); then
        echo "[$DATE] 告警: 内存使用率过高 (${MEM_USAGE}%)" >> $LOG_FILE
    fi
    
    if [ "$DISK_USAGE" -gt 90 ]; then
        echo "[$DATE] 告警: 磁盘使用率过高 (${DISK_USAGE}%)" >> $LOG_FILE
    fi
}

# 执行检查
check_service
check_http
check_resources

echo "[$DATE] 监控检查完成" >> $LOG_FILE
```

### 设置定时监控

```bash
# 添加到crontab，每5分钟检查一次
echo "*/5 * * * * /opt/upc-system/scripts/monitor.sh" | sudo crontab -u upcadmin -
```

## 💾 备份管理

### 自动备份脚本

创建备份脚本 `/opt/upc-system/scripts/backup.sh`：

```bash
#!/bin/bash

# UPC系统备份脚本
BACKUP_DIR="/opt/upc-system/backup"
DATA_DIR="/opt/upc-system/data"
DATE=$(date '+%Y%m%d_%H%M%S')
BACKUP_NAME="upc_backup_$DATE"

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 备份数据文件
echo "开始备份数据文件..."
tar -czf "$BACKUP_DIR/$BACKUP_NAME.tar.gz" -C "$DATA_DIR" .

# 验证备份
if [ $? -eq 0 ]; then
    echo "备份成功: $BACKUP_NAME.tar.gz"
    
    # 创建最新备份链接
    ln -sf "$BACKUP_NAME.tar.gz" "$BACKUP_DIR/latest.tar.gz"
    
    # 清理7天前的备份
    find "$BACKUP_DIR" -name "upc_backup_*.tar.gz" -mtime +7 -delete
    
    echo "备份完成，旧备份已清理"
else
    echo "备份失败"
    exit 1
fi
```

### 备份恢复

```bash
# 恢复备份
restore_backup() {
    local backup_file="$1"
    
    if [ ! -f "$backup_file" ]; then
        echo "备份文件不存在: $backup_file"
        return 1
    fi
    
    # 停止服务
    sudo systemctl stop upc-system
    
    # 备份当前数据
    mv /opt/upc-system/data /opt/upc-system/data.bak.$(date +%s)
    
    # 创建新数据目录
    mkdir -p /opt/upc-system/data
    
    # 恢复数据
    tar -xzf "$backup_file" -C /opt/upc-system/data
    
    # 设置权限
    chown -R upcadmin:upcadmin /opt/upc-system/data
    
    # 启动服务
    sudo systemctl start upc-system
    
    echo "备份恢复完成"
}

# 使用示例
# restore_backup "/opt/upc-system/backup/upc_backup_20250707_120000.tar.gz"
```

## 📝 日志管理

### 日志文件位置

```
/opt/upc-system/logs/
├── app.log              # 应用主日志
├── error.log            # 错误日志
├── access.log           # 访问日志
├── performance.log      # 性能日志
└── monitor.log          # 监控日志
```

### 日志轮转配置

创建 `/etc/logrotate.d/upc-system`：

```
/opt/upc-system/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 upcadmin upcadmin
    postrotate
        systemctl reload upc-system > /dev/null 2>&1 || true
    endrotate
}
```

### 日志分析脚本

```bash
#!/bin/bash

# 日志分析脚本
LOG_DIR="/opt/upc-system/logs"
REPORT_FILE="/tmp/upc_log_report.txt"

echo "UPC系统日志分析报告 - $(date)" > $REPORT_FILE
echo "=================================" >> $REPORT_FILE

# 错误统计
echo "错误统计:" >> $REPORT_FILE
grep -i error $LOG_DIR/app.log | wc -l | xargs echo "总错误数:" >> $REPORT_FILE
grep -i "critical\|fatal" $LOG_DIR/app.log | wc -l | xargs echo "严重错误数:" >> $REPORT_FILE

# 访问统计
echo -e "\n访问统计:" >> $REPORT_FILE
grep "GET\|POST" $LOG_DIR/access.log | wc -l | xargs echo "总请求数:" >> $REPORT_FILE

# 性能统计
echo -e "\n性能统计:" >> $REPORT_FILE
grep "响应时间" $LOG_DIR/performance.log | tail -10 >> $REPORT_FILE

# 最近错误
echo -e "\n最近错误 (最新10条):" >> $REPORT_FILE
grep -i error $LOG_DIR/app.log | tail -10 >> $REPORT_FILE

cat $REPORT_FILE
```

## 📈 性能监控

### 系统性能监控

```bash
# 实时性能监控脚本
#!/bin/bash

while true; do
    clear
    echo "UPC系统性能监控 - $(date)"
    echo "================================="
    
    # CPU使用率
    echo "CPU使用率:"
    top -bn1 | grep "Cpu(s)" | awk '{print "  用户: " $2 ", 系统: " $4 ", 空闲: " $8}'
    
    # 内存使用
    echo -e "\n内存使用:"
    free -h | grep -E "Mem|Swap" | awk '{print "  " $1 " 总计: " $2 ", 已用: " $3 ", 可用: " $7}'
    
    # 磁盘使用
    echo -e "\n磁盘使用:"
    df -h /opt/upc-system | awk 'NR==2 {print "  系统盘: " $5 " 已用 (" $3 "/" $2 ")"}'
    
    # 网络连接
    echo -e "\n网络连接:"
    ss -tuln | grep :3000 | wc -l | xargs echo "  活跃连接数:"
    
    # 进程信息
    echo -e "\nUPC进程信息:"
    ps aux | grep "node.*simple-server" | grep -v grep | awk '{print "  PID: " $2 ", CPU: " $3 "%, MEM: " $4 "%"}'
    
    sleep 5
done
```

### 应用性能监控

```bash
# 应用性能检查
check_app_performance() {
    echo "检查应用性能..."
    
    # 响应时间测试
    RESPONSE_TIME=$(curl -o /dev/null -s -w "%{time_total}" http://localhost:3000/api/health)
    echo "API响应时间: ${RESPONSE_TIME}s"
    
    # 内存使用
    PID=$(pgrep -f "node.*simple-server")
    if [ ! -z "$PID" ]; then
        MEM_USAGE=$(ps -p $PID -o rss= | awk '{print $1/1024 " MB"}')
        echo "应用内存使用: $MEM_USAGE"
    fi
    
    # 文件描述符使用
    if [ ! -z "$PID" ]; then
        FD_COUNT=$(ls /proc/$PID/fd | wc -l)
        echo "文件描述符使用: $FD_COUNT"
    fi
}
```

## 🚨 故障处理

### 常见故障处理流程

1. **服务无响应**
   ```bash
   # 检查进程
   ps aux | grep node
   
   # 检查端口
   sudo ss -tlnp | grep 3000
   
   # 重启服务
   sudo systemctl restart upc-system
   
   # 查看启动日志
   sudo journalctl -u upc-system -f
   ```

2. **内存泄漏**
   ```bash
   # 监控内存使用
   watch -n 1 'ps aux | grep node | grep -v grep'
   
   # 生成内存快照（如果安装了node调试工具）
   kill -USR2 $(pgrep -f "node.*simple-server")
   
   # 重启服务释放内存
   sudo systemctl restart upc-system
   ```

3. **磁盘空间不足**
   ```bash
   # 清理日志
   sudo find /opt/upc-system/logs/ -name "*.log.*" -mtime +7 -delete
   
   # 清理临时文件
   sudo rm -rf /tmp/*upc*
   
   # 清理旧备份
   sudo find /opt/upc-system/backup/ -name "*.tar.gz" -mtime +30 -delete
   ```

### 紧急恢复程序

```bash
# 紧急恢复脚本
emergency_recovery() {
    echo "执行紧急恢复程序..."
    
    # 停止服务
    sudo systemctl stop upc-system
    
    # 备份当前状态
    cp -r /opt/upc-system/data /opt/upc-system/data.emergency.$(date +%s)
    
    # 恢复最新备份
    if [ -f "/opt/upc-system/backup/latest.tar.gz" ]; then
        tar -xzf /opt/upc-system/backup/latest.tar.gz -C /opt/upc-system/data
        chown -R upcadmin:upcadmin /opt/upc-system/data
    fi
    
    # 重置权限
    sudo chown -R upcadmin:upcadmin /opt/upc-system/
    sudo chmod +x /opt/upc-system/*.sh
    
    # 启动服务
    sudo systemctl start upc-system
    
    # 验证恢复
    sleep 10
    curl -s http://localhost:3000/api/health && echo "恢复成功" || echo "恢复失败"
}
```

## 🔄 系统更新

### 应用更新流程

1. **准备更新**
   ```bash
   # 创建完整备份
   /opt/upc-system/scripts/backup.sh
   
   # 停止服务
   sudo systemctl stop upc-system
   ```

2. **执行更新**
   ```bash
   # 备份当前版本
   cp -r /opt/upc-system /opt/upc-system.backup.$(date +%s)
   
   # 部署新版本
   # (根据具体更新包进行操作)
   
   # 更新依赖
   cd /opt/upc-system
   npm install --production
   ```

3. **验证更新**
   ```bash
   # 启动服务
   sudo systemctl start upc-system
   
   # 检查版本
   curl -s http://localhost:3000/api/version
   
   # 功能测试
   curl -s http://localhost:3000/api/health
   ```

### 系统更新

```bash
# 系统安全更新
sudo dnf update --security -y

# 重启服务（如果需要）
sudo systemctl restart upc-system
```

## 🔒 安全维护

### 定期安全检查

```bash
# 检查系统安全
security_check() {
    echo "执行安全检查..."
    
    # 检查登录失败
    sudo lastb | head -10
    
    # 检查异常进程
    ps aux | grep -v "^\(root\|upcadmin\)"
    
    # 检查网络连接
    sudo ss -tuln | grep -v "127.0.0.1\|::1"
    
    # 检查文件权限
    find /opt/upc-system -type f -perm /o+w
    
    echo "安全检查完成"
}
```

### 密码更新

```bash
# 定期更新系统密钥
update_secrets() {
    # 生成新密钥
    NEW_SECRET=$(openssl rand -base64 32)
    
    # 更新配置文件
    sed -i "s/UPC_SESSION_SECRET=.*/UPC_SESSION_SECRET=$NEW_SECRET/" /opt/upc-system/.env
    
    # 重启服务
    sudo systemctl restart upc-system
    
    echo "密钥更新完成"
}
```

---

**版本**: V2.9.0  
**更新日期**: 2025-07-07  
**文档版本**: 1.0
