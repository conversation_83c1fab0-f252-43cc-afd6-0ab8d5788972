#!/usr/bin/env node

// 测试用户端完整体验：模拟用户看到重新激活记录并标记已使用

const http = require('http');

console.log('🧪 测试用户端完整体验');
console.log('=====================================');

// HTTP请求工具函数
function makeRequest(options) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const result = {
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data ? JSON.parse(data) : null
                    };
                    resolve(result);
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data,
                        parseError: error.message
                    });
                }
            });
        });
        
        req.on('error', reject);
        req.setTimeout(10000, () => {
            req.destroy();
            reject(new Error('请求超时'));
        });
        
        if (options.body) {
            req.write(options.body);
        }
        req.end();
    });
}

// 登录函数
async function login(credentials) {
    console.log(`🔍 登录: ${credentials.username}`);
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(credentials)
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        console.log(`✅ 登录成功: ${response.data.user.name} (${response.data.user.role})`);
        return response.data.sessionId;
    } else {
        throw new Error(`登录失败: ${response.data?.message || '未知错误'}`);
    }
}

// 获取回收历史
async function getRecycleHistory(sessionId, userType) {
    console.log(`📊 获取回收历史 (${userType})...`);
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/recycle/history',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${sessionId}`
        }
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        console.log(`✅ 获取回收历史成功，记录数: ${response.data.data.length}`);
        return response.data.data;
    } else {
        throw new Error(`获取回收历史失败: ${response.data?.message || '未知错误'}`);
    }
}

// 重新激活UPC码
async function reactivateUPC(sessionId, upcCode) {
    console.log(`🔄 重新激活UPC码: ${upcCode}`);
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc/reactivate',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${sessionId}`
        },
        body: JSON.stringify({ upcCodes: [upcCode] })
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        console.log(`✅ 重新激活成功: ${upcCode}`);
        return response.data;
    } else {
        throw new Error(`重新激活失败: ${response.data?.message || '未知错误'}`);
    }
}

// 模拟前端markAsUsed函数
async function markAsUsed(sessionId, record) {
    console.log(`🎯 模拟前端标记已使用: ${record.code}`);
    
    // 🔧 前端验证逻辑（修复后）
    if (!record.upcId || record.upcId === null || record.upcId === undefined) {
        console.log(`❌ 前端验证失败: upcId无效`);
        console.log(`   这就是用户看到的错误信息！`);
        return {
            success: false,
            error: `无法标记已使用：UPC码 ${record.code} 缺少有效的ID信息，请刷新页面后重试`
        };
    }
    
    console.log(`✅ 前端验证通过，upcId: ${record.upcId}`);
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc/update',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${sessionId}`
        },
        body: JSON.stringify({
            upcId: record.upcId,
            updates: {
                status: 'invalid',
                notes: '用户端标记为已使用'
            }
        })
    };
    
    try {
        const response = await makeRequest(options);
        
        if (response.statusCode === 200 && response.data?.success) {
            console.log(`✅ API调用成功`);
            return { success: true };
        } else {
            console.log(`❌ API调用失败: ${response.data?.message || '未知错误'}`);
            return { success: false, error: response.data?.message || '未知错误' };
        }
    } catch (error) {
        console.log(`❌ API调用异常: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// 测试完整用户体验
async function testUserExperience() {
    try {
        console.log('开始测试用户端完整体验...\n');
        
        // 场景：管理员重新激活 -> 用户端刷新 -> 标记已使用
        
        // 步骤1: 管理员登录并重新激活
        console.log('📋 步骤1: 管理员重新激活UPC码');
        const adminSession = await login({ username: 'manager', password: 'Manager@2025' });
        
        // 获取一个可重新激活的记录
        const adminHistory = await getRecycleHistory(adminSession, '管理员');
        const reusableRecord = adminHistory.find(r => r.status === 'reusable' && r.upcId);
        
        if (!reusableRecord) {
            console.log('❌ 没有找到可重新激活的记录');
            return;
        }
        
        console.log(`🎯 选择UPC码: ${reusableRecord.code} (归属: ${reusableRecord.user})`);
        
        // 重新激活
        await reactivateUPC(adminSession, reusableRecord.code);
        
        // 步骤2: 等待数据同步
        console.log('\n📋 步骤2: 等待数据同步');
        console.log('⏰ 等待3秒让数据同步...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 步骤3: 用户端登录（模拟用户刷新页面）
        console.log('\n📋 步骤3: 用户端登录（模拟刷新页面）');
        
        // 根据记录归属确定登录用户
        let userSession;
        let userType;
        
        if (reusableRecord.user === 'manager') {
            userSession = adminSession; // 复用管理员会话
            userType = 'manager';
            console.log('使用管理员会话（记录归属管理员）');
        } else if (reusableRecord.user === 'operator') {
            userSession = await login({ username: 'operator', password: 'Operator@2025' });
            userType = 'operator';
        } else {
            console.log(`❌ 未知的用户类型: ${reusableRecord.user}`);
            return;
        }
        
        // 步骤4: 用户端获取回收历史（模拟页面加载）
        console.log('\n📋 步骤4: 用户端获取回收历史（模拟页面加载）');
        const userHistory = await getRecycleHistory(userSession, `用户端(${userType})`);
        
        // 查找重新激活的记录
        const userRecord = userHistory.find(r => r.code === reusableRecord.code);
        
        if (!userRecord) {
            console.log(`❌ 用户端看不到重新激活的记录: ${reusableRecord.code}`);
            console.log('这就是原来的问题！用户无法看到管理员重新激活的记录。');
            return;
        }
        
        console.log(`\n✅ 用户端可以看到重新激活的记录:`);
        console.log(`   代码: ${userRecord.code}`);
        console.log(`   状态: ${userRecord.status}`);
        console.log(`   upcId: ${userRecord.upcId} (${typeof userRecord.upcId})`);
        console.log(`   用户: ${userRecord.user}`);
        
        // 步骤5: 用户端尝试标记已使用
        console.log('\n📋 步骤5: 用户端尝试标记已使用');
        const markResult = await markAsUsed(userSession, userRecord);
        
        // 总结
        console.log('\n📊 用户体验测试结果');
        console.log('=====================================');
        console.log(`管理员重新激活: ✅ 成功`);
        console.log(`用户端数据同步: ${userRecord ? '✅ 成功' : '❌ 失败'}`);
        console.log(`用户端upcId有效: ${userRecord?.upcId ? '✅ 有效' : '❌ 无效'}`);
        console.log(`用户端标记已使用: ${markResult.success ? '✅ 成功' : '❌ 失败'}`);
        
        const overallSuccess = userRecord && userRecord.upcId && markResult.success;
        console.log(`\n总体用户体验: ${overallSuccess ? '✅ 完美' : '❌ 有问题'}`);
        
        if (overallSuccess) {
            console.log('\n🎉 用户体验测试成功！');
            console.log('现在用户可以正常看到管理员重新激活的记录，并成功标记已使用。');
            console.log('不再出现"缺少有效的ID信息"错误。');
        } else {
            console.log('\n❌ 用户体验仍有问题：');
            if (!userRecord) {
                console.log('- 用户端无法看到重新激活的记录');
            } else if (!userRecord.upcId) {
                console.log('- 用户端记录缺少upcId');
            } else if (!markResult.success) {
                console.log(`- 标记已使用失败: ${markResult.error}`);
            }
        }
        
    } catch (error) {
        console.log(`❌ 测试失败: ${error.message}`);
        process.exit(1);
    }
}

// 运行测试
testUserExperience();
