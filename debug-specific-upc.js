#!/usr/bin/env node

// 调试特定UPC码 758522447161 的问题

const http = require('http');

console.log('🔍 调试特定UPC码: 758522447161');
console.log('=====================================');

// HTTP请求工具函数
function makeRequest(options) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const result = {
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data ? JSON.parse(data) : null
                    };
                    resolve(result);
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data,
                        parseError: error.message
                    });
                }
            });
        });
        
        req.on('error', reject);
        req.setTimeout(10000, () => {
            req.destroy();
            reject(new Error('请求超时'));
        });
        
        if (options.body) {
            req.write(options.body);
        }
        req.end();
    });
}

// 登录函数
async function login(credentials) {
    console.log(`🔍 登录: ${credentials.username}`);
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(credentials)
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        console.log(`✅ 登录成功: ${response.data.user.name} (${response.data.user.role})`);
        return response.data.sessionId;
    } else {
        throw new Error(`登录失败: ${response.data?.message || '未知错误'}`);
    }
}

// 获取UPC池数据
async function getUpcPool(sessionId) {
    console.log('📊 获取UPC池数据...');
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc-pool',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${sessionId}`
        }
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        console.log(`✅ 获取UPC池数据成功，记录数: ${response.data.data.length}`);
        return response.data.data;
    } else {
        throw new Error(`获取UPC池数据失败: ${response.data?.message || '未知错误'}`);
    }
}

// 获取回收历史
async function getRecycleHistory(sessionId) {
    console.log('📊 获取回收历史...');
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/recycle/history',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${sessionId}`
        }
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        console.log(`✅ 获取回收历史成功，记录数: ${response.data.data.length}`);
        return response.data.data;
    } else {
        throw new Error(`获取回收历史失败: ${response.data?.message || '未知错误'}`);
    }
}

// 分析特定UPC码
async function analyzeSpecificUPC(upcCode) {
    try {
        console.log(`\n🎯 分析UPC码: ${upcCode}`);
        
        // 管理员登录
        const adminSession = await login({ username: 'manager', password: 'Manager@2025' });
        
        // 获取UPC池数据
        const upcPool = await getUpcPool(adminSession);
        const targetUpc = upcPool.find(u => u.code === upcCode);
        
        console.log(`\n📊 UPC池中的记录:`);
        if (targetUpc) {
            console.log(`   ID: ${targetUpc.id}`);
            console.log(`   代码: ${targetUpc.code}`);
            console.log(`   状态: ${targetUpc.status}`);
            console.log(`   分配用户: ${targetUpc.assigned_user}`);
            console.log(`   更新时间: ${targetUpc.updated_at}`);
        } else {
            console.log(`   ❌ 在UPC池中未找到: ${upcCode}`);
        }
        
        // 获取回收历史
        const recycleHistory = await getRecycleHistory(adminSession);
        const recycleRecords = recycleHistory.filter(r => r.code === upcCode);
        
        console.log(`\n📊 回收历史中的记录 (${recycleRecords.length}条):`);
        recycleRecords.forEach((record, index) => {
            console.log(`   记录${index + 1}:`);
            console.log(`     ID: ${record.id}`);
            console.log(`     代码: ${record.code}`);
            console.log(`     upcId: ${record.upcId} (${typeof record.upcId})`);
            console.log(`     状态: ${record.status}`);
            console.log(`     用户: ${record.user}`);
            console.log(`     日期: ${record.date} ${record.time}`);
        });
        
        // 分析问题
        console.log(`\n🔍 问题分析:`);
        
        if (!targetUpc) {
            console.log(`❌ 问题1: UPC码在UPC池中不存在`);
        }
        
        const recordsWithoutUpcId = recycleRecords.filter(r => !r.upcId);
        if (recordsWithoutUpcId.length > 0) {
            console.log(`❌ 问题2: ${recordsWithoutUpcId.length}条回收记录缺少upcId`);
        }
        
        if (targetUpc && recycleRecords.length > 0) {
            const mismatchedRecords = recycleRecords.filter(r => r.upcId !== targetUpc.id);
            if (mismatchedRecords.length > 0) {
                console.log(`❌ 问题3: ${mismatchedRecords.length}条回收记录的upcId与UPC池不匹配`);
                mismatchedRecords.forEach(r => {
                    console.log(`     回收记录upcId: ${r.upcId}, UPC池ID: ${targetUpc.id}`);
                });
            }
        }
        
        // 尝试标记已使用
        if (recycleRecords.length > 0) {
            const testRecord = recycleRecords[0];
            console.log(`\n🧪 测试标记已使用:`);
            console.log(`   使用记录: ${testRecord.code} (upcId: ${testRecord.upcId})`);
            
            if (!testRecord.upcId) {
                console.log(`❌ 无法测试: upcId为空`);
                console.log(`   这就是用户看到"缺少有效的ID信息"错误的原因`);
            } else {
                console.log(`✅ 可以测试: upcId有效`);
                
                // 实际调用标记已使用API
                const options = {
                    hostname: 'localhost',
                    port: 3001,
                    path: '/api/upc/update',
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${adminSession}`
                    },
                    body: JSON.stringify({
                        upcId: testRecord.upcId,
                        updates: {
                            status: 'invalid',
                            notes: '调试测试标记为已使用'
                        }
                    })
                };
                
                try {
                    const markResponse = await makeRequest(options);
                    
                    if (markResponse.statusCode === 200 && markResponse.data?.success) {
                        console.log(`✅ 标记已使用成功`);
                    } else {
                        console.log(`❌ 标记已使用失败: ${markResponse.data?.message || '未知错误'}`);
                    }
                } catch (error) {
                    console.log(`❌ 标记已使用异常: ${error.message}`);
                }
            }
        }
        
    } catch (error) {
        console.log(`❌ 分析失败: ${error.message}`);
    }
}

// 运行分析
analyzeSpecificUPC('758522447161');
