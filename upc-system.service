[Unit]
Description=UPC管理系统 V3.0 - 企业级UPC码管理解决方案
Documentation=https://sutuo.net
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=upc
Group=upc
WorkingDirectory=/opt/upc-system
ExecStart=/usr/bin/node simple-server.js
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
TimeoutStartSec=60
TimeoutStopSec=30

# 环境变量
Environment=NODE_ENV=production
Environment=PORT=3001
Environment=TZ=Asia/Shanghai

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=upc-system

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/upc-system
ReadOnlyPaths=/opt/upc-system/public

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 进程管理
KillMode=mixed
KillSignal=SIGTERM

[Install]
WantedBy=multi-user.target
