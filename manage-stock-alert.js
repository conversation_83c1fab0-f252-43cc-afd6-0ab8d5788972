// 库存预警配置管理脚本
const fs = require('fs');
const http = require('http');

// 配置
const SERVER_URL = 'http://localhost:3001';
const SETTINGS_FILE = './system_settings.json';

// 读取当前设置
function loadSettings() {
    try {
        if (fs.existsSync(SETTINGS_FILE)) {
            const data = fs.readFileSync(SETTINGS_FILE, 'utf8');
            return JSON.parse(data);
        }
        return {};
    } catch (error) {
        console.error('读取设置失败:', error.message);
        return {};
    }
}

// 保存设置
function saveSettings(settings) {
    try {
        fs.writeFileSync(SETTINGS_FILE, JSON.stringify(settings, null, 2), 'utf8');
        return true;
    } catch (error) {
        console.error('保存设置失败:', error.message);
        return false;
    }
}

// 获取当前库存
function getCurrentStock() {
    try {
        const upcData = fs.readFileSync('./data/upc_codes.json', 'utf8');
        const upcCodes = JSON.parse(upcData);
        return upcCodes.filter(c => c.status === 'available').length;
    } catch (error) {
        console.error('读取库存数据失败:', error.message);
        return 0;
    }
}

// 显示当前配置
function showCurrentConfig() {
    const settings = loadSettings();
    const stockAlert = settings.stockAlert || {};
    const currentStock = getCurrentStock();
    
    console.log('\n📊 当前库存预警配置:');
    console.log('================================');
    console.log(`📋 预警启用: ${stockAlert.enableStockAlert ? '✅ 是' : '❌ 否'}`);
    console.log(`📋 当前库存: ${currentStock} 个`);
    console.log(`📋 预警阈值: ${stockAlert.stockThreshold || 50} 个`);
    console.log(`📋 预警频率: ${stockAlert.alertFrequency || 'daily'}`);
    console.log(`📋 邮件通知: ${stockAlert.alertByEmail ? '✅ 启用' : '❌ 禁用'}`);
    console.log(`📋 短信通知: ${stockAlert.alertBySMS ? '✅ 启用' : '❌ 禁用'}`);
    console.log(`📋 邮件接收人: ${stockAlert.emailRecipients || '未设置'}`);
    console.log(`📋 短信接收人: ${stockAlert.smsRecipients || '未设置'}`);
    console.log(`📋 上次通知: ${stockAlert.lastAlertTime || '从未'}`);
    
    // 判断是否会触发预警
    const willTrigger = stockAlert.enableStockAlert && currentStock <= (stockAlert.stockThreshold || 50);
    console.log(`📋 预警状态: ${willTrigger ? '🚨 会触发预警' : '✅ 正常'}`);
    
    return { settings, stockAlert, currentStock };
}

// 更新配置
function updateConfig(updates) {
    const settings = loadSettings();
    if (!settings.stockAlert) {
        settings.stockAlert = {};
    }
    
    Object.assign(settings.stockAlert, updates);
    
    if (saveSettings(settings)) {
        console.log('✅ 配置已更新');
        return true;
    } else {
        console.log('❌ 配置更新失败');
        return false;
    }
}

// 主菜单
function showMenu() {
    console.log('\n🔧 库存预警配置管理');
    console.log('================================');
    console.log('1. 查看当前配置');
    console.log('2. 启用/禁用预警');
    console.log('3. 设置预警阈值');
    console.log('4. 设置预警频率');
    console.log('5. 设置通知方式');
    console.log('6. 设置接收人');
    console.log('7. 重置上次通知时间');
    console.log('8. 快速修复（推荐设置）');
    console.log('0. 退出');
    console.log('================================');
}

// 处理用户选择
function handleChoice(choice, rl) {
    const { settings, stockAlert, currentStock } = showCurrentConfig();
    
    switch (choice.trim()) {
        case '1':
            // 已经在showCurrentConfig中显示了
            showMenu();
            break;
            
        case '2':
            rl.question(`当前预警状态: ${stockAlert.enableStockAlert ? '启用' : '禁用'}，是否切换？(y/N): `, (answer) => {
                if (answer.toLowerCase() === 'y') {
                    updateConfig({ enableStockAlert: !stockAlert.enableStockAlert });
                    console.log(`✅ 预警已${!stockAlert.enableStockAlert ? '启用' : '禁用'}`);
                }
                showMenu();
            });
            return;
            
        case '3':
            rl.question(`当前阈值: ${stockAlert.stockThreshold || 50}，请输入新阈值: `, (answer) => {
                const threshold = parseInt(answer);
                if (!isNaN(threshold) && threshold >= 0) {
                    updateConfig({ stockThreshold: threshold });
                    console.log(`✅ 预警阈值已设置为 ${threshold}`);
                } else {
                    console.log('❌ 无效的阈值');
                }
                showMenu();
            });
            return;
            
        case '4':
            console.log('预警频率选项:');
            console.log('1. realtime - 实时');
            console.log('2. hourly - 每小时');
            console.log('3. daily - 每天');
            console.log('4. weekly - 每周');
            rl.question('请选择频率 (1-4): ', (answer) => {
                const frequencies = ['', 'realtime', 'hourly', 'daily', 'weekly'];
                const freq = frequencies[parseInt(answer)];
                if (freq) {
                    updateConfig({ alertFrequency: freq });
                    console.log(`✅ 预警频率已设置为 ${freq}`);
                } else {
                    console.log('❌ 无效选择');
                }
                showMenu();
            });
            return;
            
        case '5':
            rl.question(`邮件通知 (当前: ${stockAlert.alertByEmail ? '启用' : '禁用'}) 是否启用？(y/N): `, (emailAnswer) => {
                rl.question(`短信通知 (当前: ${stockAlert.alertBySMS ? '启用' : '禁用'}) 是否启用？(y/N): `, (smsAnswer) => {
                    updateConfig({
                        alertByEmail: emailAnswer.toLowerCase() === 'y',
                        alertBySMS: smsAnswer.toLowerCase() === 'y'
                    });
                    console.log('✅ 通知方式已更新');
                    showMenu();
                });
            });
            return;
            
        case '6':
            rl.question(`邮件接收人 (当前: ${stockAlert.emailRecipients || '未设置'}): `, (email) => {
                rl.question(`短信接收人 (当前: ${stockAlert.smsRecipients || '未设置'}): `, (sms) => {
                    const updates = {};
                    if (email.trim()) updates.emailRecipients = email.trim();
                    if (sms.trim()) updates.smsRecipients = sms.trim();
                    updateConfig(updates);
                    console.log('✅ 接收人已更新');
                    showMenu();
                });
            });
            return;
            
        case '7':
            updateConfig({ lastAlertTime: null });
            console.log('✅ 上次通知时间已重置');
            showMenu();
            break;
            
        case '8':
            console.log('🔧 应用推荐设置...');
            const recommendedSettings = {
                enableStockAlert: true,
                stockThreshold: Math.max(5, currentStock - 2), // 设置为当前库存-2，最少5
                alertFrequency: 'daily',
                alertByEmail: true,
                alertBySMS: true,
                alertBySystem: true
            };
            updateConfig(recommendedSettings);
            console.log('✅ 已应用推荐设置:');
            console.log(`   - 预警阈值: ${recommendedSettings.stockThreshold}`);
            console.log(`   - 预警频率: 每天一次`);
            console.log(`   - 通知方式: 邮件+短信`);
            showMenu();
            break;
            
        case '0':
            console.log('👋 再见！');
            rl.close();
            return;
            
        default:
            console.log('❌ 无效选择');
            showMenu();
            break;
    }
}

// 主函数
function main() {
    console.log('🚀 UPC管理系统 - 库存预警配置管理工具');
    
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    
    showCurrentConfig();
    showMenu();
    
    rl.on('line', (input) => {
        handleChoice(input, rl);
    });
    
    rl.on('close', () => {
        console.log('\n程序已退出');
        process.exit(0);
    });
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    showCurrentConfig,
    updateConfig,
    getCurrentStock
};
