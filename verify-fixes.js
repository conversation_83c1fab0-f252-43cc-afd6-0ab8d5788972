// 验证修复结果的脚本
const fs = require('fs');
const path = require('path');

// 文件路径
const UPC_CODES_FILE = path.join(__dirname, 'data', 'upc_codes.json');
const APPLICATIONS_FILE = path.join(__dirname, 'data', 'applications.json');

function verifyFixes() {
    console.log('🔍 开始验证修复结果...');
    
    try {
        // 1. 验证UPC码ID修复
        console.log('\n📋 1. 验证UPC码ID修复...');
        const upcCodes = JSON.parse(fs.readFileSync(UPC_CODES_FILE, 'utf8'));
        console.log(`📊 总共 ${upcCodes.length} 个UPC码`);
        
        const nullIdCodes = upcCodes.filter(code => code.id === null || code.id === undefined);
        if (nullIdCodes.length === 0) {
            console.log('✅ 所有UPC码都有有效的ID');
        } else {
            console.log(`❌ 仍有 ${nullIdCodes.length} 个UPC码的ID为null`);
            nullIdCodes.forEach(code => {
                console.log(`   - UPC码: ${code.code}, ID: ${code.id}`);
            });
        }
        
        // 2. 验证申请记录修复
        console.log('\n📋 2. 验证申请记录修复...');
        const applications = JSON.parse(fs.readFileSync(APPLICATIONS_FILE, 'utf8'));
        console.log(`📊 总共 ${applications.length} 个申请记录`);
        
        // 获取所有UPC码中的request_id
        const upcRequestIds = new Set(
            upcCodes
                .filter(code => code.request_id)
                .map(code => code.request_id)
        );
        
        console.log(`📊 UPC码中包含的request_id数量: ${upcRequestIds.size}`);
        
        // 检查申请记录是否都有对应的UPC码
        let invalidCount = 0;
        applications.forEach(app => {
            const hasUPCCodes = upcRequestIds.has(app.request_id) || app.allocated_codes;
            if (!hasUPCCodes) {
                console.log(`❌ 申请记录 ${app.id} (${app.request_id}) 没有对应的UPC码`);
                invalidCount++;
            }
        });
        
        if (invalidCount === 0) {
            console.log('✅ 所有申请记录都有对应的UPC码');
        } else {
            console.log(`❌ 有 ${invalidCount} 个申请记录没有对应的UPC码`);
        }
        
        // 3. 显示一些统计信息
        console.log('\n📊 3. 统计信息...');
        
        const statusCounts = {};
        upcCodes.forEach(code => {
            statusCounts[code.status] = (statusCounts[code.status] || 0) + 1;
        });
        
        console.log('UPC码状态分布:');
        Object.entries(statusCounts).forEach(([status, count]) => {
            console.log(`   - ${status}: ${count} 个`);
        });
        
        // 4. 检查可用于测试的UPC码
        console.log('\n📋 4. 可用于测试的UPC码...');
        const availableCodes = upcCodes.filter(code => code.status === 'available');
        console.log(`📊 可用UPC码数量: ${availableCodes.length}`);
        
        if (availableCodes.length > 0) {
            console.log('前5个可用UPC码:');
            availableCodes.slice(0, 5).forEach(code => {
                console.log(`   - ID: ${code.id}, 码: ${code.code}, 用户: ${code.assigned_user || 'N/A'}`);
            });
        }
        
        console.log('\n🎉 验证完成！');
        
    } catch (error) {
        console.error('❌ 验证过程中发生错误:', error);
    }
}

// 运行验证
verifyFixes();
