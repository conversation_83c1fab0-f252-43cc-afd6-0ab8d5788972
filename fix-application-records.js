// 修复申请记录数据不一致问题的脚本
const fs = require('fs');
const path = require('path');

// 文件路径
const APPLICATIONS_FILE = path.join(__dirname, 'data', 'applications.json');
const UPC_CODES_FILE = path.join(__dirname, 'data', 'upc_codes.json');

function fixApplicationRecords() {
    console.log('🔧 开始修复申请记录数据不一致问题...');
    
    try {
        // 读取数据
        const applications = JSON.parse(fs.readFileSync(APPLICATIONS_FILE, 'utf8'));
        const upcCodes = JSON.parse(fs.readFileSync(UPC_CODES_FILE, 'utf8'));
        
        console.log(`📊 总共 ${applications.length} 个申请记录`);
        console.log(`📊 总共 ${upcCodes.length} 个UPC码`);
        
        // 获取所有UPC码中的request_id
        const upcRequestIds = new Set(
            upcCodes
                .filter(code => code.request_id)
                .map(code => code.request_id)
        );
        
        console.log(`📋 UPC码中包含的request_id数量: ${upcRequestIds.size}`);
        
        // 检查申请记录
        const validApplications = [];
        const invalidApplications = [];
        
        applications.forEach(app => {
            // 检查是否有对应的UPC码
            const hasUPCCodes = upcRequestIds.has(app.request_id);
            
            if (hasUPCCodes || app.allocated_codes) {
                validApplications.push(app);
            } else {
                invalidApplications.push(app);
                console.log(`❌ 无效申请记录: ID=${app.id}, request_id=${app.request_id}, 原因: 没有对应的UPC码`);
            }
        });
        
        console.log(`✅ 有效申请记录: ${validApplications.length} 个`);
        console.log(`❌ 无效申请记录: ${invalidApplications.length} 个`);
        
        if (invalidApplications.length === 0) {
            console.log('🎉 所有申请记录都有对应的UPC码，无需修复');
            return;
        }
        
        // 备份原文件
        const backupFile = APPLICATIONS_FILE + '.backup.' + Date.now();
        fs.copyFileSync(APPLICATIONS_FILE, backupFile);
        console.log(`💾 原文件已备份到: ${backupFile}`);
        
        // 重新分配ID，确保连续性
        validApplications.forEach((app, index) => {
            app.id = index + 1;
        });
        
        // 保存修复后的数据
        fs.writeFileSync(APPLICATIONS_FILE, JSON.stringify(validApplications, null, 2), 'utf8');
        console.log(`✅ 修复完成！删除了 ${invalidApplications.length} 个无效申请记录`);
        
        // 验证修复结果
        const verifyData = JSON.parse(fs.readFileSync(APPLICATIONS_FILE, 'utf8'));
        console.log(`🔍 验证结果: 现在有 ${verifyData.length} 个申请记录`);
        
        // 检查每个申请记录是否都有对应的UPC码
        let allValid = true;
        verifyData.forEach(app => {
            const hasUPCCodes = upcRequestIds.has(app.request_id) || app.allocated_codes;
            if (!hasUPCCodes) {
                console.log(`⚠️ 警告: 申请记录 ${app.id} 仍然没有对应的UPC码`);
                allValid = false;
            }
        });
        
        if (allValid) {
            console.log('🎉 验证通过！所有申请记录现在都有对应的UPC码');
        }
        
    } catch (error) {
        console.error('❌ 修复过程中发生错误:', error);
    }
}

// 运行修复
fixApplicationRecords();
