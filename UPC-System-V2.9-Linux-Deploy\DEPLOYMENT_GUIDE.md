# UPC管理系统 V2.9 Linux版 部署指南

## 📋 目录
- [系统要求](#系统要求)
- [快速部署](#快速部署)
- [手动部署](#手动部署)
- [配置说明](#配置说明)
- [服务管理](#服务管理)
- [故障排除](#故障排除)
- [安全配置](#安全配置)
- [性能优化](#性能优化)

## 🖥️ 系统要求

### 最低配置
- **操作系统**: CentOS 8 / RHEL 8 / Rocky Linux 8
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB 可用空间
- **网络**: 稳定的互联网连接

### 推荐配置
- **操作系统**: CentOS 8 / RHEL 8 / Rocky Linux 8
- **CPU**: 4核心或更多
- **内存**: 8GB RAM或更多
- **存储**: 50GB SSD
- **网络**: 100Mbps带宽

### 软件依赖
- **Node.js**: >= 14.0.0 (推荐 18.x LTS)
- **npm**: >= 6.0.0
- **Git**: 最新版本
- **curl/wget**: 用于下载依赖

## 🚀 快速部署

### 一键安装（推荐）

1. **下载部署包**
   ```bash
   # 解压部署包到目标目录
   cd /tmp
   tar -xzf UPC-System-V2.9-Linux-Deploy.tar.gz
   cd UPC-System-V2.9-Linux-Deploy
   ```

2. **执行一键安装**
   ```bash
   # 确保有root权限
   sudo bash install.sh
   ```

3. **等待安装完成**
   - 安装过程约需要5-10分钟
   - 脚本会自动处理所有依赖和配置
   - 安装完成后会显示访问信息

4. **访问系统**
   ```
   浏览器访问: http://YOUR_SERVER_IP:3000
   默认管理员: admin / admin123
   ```

## 🔧 手动部署

### 步骤1: 准备环境

1. **更新系统**
   ```bash
   sudo dnf update -y
   ```

2. **安装基础工具**
   ```bash
   sudo dnf install -y curl wget git unzip zip tar gzip
   sudo dnf groupinstall -y "Development Tools"
   ```

3. **安装Node.js**
   ```bash
   # 添加Node.js 18.x仓库
   curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
   
   # 安装Node.js
   sudo dnf install -y nodejs
   
   # 验证安装
   node --version
   npm --version
   ```

### 步骤2: 创建用户

```bash
# 创建专用用户
sudo useradd -m -s /bin/bash upcadmin
sudo usermod -aG wheel upcadmin

# 切换到用户目录
sudo su - upcadmin
```

### 步骤3: 部署应用

1. **创建安装目录**
   ```bash
   sudo mkdir -p /opt/upc-system
   sudo chown upcadmin:upcadmin /opt/upc-system
   ```

2. **复制文件**
   ```bash
   # 复制所有文件到安装目录
   cp -r /path/to/UPC-System-V2.9-Linux-Deploy/* /opt/upc-system/
   cd /opt/upc-system
   ```

3. **安装依赖**
   ```bash
   npm install --production
   ```

### 步骤4: 配置服务

1. **创建systemd服务文件**
   ```bash
   sudo tee /etc/systemd/system/upc-system.service > /dev/null <<EOF
   [Unit]
   Description=UPC管理系统 V2.9 Linux版
   After=network.target
   
   [Service]
   Type=simple
   User=upcadmin
   Group=upcadmin
   WorkingDirectory=/opt/upc-system
   ExecStart=/usr/bin/node simple-server.js
   Restart=always
   RestartSec=10
   Environment=NODE_ENV=production
   
   [Install]
   WantedBy=multi-user.target
   EOF
   ```

2. **启用并启动服务**
   ```bash
   sudo systemctl daemon-reload
   sudo systemctl enable upc-system
   sudo systemctl start upc-system
   ```

### 步骤5: 配置防火墙

```bash
# 开放端口
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload

# 验证规则
sudo firewall-cmd --list-ports
```

## ⚙️ 配置说明

### 环境变量配置

在 `/opt/upc-system/.env` 文件中配置：

```bash
# 服务配置
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# 日志配置
UPC_LOG_LEVEL=info
UPC_ENABLE_DEBUG=true
UPC_ENABLE_LOGGING=true

# 服务功能开关
UPC_ENABLE_EMAIL=true
UPC_ENABLE_SMS=true
UPC_ENABLE_BACKUP=true

# 数据库配置
UPC_DATA_PATH=/opt/upc-system/data

# 安全配置
UPC_SESSION_SECRET=your-secret-key-here
UPC_JWT_SECRET=your-jwt-secret-here
```

### 邮件服务配置

编辑 `email-config.js` 文件：

```javascript
module.exports = {
    host: 'smtp.your-domain.com',
    port: 587,
    secure: false,
    auth: {
        user: '<EMAIL>',
        pass: 'your-password'
    }
};
```

### 短信服务配置

编辑 `sms-config.js` 文件：

```javascript
module.exports = {
    secretId: 'your-secret-id',
    secretKey: 'your-secret-key',
    region: 'ap-beijing',
    appId: 'your-app-id',
    templateId: 'your-template-id'
};
```

## 🔄 服务管理

### 基本命令

```bash
# 启动服务
sudo systemctl start upc-system

# 停止服务
sudo systemctl stop upc-system

# 重启服务
sudo systemctl restart upc-system

# 查看状态
sudo systemctl status upc-system

# 查看日志
sudo journalctl -u upc-system -f

# 查看最近日志
sudo journalctl -u upc-system --since "1 hour ago"
```

### 日志管理

```bash
# 应用日志位置
/opt/upc-system/logs/

# 系统日志
sudo journalctl -u upc-system

# 实时日志
sudo tail -f /opt/upc-system/logs/app.log
```

## 🔍 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   # 检查服务状态
   sudo systemctl status upc-system
   
   # 查看详细错误
   sudo journalctl -u upc-system --no-pager
   
   # 检查端口占用
   sudo netstat -tlnp | grep 3000
   ```

2. **无法访问Web界面**
   ```bash
   # 检查防火墙
   sudo firewall-cmd --list-ports
   
   # 检查服务监听
   sudo ss -tlnp | grep 3000
   
   # 测试本地连接
   curl http://localhost:3000
   ```

3. **权限问题**
   ```bash
   # 检查文件权限
   ls -la /opt/upc-system/
   
   # 修复权限
   sudo chown -R upcadmin:upcadmin /opt/upc-system/
   sudo chmod +x /opt/upc-system/*.sh
   ```

### 性能问题

1. **内存使用过高**
   ```bash
   # 查看内存使用
   free -h
   top -p $(pgrep -f "node.*simple-server")
   
   # 重启服务释放内存
   sudo systemctl restart upc-system
   ```

2. **CPU使用过高**
   ```bash
   # 查看CPU使用
   top -p $(pgrep -f "node.*simple-server")
   
   # 查看进程详情
   ps aux | grep node
   ```

### 数据问题

1. **数据文件损坏**
   ```bash
   # 检查数据文件
   cd /opt/upc-system/data
   ls -la *.json
   
   # 验证JSON格式
   python3 -m json.tool upc_codes.json > /dev/null
   ```

2. **备份恢复**
   ```bash
   # 查看备份
   ls -la /opt/upc-system/backup/
   
   # 恢复备份（停止服务后）
   sudo systemctl stop upc-system
   cp /opt/upc-system/backup/latest/* /opt/upc-system/data/
   sudo systemctl start upc-system
   ```

## 🔒 安全配置

### 基础安全

1. **更新系统密钥**
   ```bash
   # 生成新的会话密钥
   openssl rand -base64 32
   
   # 更新配置文件中的密钥
   ```

2. **配置SSL/TLS（可选）**
   ```bash
   # 安装nginx作为反向代理
   sudo dnf install -y nginx
   
   # 配置SSL证书
   # 编辑 /etc/nginx/conf.d/upc-system.conf
   ```

3. **限制访问**
   ```bash
   # 仅允许特定IP访问
   sudo firewall-cmd --permanent --add-rich-rule="rule family='ipv4' source address='***********/24' port protocol='tcp' port='3000' accept"
   sudo firewall-cmd --reload
   ```

### 用户安全

1. **修改默认密码**
   - 首次登录后立即修改所有默认账户密码
   - 使用强密码策略

2. **定期备份**
   ```bash
   # 设置自动备份
   echo "0 2 * * * /opt/upc-system/backup.sh" | sudo crontab -u upcadmin -
   ```

## 📈 性能优化

### 系统优化

1. **调整文件描述符限制**
   ```bash
   echo "upcadmin soft nofile 65536" | sudo tee -a /etc/security/limits.conf
   echo "upcadmin hard nofile 65536" | sudo tee -a /etc/security/limits.conf
   ```

2. **优化内核参数**
   ```bash
   echo "net.core.somaxconn = 65535" | sudo tee -a /etc/sysctl.conf
   echo "net.ipv4.tcp_max_syn_backlog = 65535" | sudo tee -a /etc/sysctl.conf
   sudo sysctl -p
   ```

### 应用优化

1. **启用生产模式**
   ```bash
   export NODE_ENV=production
   ```

2. **配置日志轮转**
   ```bash
   sudo tee /etc/logrotate.d/upc-system > /dev/null <<EOF
   /opt/upc-system/logs/*.log {
       daily
       missingok
       rotate 30
       compress
       notifempty
       create 644 upcadmin upcadmin
   }
   EOF
   ```

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查系统日志和应用日志
3. 确认系统要求是否满足
4. 联系技术支持团队

---

**版本**: V2.9.0  
**更新日期**: 2025-07-07  
**文档版本**: 1.0
