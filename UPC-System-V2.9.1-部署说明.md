# UPC管理系统 V2.9.1 部署说明

## 📋 版本概述
- **版本**: V2.9.1
- **发布日期**: 2025年7月8日
- **包含修复**: UPC码ID生成、批量操作、页面位置保持等重要修复
- **推荐部署**: 生产环境可用

## 📦 压缩包说明

### 1. 完整备份包 (推荐用于升级)
- **文件**: `UPC-System-V2.9.1-2025-07-08T03-31-10.zip` (248 KB)
- **内容**: 完整系统 + 所有数据 + 日志
- **适用**: 现有系统升级、完整备份恢复

### 2. 部署包 (推荐用于新安装)
- **文件**: `UPC-System-V2.9.1-Deploy-2025-07-08T03-31-10.zip` (204 KB)
- **内容**: 运行文件 + 空数据结构
- **适用**: 新环境部署、干净安装

### 3. 清理版本 (开发测试用)
- **文件**: `UPC-System-V2.9.1-Clean-2025-07-08T03-31-10.zip` (178 KB)
- **内容**: 仅核心文件
- **适用**: 开发测试、最小化部署

## 🚀 快速部署指南

### Windows 环境部署

#### 方案一：使用完整备份包（升级现有系统）
```cmd
# 1. 停止现有服务
# 如果使用PM2: pm2 stop upc-system
# 如果直接运行: Ctrl+C 停止

# 2. 备份当前数据（重要！）
xcopy data data_backup_20250708 /E /I

# 3. 解压新版本
unzip UPC-System-V2.9.1-2025-07-08T03-31-10.zip
cd UPC-System-V2.9.1

# 4. 安装依赖
npm install

# 5. 启动服务
npm start
```

#### 方案二：使用部署包（全新安装）
```cmd
# 1. 解压部署包
unzip UPC-System-V2.9.1-Deploy-2025-07-08T03-31-10.zip
cd UPC-System-V2.9.1-Deploy

# 2. 安装依赖
npm install

# 3. 启动服务
npm start
```

### Linux 环境部署

#### 方案一：系统服务部署（推荐）
```bash
# 1. 解压部署包
unzip UPC-System-V2.9.1-Deploy-2025-07-08T03-31-10.zip
cd UPC-System-V2.9.1-Deploy

# 2. 运行安装脚本
chmod +x install.sh
sudo ./install.sh

# 3. 启动并启用服务
sudo systemctl start upc-system
sudo systemctl enable upc-system

# 4. 检查服务状态
sudo systemctl status upc-system
```

#### 方案二：手动部署
```bash
# 1. 解压文件
unzip UPC-System-V2.9.1-Deploy-2025-07-08T03-31-10.zip
cd UPC-System-V2.9.1-Deploy

# 2. 安装依赖
npm install

# 3. 后台运行
nohup node simple-server.js > upc-system.log 2>&1 &

# 或使用PM2
npm install -g pm2
pm2 start simple-server.js --name upc-system
pm2 startup
pm2 save
```

## 🔧 升级现有系统

### 升级步骤
```bash
# 1. 停止现有服务
sudo systemctl stop upc-system
# 或 pm2 stop upc-system

# 2. 备份当前数据（重要！）
cp -r data data_backup_$(date +%Y%m%d_%H%M%S)

# 3. 备份当前系统
cp -r . ../upc-system-backup-$(date +%Y%m%d_%H%M%S)

# 4. 解压新版本到临时目录
unzip UPC-System-V2.9.1-2025-07-08T03-31-10.zip -d /tmp/

# 5. 替换程序文件（保留数据）
cp /tmp/UPC-System-V2.9.1/simple-server.js .
cp /tmp/UPC-System-V2.9.1/public/index.html public/
cp /tmp/UPC-System-V2.9.1/*.js .
cp /tmp/UPC-System-V2.9.1/package.json .

# 6. 更新依赖
npm install

# 7. 启动服务
sudo systemctl start upc-system

# 8. 验证升级
curl http://localhost:3001/api/system/status
```

## ⚙️ 配置说明

### 系统配置文件
- **主配置**: `system_settings.json`
- **服务配置**: `upc-system.service`
- **包配置**: `package.json`

### 重要配置项
```json
{
  "server": {
    "port": 3001,
    "host": "0.0.0.0"
  },
  "security": {
    "sessionTimeout": 120,
    "maxLoginAttempts": 5
  },
  "email": {
    "provider": "custom",
    "host": "smtp.163.com",
    "port": 25
  },
  "stockAlert": {
    "enableStockAlert": true,
    "stockThreshold": 50
  }
}
```

## 🔍 验证部署

### 1. 服务状态检查
```bash
# Linux系统服务
sudo systemctl status upc-system

# PM2进程
pm2 status

# 端口检查
netstat -tlnp | grep 3001
```

### 2. 功能验证
```bash
# 系统状态API
curl http://localhost:3001/api/system/status

# 健康检查
curl http://localhost:3001/

# 登录测试
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

### 3. 日志检查
```bash
# 系统日志
sudo journalctl -u upc-system -f

# 应用日志
tail -f logs/$(date +%Y-%m-%d).log

# PM2日志
pm2 logs upc-system
```

## 🛠️ 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 查找占用进程
sudo lsof -i :3001
# 或
sudo netstat -tlnp | grep 3001

# 停止占用进程
sudo kill -9 <PID>
```

#### 2. 权限问题
```bash
# 修改文件权限
sudo chown -R $USER:$USER .
chmod +x *.sh

# 修改日志目录权限
sudo mkdir -p logs
sudo chown -R $USER:$USER logs
```

#### 3. 依赖安装失败
```bash
# 清理缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

#### 4. 服务启动失败
```bash
# 检查配置文件
node -c simple-server.js

# 手动启动查看错误
node simple-server.js

# 检查系统资源
free -h
df -h
```

## 📊 性能优化

### 系统优化
```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化内核参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
sysctl -p
```

### 应用优化
- 定期清理日志文件
- 监控内存使用情况
- 定期备份数据
- 监控磁盘空间

## 🔒 安全建议

### 1. 修改默认密码
```bash
# 登录系统后立即修改admin密码
# 访问: http://localhost:3001
# 用户管理 -> 修改密码
```

### 2. 配置防火墙
```bash
# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=3001/tcp
sudo firewall-cmd --reload

# Ubuntu
sudo ufw allow 3001/tcp
```

### 3. 启用HTTPS（可选）
```bash
# 使用nginx反向代理
# 配置SSL证书
# 修改系统配置
```

## 📞 技术支持

### 联系方式
- **技术支持**: 系统管理员
- **文档**: 查看系统内置帮助
- **日志**: 查看 `logs/` 目录

### 备份建议
- **数据备份**: 每日自动备份 `data/` 目录
- **系统备份**: 每周完整系统备份
- **配置备份**: 修改配置前备份

---

**重要提醒**: 
1. 升级前务必备份数据
2. 在测试环境验证后再部署生产环境
3. 监控系统运行状态
4. 定期检查日志文件
