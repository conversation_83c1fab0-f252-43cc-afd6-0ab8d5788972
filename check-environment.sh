#!/bin/bash

# UPC管理系统 V3.0 环境检查脚本
# 用于检查 CentOS 8 系统环境是否满足安装要求

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 检查结果
CHECKS_PASSED=0
CHECKS_FAILED=0
WARNINGS=0

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((CHECKS_PASSED++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
    ((WARNINGS++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((CHECKS_FAILED++))
}

log_check() {
    echo -e "${PURPLE}[CHECK]${NC} $1"
}

# 显示标题
show_header() {
    clear
    echo -e "${CYAN}"
    echo "=================================================================="
    echo "           UPC管理系统 V3.0 环境检查"
    echo "=================================================================="
    echo -e "${NC}"
    echo ""
}

# 检查操作系统
check_os() {
    log_check "检查操作系统..."
    
    if [[ -f /etc/os-release ]]; then
        source /etc/os-release
        echo "  系统: $PRETTY_NAME"
        
        if [[ "$ID" == "centos" && "$VERSION_ID" == "8" ]]; then
            log_success "操作系统: CentOS 8 (推荐)"
        elif [[ "$ID" == "centos" ]]; then
            log_warning "操作系统: CentOS $VERSION_ID (建议使用 CentOS 8)"
        elif [[ "$ID" == "rhel" || "$ID" == "rocky" || "$ID" == "almalinux" ]]; then
            log_warning "操作系统: $PRETTY_NAME (兼容，但建议使用 CentOS 8)"
        else
            log_error "操作系统: $PRETTY_NAME (不推荐，可能存在兼容性问题)"
        fi
    else
        log_error "无法识别操作系统"
    fi
}

# 检查用户权限
check_privileges() {
    log_check "检查用户权限..."
    
    if [[ $EUID -eq 0 ]]; then
        log_success "用户权限: root (满足要求)"
    else
        log_error "用户权限: 非root用户 (需要root权限进行安装)"
    fi
}

# 检查网络连接
check_network() {
    log_check "检查网络连接..."
    
    if ping -c 1 8.8.8.8 &> /dev/null; then
        log_success "网络连接: 正常"
    else
        log_error "网络连接: 失败 (安装需要网络连接)"
    fi
    
    # 检查DNS解析
    if nslookup google.com &> /dev/null; then
        log_success "DNS解析: 正常"
    else
        log_warning "DNS解析: 可能存在问题"
    fi
}

# 检查系统资源
check_resources() {
    log_check "检查系统资源..."
    
    # 检查内存
    MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    echo "  内存: ${MEMORY_GB}GB"
    if [[ $MEMORY_GB -ge 2 ]]; then
        log_success "内存: ${MEMORY_GB}GB (满足要求)"
    elif [[ $MEMORY_GB -ge 1 ]]; then
        log_warning "内存: ${MEMORY_GB}GB (最低要求，建议2GB以上)"
    else
        log_error "内存: ${MEMORY_GB}GB (不足，至少需要1GB)"
    fi
    
    # 检查磁盘空间
    DISK_GB=$(df -BG / | awk 'NR==2 {print $4}' | sed 's/G//')
    echo "  可用磁盘空间: ${DISK_GB}GB"
    if [[ $DISK_GB -ge 5 ]]; then
        log_success "磁盘空间: ${DISK_GB}GB (满足要求)"
    elif [[ $DISK_GB -ge 2 ]]; then
        log_warning "磁盘空间: ${DISK_GB}GB (最低要求，建议5GB以上)"
    else
        log_error "磁盘空间: ${DISK_GB}GB (不足，至少需要2GB)"
    fi
    
    # 检查CPU
    CPU_CORES=$(nproc)
    echo "  CPU核心数: $CPU_CORES"
    if [[ $CPU_CORES -ge 2 ]]; then
        log_success "CPU: ${CPU_CORES}核心 (满足要求)"
    else
        log_warning "CPU: ${CPU_CORES}核心 (最低要求，建议2核心以上)"
    fi
}

# 检查必要的命令
check_commands() {
    log_check "检查系统命令..."
    
    local commands=("curl" "wget" "unzip" "tar" "systemctl")
    
    for cmd in "${commands[@]}"; do
        if command -v $cmd &> /dev/null; then
            log_success "命令: $cmd 已安装"
        else
            log_warning "命令: $cmd 未安装 (安装脚本会自动安装)"
        fi
    done
}

# 检查Node.js
check_nodejs() {
    log_check "检查Node.js..."
    
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        echo "  Node.js版本: $NODE_VERSION"
        
        # 提取主版本号
        MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        
        if [[ $MAJOR_VERSION -ge 18 ]]; then
            log_success "Node.js: $NODE_VERSION (推荐版本)"
        elif [[ $MAJOR_VERSION -ge 14 ]]; then
            log_warning "Node.js: $NODE_VERSION (满足要求，建议升级到18+)"
        else
            log_error "Node.js: $NODE_VERSION (版本过低，需要14+)"
        fi
        
        # 检查npm
        if command -v npm &> /dev/null; then
            NPM_VERSION=$(npm --version)
            echo "  npm版本: $NPM_VERSION"
            log_success "npm: $NPM_VERSION"
        else
            log_error "npm: 未安装"
        fi
    else
        log_warning "Node.js: 未安装 (安装脚本会自动安装)"
    fi
}

# 检查端口占用
check_ports() {
    log_check "检查端口占用..."
    
    local ports=(3001 80 443)
    
    for port in "${ports[@]}"; do
        if netstat -tuln 2>/dev/null | grep ":$port " &> /dev/null; then
            if [[ $port -eq 3001 ]]; then
                log_error "端口 $port: 已被占用 (系统默认端口)"
            else
                log_warning "端口 $port: 已被占用 (可选端口)"
            fi
        else
            log_success "端口 $port: 可用"
        fi
    done
}

# 检查防火墙
check_firewall() {
    log_check "检查防火墙..."
    
    if systemctl is-active --quiet firewalld; then
        log_success "防火墙: firewalld 运行中"
        
        # 检查端口规则
        if firewall-cmd --list-ports 2>/dev/null | grep -q "3001/tcp"; then
            log_success "防火墙规则: 端口3001已开放"
        else
            log_warning "防火墙规则: 端口3001未开放 (安装脚本会自动配置)"
        fi
    elif systemctl is-active --quiet iptables; then
        log_warning "防火墙: iptables 运行中 (需要手动配置端口)"
    else
        log_warning "防火墙: 未运行 (建议启用防火墙)"
    fi
}

# 检查SELinux
check_selinux() {
    log_check "检查SELinux..."
    
    if command -v getenforce &> /dev/null; then
        SELINUX_STATUS=$(getenforce)
        echo "  SELinux状态: $SELINUX_STATUS"
        
        case $SELINUX_STATUS in
            "Enforcing")
                log_warning "SELinux: 强制模式 (可能需要配置策略)"
                ;;
            "Permissive")
                log_success "SELinux: 宽松模式 (推荐)"
                ;;
            "Disabled")
                log_success "SELinux: 已禁用"
                ;;
            *)
                log_warning "SELinux: 未知状态"
                ;;
        esac
    else
        log_success "SELinux: 未安装"
    fi
}

# 显示检查结果
show_results() {
    echo ""
    echo -e "${CYAN}"
    echo "=================================================================="
    echo "                    检查结果汇总"
    echo "=================================================================="
    echo -e "${NC}"
    echo ""
    
    echo -e "${GREEN}✅ 通过检查: $CHECKS_PASSED${NC}"
    echo -e "${YELLOW}⚠️  警告项目: $WARNINGS${NC}"
    echo -e "${RED}❌ 失败检查: $CHECKS_FAILED${NC}"
    echo ""
    
    if [[ $CHECKS_FAILED -eq 0 ]]; then
        echo -e "${GREEN}🎉 系统环境满足安装要求！${NC}"
        echo -e "${GREEN}📋 可以继续执行安装脚本${NC}"
        
        if [[ $WARNINGS -gt 0 ]]; then
            echo -e "${YELLOW}⚠️  请注意警告项目，可能影响系统性能${NC}"
        fi
    else
        echo -e "${RED}❌ 系统环境不满足安装要求${NC}"
        echo -e "${RED}📋 请解决失败项目后重新检查${NC}"
    fi
    
    echo ""
    echo -e "${BLUE}📞 技术支持: 深圳速拓电子商务有限公司${NC}"
    echo ""
}

# 主函数
main() {
    show_header
    
    check_os
    check_privileges
    check_network
    check_resources
    check_commands
    check_nodejs
    check_ports
    check_firewall
    check_selinux
    
    show_results
}

# 运行检查
main "$@"
