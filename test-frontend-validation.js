#!/usr/bin/env node

// 测试前端upcId验证修复
// 模拟前端可能遇到的各种数据状态

const http = require('http');

console.log('🧪 测试前端upcId验证修复');
console.log('=====================================');

// HTTP请求工具函数
function makeRequest(options) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const result = {
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data ? JSON.parse(data) : null
                    };
                    resolve(result);
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data,
                        parseError: error.message
                    });
                }
            });
        });
        
        req.on('error', reject);
        req.setTimeout(10000, () => {
            req.destroy();
            reject(new Error('请求超时'));
        });
        
        if (options.body) {
            req.write(options.body);
        }
        req.end();
    });
}

// 登录函数
async function login() {
    console.log('🔍 用户登录...');
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            username: 'manager',
            password: 'Manager@2025'
        })
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        console.log(`✅ 登录成功: ${response.data.user.name} (${response.data.user.role})`);
        return response.data.sessionId;
    } else {
        throw new Error(`登录失败: ${response.data?.message || '未知错误'}`);
    }
}

// 模拟前端markAsUsed函数的验证逻辑
function validateUpcId(record) {
    console.log(`🔍 验证记录: ${record.code}, upcId: ${record.upcId} (${typeof record.upcId})`);
    
    // 前端验证逻辑（修复后）
    if (!record.upcId || record.upcId === null || record.upcId === undefined) {
        console.log(`❌ 前端验证失败: upcId无效`);
        return {
            valid: false,
            error: `无法标记已使用：UPC码 ${record.code} 缺少有效的ID信息，请刷新页面后重试`
        };
    }
    
    console.log(`✅ 前端验证通过`);
    return { valid: true };
}

// 测试各种记录状态
async function testRecordValidation() {
    console.log('\n🧪 测试前端记录验证...');
    
    const testRecords = [
        {
            id: 1,
            code: '234567890129',
            upcId: 28,
            status: 'reusable',
            description: '正常记录 - 有效upcId'
        },
        {
            id: 2,
            code: '123456789012',
            upcId: null,
            status: 'reusable',
            description: '异常记录 - null upcId'
        },
        {
            id: 3,
            code: '987654321098',
            upcId: undefined,
            status: 'reusable',
            description: '异常记录 - undefined upcId'
        },
        {
            id: 4,
            code: '555666777888',
            // 没有upcId字段
            status: 'reusable',
            description: '异常记录 - 缺少upcId字段'
        },
        {
            id: 5,
            code: '111222333444',
            upcId: 0,
            status: 'reusable',
            description: '边界情况 - upcId为0'
        },
        {
            id: 6,
            code: '999888777666',
            upcId: '',
            status: 'reusable',
            description: '异常记录 - 空字符串upcId'
        }
    ];
    
    console.log(`\n📊 测试 ${testRecords.length} 种记录状态:`);
    
    let passCount = 0;
    let failCount = 0;
    
    for (const record of testRecords) {
        console.log(`\n🔍 测试: ${record.description}`);
        
        const validation = validateUpcId(record);
        
        if (validation.valid) {
            console.log(`✅ 验证通过，可以调用API`);
            passCount++;
        } else {
            console.log(`❌ 验证失败: ${validation.error}`);
            failCount++;
        }
    }
    
    console.log(`\n📊 验证结果统计:`);
    console.log(`   通过: ${passCount}`);
    console.log(`   失败: ${failCount}`);
    console.log(`   总计: ${testRecords.length}`);
    
    return { passCount, failCount, total: testRecords.length };
}

// 测试实际API调用
async function testApiCalls(sessionId) {
    console.log('\n🧪 测试实际API调用...');
    
    // 只测试有效的记录
    const validRecord = {
        code: '234567890129',
        upcId: 28
    };
    
    console.log(`🎯 测试有效记录: ${validRecord.code} (ID: ${validRecord.upcId})`);
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc/update',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${sessionId}`
        },
        body: JSON.stringify({
            upcId: validRecord.upcId,
            updates: {
                status: 'invalid',
                notes: '前端验证测试标记为已使用'
            }
        })
    };
    
    try {
        const response = await makeRequest(options);
        
        if (response.statusCode === 200 && response.data?.success) {
            console.log(`✅ API调用成功: ${validRecord.code}`);
            return true;
        } else {
            console.log(`❌ API调用失败: ${response.data?.message || '未知错误'}`);
            return false;
        }
    } catch (error) {
        console.log(`❌ API调用异常: ${error.message}`);
        return false;
    }
}

// 主测试函数
async function runTests() {
    try {
        console.log('开始前端验证修复测试...\n');
        
        // 登录
        const sessionId = await login();
        
        // 测试前端验证逻辑
        const validationResults = await testRecordValidation();
        
        // 测试实际API调用
        const apiSuccess = await testApiCalls(sessionId);
        
        console.log('\n📊 最终测试报告');
        console.log('=====================================');
        console.log(`前端验证测试:`);
        console.log(`   通过: ${validationResults.passCount}`);
        console.log(`   失败: ${validationResults.failCount}`);
        console.log(`   总计: ${validationResults.total}`);
        console.log(`API调用测试: ${apiSuccess ? '✅ 成功' : '❌ 失败'}`);
        
        const overallSuccess = validationResults.failCount > 0 && apiSuccess;
        console.log(`\n总体结果: ${overallSuccess ? '✅ 修复有效' : '❌ 需要进一步调试'}`);
        
        if (overallSuccess) {
            console.log('\n🎉 前端验证修复成功！');
            console.log('现在前端会在调用API前验证upcId，避免"UPC码不存在"错误。');
        }
        
    } catch (error) {
        console.log(`❌ 测试失败: ${error.message}`);
        process.exit(1);
    }
}

// 运行测试
runTests();
