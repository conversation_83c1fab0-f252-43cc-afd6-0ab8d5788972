// UPC管理系统 V3.0 服务验证脚本
const http = require('http');

// 配置
const CONFIG = {
    serverUrl: 'http://localhost:3001',
    timeout: 5000
};

// 验证结果
const results = {
    services: {},
    features: {},
    overall: false
};

// 发送HTTP请求
function makeRequest(path, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
        const url = new URL(path, CONFIG.serverUrl);
        const options = {
            hostname: url.hostname,
            port: url.port,
            path: url.pathname + url.search,
            method: method,
            timeout: CONFIG.timeout,
            headers: {
                'Content-Type': 'application/json'
            }
        };

        const req = http.request(options, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                try {
                    const parsed = JSON.parse(responseData);
                    resolve({ status: res.statusCode, data: parsed });
                } catch (error) {
                    resolve({ status: res.statusCode, data: responseData });
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.on('timeout', () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });

        if (data) {
            req.write(JSON.stringify(data));
        }
        
        req.end();
    });
}

// 验证系统基本信息
async function verifySystemInfo() {
    console.log('🔍 验证系统基本信息...');
    
    try {
        const response = await makeRequest('/api/system/info');
        if (response.status === 200) {
            console.log('✅ 系统信息API正常');
            console.log(`   版本: ${response.data.version || 'V3.0'}`);
            console.log(`   状态: ${response.data.status || '运行中'}`);
            results.services.systemInfo = true;
        } else {
            console.log('❌ 系统信息API异常');
            results.services.systemInfo = false;
        }
    } catch (error) {
        console.log('❌ 系统信息API连接失败:', error.message);
        results.services.systemInfo = false;
    }
}

// 验证健康检查
async function verifyHealthCheck() {
    console.log('🔍 验证健康检查...');
    
    try {
        const response = await makeRequest('/api/health');
        if (response.status === 200) {
            console.log('✅ 健康检查正常');
            results.services.health = true;
        } else {
            console.log('❌ 健康检查异常');
            results.services.health = false;
        }
    } catch (error) {
        console.log('❌ 健康检查连接失败:', error.message);
        results.services.health = false;
    }
}

// 验证邮件服务
async function verifyEmailService() {
    console.log('🔍 验证邮件服务...');
    
    try {
        const response = await makeRequest('/api/test/email');
        if (response.status === 200 && response.data.success) {
            console.log('✅ 邮件服务正常');
            results.services.email = true;
        } else {
            console.log('⚠️ 邮件服务配置存在问题');
            results.services.email = false;
        }
    } catch (error) {
        console.log('❌ 邮件服务验证失败:', error.message);
        results.services.email = false;
    }
}

// 验证短信服务
async function verifySMSService() {
    console.log('🔍 验证短信服务...');
    
    try {
        const response = await makeRequest('/api/test/sms');
        if (response.status === 200 && response.data.success) {
            console.log('✅ 短信服务正常');
            results.services.sms = true;
        } else {
            console.log('⚠️ 短信服务配置存在问题');
            results.services.sms = false;
        }
    } catch (error) {
        console.log('❌ 短信服务验证失败:', error.message);
        results.services.sms = false;
    }
}

// 验证备份功能
async function verifyBackupService() {
    console.log('🔍 验证备份功能...');
    
    try {
        const response = await makeRequest('/api/backup/status');
        if (response.status === 200) {
            console.log('✅ 备份功能正常');
            results.services.backup = true;
        } else {
            console.log('⚠️ 备份功能可能存在问题');
            results.services.backup = false;
        }
    } catch (error) {
        console.log('❌ 备份功能验证失败:', error.message);
        results.services.backup = false;
    }
}

// 验证日志服务
async function verifyLogService() {
    console.log('🔍 验证日志服务...');
    
    try {
        const response = await makeRequest('/api/logs/recent');
        if (response.status === 200) {
            console.log('✅ 日志服务正常');
            results.services.logs = true;
        } else {
            console.log('⚠️ 日志服务可能存在问题');
            results.services.logs = false;
        }
    } catch (error) {
        console.log('❌ 日志服务验证失败:', error.message);
        results.services.logs = false;
    }
}

// 验证核心功能
async function verifyCoreFeatures() {
    console.log('🔍 验证核心功能...');
    
    // 验证UPC码管理
    try {
        const response = await makeRequest('/api/upc/list?page=1&limit=5');
        if (response.status === 200) {
            console.log('✅ UPC码管理功能正常');
            results.features.upcManagement = true;
        } else {
            console.log('❌ UPC码管理功能异常');
            results.features.upcManagement = false;
        }
    } catch (error) {
        console.log('❌ UPC码管理功能验证失败:', error.message);
        results.features.upcManagement = false;
    }
    
    // 验证申请管理
    try {
        const response = await makeRequest('/api/applications/list?page=1&limit=5');
        if (response.status === 200) {
            console.log('✅ 申请管理功能正常');
            results.features.applicationManagement = true;
        } else {
            console.log('❌ 申请管理功能异常');
            results.features.applicationManagement = false;
        }
    } catch (error) {
        console.log('❌ 申请管理功能验证失败:', error.message);
        results.features.applicationManagement = false;
    }
    
    // 验证回收管理
    try {
        const response = await makeRequest('/api/recycle/list?page=1&limit=5');
        if (response.status === 200) {
            console.log('✅ 回收管理功能正常');
            results.features.recycleManagement = true;
        } else {
            console.log('❌ 回收管理功能异常');
            results.features.recycleManagement = false;
        }
    } catch (error) {
        console.log('❌ 回收管理功能验证失败:', error.message);
        results.features.recycleManagement = false;
    }
}

// 显示验证结果
function showResults() {
    console.log('\n' + '='.repeat(60));
    console.log('🎯 UPC管理系统 V3.0 服务验证结果');
    console.log('='.repeat(60));
    
    console.log('\n📋 服务状态:');
    Object.entries(results.services).forEach(([service, status]) => {
        const icon = status ? '✅' : '❌';
        const serviceName = {
            systemInfo: '系统信息',
            health: '健康检查',
            email: '邮件服务',
            sms: '短信服务',
            backup: '备份功能',
            logs: '日志服务'
        }[service] || service;
        console.log(`   ${icon} ${serviceName}: ${status ? '正常' : '异常'}`);
    });
    
    console.log('\n🔧 功能状态:');
    Object.entries(results.features).forEach(([feature, status]) => {
        const icon = status ? '✅' : '❌';
        const featureName = {
            upcManagement: 'UPC码管理',
            applicationManagement: '申请管理',
            recycleManagement: '回收管理'
        }[feature] || feature;
        console.log(`   ${icon} ${featureName}: ${status ? '正常' : '异常'}`);
    });
    
    // 计算总体状态
    const serviceCount = Object.keys(results.services).length;
    const serviceSuccess = Object.values(results.services).filter(Boolean).length;
    const featureCount = Object.keys(results.features).length;
    const featureSuccess = Object.values(results.features).filter(Boolean).length;
    
    const totalCount = serviceCount + featureCount;
    const totalSuccess = serviceSuccess + featureSuccess;
    const successRate = ((totalSuccess / totalCount) * 100).toFixed(1);
    
    console.log('\n📊 总体状态:');
    console.log(`   成功率: ${successRate}% (${totalSuccess}/${totalCount})`);
    console.log(`   服务: ${serviceSuccess}/${serviceCount} 正常`);
    console.log(`   功能: ${featureSuccess}/${featureCount} 正常`);
    
    if (successRate >= 80) {
        console.log('\n🎉 系统状态良好，可以正常使用！');
        results.overall = true;
    } else {
        console.log('\n⚠️ 系统存在问题，建议检查异常项目');
        results.overall = false;
    }
    
    console.log('\n🌐 访问地址: http://43.136.13.42:3001');
    console.log('👤 默认账户:');
    console.log('   管理员: admin / admin123');
    console.log('   经理: manager / Manager@2025');
    console.log('   操作员: operator / Operator@2025');
    
    console.log('\n📞 技术支持: 深圳速拓电子商务有限公司');
    console.log('='.repeat(60));
}

// 主验证函数
async function runVerification() {
    console.log('🚀 开始验证 UPC管理系统 V3.0 服务状态...\n');
    
    await verifySystemInfo();
    await verifyHealthCheck();
    await verifyEmailService();
    await verifySMSService();
    await verifyBackupService();
    await verifyLogService();
    await verifyCoreFeatures();
    
    showResults();
    
    return results.overall;
}

// 运行验证
if (require.main === module) {
    runVerification().then(success => {
        process.exit(success ? 0 : 1);
    }).catch(error => {
        console.error('❌ 验证过程出错:', error);
        process.exit(1);
    });
}

module.exports = { runVerification };
