[2025-07-07T14:39:25.050Z] [INFO] 尝试登录: manager
[2025-07-07T14:39:25.064Z] [SUCCESS] 登录成功: 业务经理 (manager)
[2025-07-07T14:39:25.065Z] [INFO] 获取回收历史...
[2025-07-07T14:39:25.078Z] [SUCCESS] 获取回收历史成功，记录数: 111
[2025-07-07T14:39:25.079Z] [INFO] 数据分析:
[2025-07-07T14:39:25.079Z] [INFO]   总记录数: 111
[2025-07-07T14:39:25.079Z] [INFO]   可重用记录: 20
[2025-07-07T14:39:25.080Z] [INFO]   有upcId记录: 111
[2025-07-07T14:39:25.080Z] [INFO]   无upcId记录: 0
[2025-07-07T14:39:25.080Z] [INFO] 开始测试各种标记已使用场景...
[2025-07-07T14:39:25.080Z] [INFO] 
🧪 测试场景: 正常场景 - 有效的upcId
[2025-07-07T14:39:25.081Z] [INFO] 描述: 使用有效的upcId和reusable状态的记录
[2025-07-07T14:39:25.081Z] [INFO] 测试记录: 234567890129, upcId: 28 (number)
[2025-07-07T14:39:25.084Z] [SUCCESS] ✅ 场景成功: 正常场景 - 有效的upcId
[2025-07-07T14:39:25.597Z] [INFO] 
🧪 测试场景: 错误场景1 - 无效的upcId
[2025-07-07T14:39:25.598Z] [INFO] 描述: 使用不存在的upcId
[2025-07-07T14:39:25.598Z] [INFO] 测试记录: 234567890129, upcId: 99999 (number)
[2025-07-07T14:39:25.600Z] [ERROR] ❌ 场景失败: 错误场景1 - 无效的upcId
[2025-07-07T14:39:25.600Z] [ERROR] 错误信息: UPC码不存在 (ID: 99999)，数据可能已发生变化，请刷新页面后重试
[2025-07-07T14:39:25.601Z] [ERROR] 状态码: 404
[2025-07-07T14:39:25.601Z] [ERROR] 响应数据: {
  "success": false,
  "message": "UPC码不存在 (ID: 99999)，数据可能已发生变化，请刷新页面后重试"
}
[2025-07-07T14:39:25.601Z] [ERROR] 🎯 捕获到目标错误！
[2025-07-07T14:39:25.601Z] [ERROR] 错误场景: 错误场景1 - 无效的upcId
[2025-07-07T14:39:25.601Z] [ERROR] upcId值: 99999
[2025-07-07T14:39:25.602Z] [ERROR] upcId类型: number
[2025-07-07T14:39:25.602Z] [ERROR] 请求体: {
  "upcId": 99999,
  "updates": {
    "status": "invalid",
    "notes": "调试测试标记为已使用"
  }
}
[2025-07-07T14:39:26.106Z] [INFO] 
🧪 测试场景: 错误场景2 - null upcId
[2025-07-07T14:39:26.106Z] [INFO] 描述: 使用null作为upcId
[2025-07-07T14:39:26.107Z] [INFO] 测试记录: 234567890129, upcId: null (object)
[2025-07-07T14:39:26.108Z] [ERROR] ❌ 场景失败: 错误场景2 - null upcId
[2025-07-07T14:39:26.108Z] [ERROR] 错误信息: UPC码不存在 (ID: null)，数据可能已发生变化，请刷新页面后重试
[2025-07-07T14:39:26.109Z] [ERROR] 状态码: 404
[2025-07-07T14:39:26.109Z] [ERROR] 响应数据: {
  "success": false,
  "message": "UPC码不存在 (ID: null)，数据可能已发生变化，请刷新页面后重试"
}
[2025-07-07T14:39:26.109Z] [ERROR] 🎯 捕获到目标错误！
[2025-07-07T14:39:26.109Z] [ERROR] 错误场景: 错误场景2 - null upcId
[2025-07-07T14:39:26.109Z] [ERROR] upcId值: null
[2025-07-07T14:39:26.109Z] [ERROR] upcId类型: object
[2025-07-07T14:39:26.110Z] [ERROR] 请求体: {
  "upcId": null,
  "updates": {
    "status": "invalid",
    "notes": "调试测试标记为已使用"
  }
}
[2025-07-07T14:39:26.619Z] [INFO] 
🧪 测试场景: 错误场景3 - undefined upcId
[2025-07-07T14:39:26.620Z] [INFO] 描述: 使用undefined作为upcId
[2025-07-07T14:39:26.621Z] [INFO] 测试记录: 234567890129, upcId: undefined (undefined)
[2025-07-07T14:39:26.625Z] [ERROR] ❌ 场景失败: 错误场景3 - undefined upcId
[2025-07-07T14:39:26.625Z] [ERROR] 错误信息: UPC码不存在 (ID: undefined)，数据可能已发生变化，请刷新页面后重试
[2025-07-07T14:39:26.626Z] [ERROR] 状态码: 404
[2025-07-07T14:39:26.626Z] [ERROR] 响应数据: {
  "success": false,
  "message": "UPC码不存在 (ID: undefined)，数据可能已发生变化，请刷新页面后重试"
}
[2025-07-07T14:39:26.626Z] [ERROR] 🎯 捕获到目标错误！
[2025-07-07T14:39:26.627Z] [ERROR] 错误场景: 错误场景3 - undefined upcId
[2025-07-07T14:39:26.627Z] [ERROR] upcId值: undefined
[2025-07-07T14:39:26.627Z] [ERROR] upcId类型: undefined
[2025-07-07T14:39:26.627Z] [ERROR] 请求体: {
  "updates": {
    "status": "invalid",
    "notes": "调试测试标记为已使用"
  }
}
[2025-07-07T14:39:27.130Z] [INFO] 
🧪 测试场景: 错误场景4 - 字符串upcId
[2025-07-07T14:39:27.131Z] [INFO] 描述: 使用字符串作为upcId
[2025-07-07T14:39:27.132Z] [INFO] 测试记录: 234567890129, upcId: invalid (string)
[2025-07-07T14:39:27.136Z] [ERROR] ❌ 场景失败: 错误场景4 - 字符串upcId
[2025-07-07T14:39:27.136Z] [ERROR] 错误信息: UPC码不存在 (ID: invalid)，数据可能已发生变化，请刷新页面后重试
[2025-07-07T14:39:27.137Z] [ERROR] 状态码: 404
[2025-07-07T14:39:27.137Z] [ERROR] 响应数据: {
  "success": false,
  "message": "UPC码不存在 (ID: invalid)，数据可能已发生变化，请刷新页面后重试"
}
[2025-07-07T14:39:27.137Z] [ERROR] 🎯 捕获到目标错误！
[2025-07-07T14:39:27.138Z] [ERROR] 错误场景: 错误场景4 - 字符串upcId
[2025-07-07T14:39:27.138Z] [ERROR] upcId值: invalid
[2025-07-07T14:39:27.138Z] [ERROR] upcId类型: string
[2025-07-07T14:39:27.139Z] [ERROR] 请求体: {
  "upcId": "invalid",
  "updates": {
    "status": "invalid",
    "notes": "调试测试标记为已使用"
  }
}
[2025-07-07T14:39:27.641Z] [INFO] 
📊 监控完成
[2025-07-07T14:39:27.642Z] [INFO] 详细日志已保存到: error-debug.log
