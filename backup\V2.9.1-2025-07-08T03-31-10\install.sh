#!/bin/bash

# UPC管理系统 V2.8.0 Linux自动安装脚本
# 适用于 CentOS 8, Ubuntu 18.04+, RHEL 8+, OpenCloudOS

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查root权限
if [[ $EUID -ne 0 ]]; then
    log_error "此脚本需要root权限运行，请使用 sudo ./install.sh"
    exit 1
fi

echo "=========================================="
echo "🚀 UPC管理系统 V2.8.1 Linux安装程序"
echo "   专用于服务器: 43.136.13.42"
echo "=========================================="
echo ""

# 检查系统
log_step "检查系统环境..."
if [[ -f /etc/redhat-release ]]; then
    if grep -q "OpenCloudOS" /etc/redhat-release; then
        OS="opencloudos"
        log_info "检测到OpenCloudOS系统"
    elif grep -q "CentOS" /etc/redhat-release; then
        OS="centos"
        log_info "检测到CentOS系统"
    else
        OS="rhel"
        log_info "检测到RHEL系统"
    fi
else
    log_error "此脚本专为RHEL系列系统设计"
    exit 1
fi

# 更新系统（处理版本冲突）
log_step "更新系统包..."
log_info "处理可能的包冲突..."
dnf update --allowerasing -y || {
    log_info "尝试跳过冲突包..."
    dnf update --skip-broken -y
}

# 清理Node.js冲突
log_step "清理Node.js冲突..."
dnf remove nodejs nodejs-full-i18n -y 2>/dev/null || true
dnf clean all

# 安装Node.js
log_step "安装Node.js..."
if ! dnf install -y nodejs npm; then
    log_info "系统仓库安装失败，使用NodeSource仓库..."
    curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
    dnf install -y nodejs
fi

# 验证Node.js安装
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    log_info "Node.js版本: $NODE_VERSION"

    # 检查版本是否满足要求
    NODE_MAJOR=$(echo $NODE_VERSION | cut -d'v' -f2 | cut -d'.' -f1)
    if [[ $NODE_MAJOR -lt 14 ]]; then
        log_step "Node.js版本过低，升级到18.x..."
        curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
        dnf install -y nodejs
        NODE_VERSION=$(node --version)
        log_info "升级后Node.js版本: $NODE_VERSION"
    fi
else
    log_error "Node.js安装失败"
    exit 1
fi

# 安装系统工具
log_step "安装系统工具..."
dnf install -y wget curl unzip tar firewalld net-tools

# 创建系统用户
log_step "创建系统用户..."
if ! id "upc-system" &>/dev/null; then
    useradd -r -s /bin/false -d /opt/upc-system upc-system
    log_info "创建用户: upc-system"
else
    log_info "用户已存在: upc-system"
fi

# 创建目录
log_step "创建目录结构..."
mkdir -p /opt/upc-system
mkdir -p /var/log/upc-system

# 复制文件
log_step "复制应用文件..."
cp -r ./* /opt/upc-system/
chown -R upc-system:upc-system /opt/upc-system
chmod +x /opt/upc-system/*.sh

# 初始化日志文件
log_step "初始化日志文件..."
CURRENT_DATE=$(date +%Y-%m-%d)
LOG_FILE="/opt/upc-system/logs/${CURRENT_DATE}.log"
mkdir -p /opt/upc-system/logs
sudo -u upc-system touch "$LOG_FILE"
sudo -u upc-system echo '{"timestamp":"'$(date -Iseconds)'","level":"INFO","category":"SYSTEM","message":"系统安装完成","data":{"version":"V2.8.0"}}' > "$LOG_FILE"
chown -R upc-system:upc-system /opt/upc-system/logs
log_info "✅ 日志文件初始化完成"

# 安装依赖
log_step "安装依赖包..."
cd /opt/upc-system

# 验证package.json存在
if [[ ! -f package.json ]]; then
    log_error "package.json文件不存在"
    exit 1
fi

# 安装Node.js依赖
log_info "正在安装Node.js依赖包..."
if sudo -u upc-system npm install --production --no-optional; then
    log_info "✅ Node.js依赖安装成功"
else
    log_error "❌ Node.js依赖安装失败"
    exit 1
fi

# 验证关键依赖是否安装成功
log_step "验证关键依赖..."
REQUIRED_DEPS=("express" "nodemailer" "tencentcloud-sdk-nodejs" "archiver" "node-cron")

for dep in "${REQUIRED_DEPS[@]}"; do
    if sudo -u upc-system node -e "require('$dep')" 2>/dev/null; then
        log_info "✅ $dep 依赖验证成功"
    else
        log_error "❌ $dep 依赖验证失败，尝试重新安装..."
        if sudo -u upc-system npm install "$dep" --save; then
            log_info "✅ $dep 重新安装成功"
        else
            log_error "❌ $dep 重新安装失败"
            exit 1
        fi
    fi
done

log_info "✅ 所有依赖验证完成"

# 配置防火墙
log_step "配置防火墙..."
systemctl start firewalld
systemctl enable firewalld
firewall-cmd --permanent --add-port=3001/tcp
firewall-cmd --permanent --add-port=465/tcp
firewall-cmd --permanent --add-port=587/tcp
firewall-cmd --reload
log_info "已开放端口3001, 465, 587"

# 安装系统服务
if [[ -f upc-system.service ]]; then
    log_step "安装系统服务..."
    cp upc-system.service /etc/systemd/system/
    systemctl daemon-reload
    systemctl enable upc-system

    # 启动服务并检查
    log_step "启动服务..."
    systemctl start upc-system
    sleep 3

    # 如果启动失败，尝试修复
    if ! systemctl is-active --quiet upc-system; then
        log_warn "服务启动失败，尝试修复配置..."

        # 创建简化的服务配置
        cat > /etc/systemd/system/upc-system.service << 'EOF'
[Unit]
Description=UPC Management System V2.8.0
After=network.target

[Service]
Type=simple
User=upc-system
Group=upc-system
WorkingDirectory=/opt/upc-system
ExecStart=/usr/bin/node simple-server.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3001

[Install]
WantedBy=multi-user.target
EOF

        systemctl daemon-reload
        systemctl start upc-system
        sleep 3
    fi

    log_info "系统服务已安装并启动"
fi

# 等待服务启动
log_step "等待服务启动..."
sleep 5

# 验证安装
log_step "验证安装..."
if systemctl is-active --quiet upc-system; then
    log_info "✅ 服务运行正常"
else
    log_error "❌ 服务启动失败"
    systemctl status upc-system
    exit 1
fi

if netstat -tulpn | grep -q ":3001"; then
    log_info "✅ 端口监听正常"
else
    log_error "❌ 端口监听异常"
fi

# 运行依赖检查
log_step "运行依赖检查..."
if sudo -u upc-system node check-dependencies.js; then
    log_info "✅ 依赖检查通过"
else
    log_error "❌ 依赖检查失败"
    exit 1
fi

# 运行系统测试
log_step "运行系统测试..."
if [[ -f tools/system-test.js ]]; then
    if sudo -u upc-system node tools/system-test.js; then
        log_info "✅ 系统测试通过"
    else
        log_error "❌ 系统测试失败"
    fi
else
    log_info "⚠️ 系统测试文件不存在，跳过测试"
fi

echo ""
echo "=========================================="
echo -e "${GREEN}🎉 安装完成！${NC}"
echo "=========================================="
echo ""
echo "📋 安装信息:"
echo "  - 服务器: 43.136.13.42"
echo "  - 安装目录: /opt/upc-system"
echo "  - 服务名称: upc-system"
echo "  - 运行用户: upc-system"
echo ""
echo "🌐 访问信息:"
echo "  - 访问地址: http://43.136.13.42:3001"
echo "  - 管理员账户: admin / admin123"
echo ""
echo "🔧 管理命令:"
echo "  - 查看状态: systemctl status upc-system"
echo "  - 启动服务: systemctl start upc-system"
echo "  - 停止服务: systemctl stop upc-system"
echo "  - 重启服务: systemctl restart upc-system"
echo "  - 查看日志: journalctl -u upc-system -f"
echo ""
echo "📚 文档位置:"
echo "  - 部署教程: /opt/upc-system/CentOS8-DEPLOYMENT-TUTORIAL.md"
echo "  - 运维手册: /opt/upc-system/MAINTENANCE-MANUAL.md"
echo "  - 快速参考: /opt/upc-system/QUICK-REFERENCE.md"
echo ""
echo "📧 邮件配置提醒:"
echo "  - 163邮箱请使用授权码，不是登录密码"
echo "  - 推荐使用SSL端口465"
echo ""
echo "🎉 现在可以访问系统了！"
echo ""
