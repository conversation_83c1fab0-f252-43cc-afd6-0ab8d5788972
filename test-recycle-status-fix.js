// 测试回收记录状态修复的脚本
const http = require('http');

let sessionToken = '';

// 先登录获取会话
function login(callback) {
    console.log('🔐 正在登录获取会话...');
    
    const loginData = JSON.stringify({
        username: 'admin',
        password: 'admin123'
    });
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(loginData)
        }
    };
    
    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                if (response.success && response.sessionId) {
                    sessionToken = response.sessionId;
                    console.log('✅ 登录成功，获取到会话令牌');
                    callback();
                } else {
                    console.log('❌ 登录失败:', response.message);
                }
            } catch (error) {
                console.log('❌ 解析登录响应失败:', error.message);
            }
        });
    });
    
    req.on('error', (error) => {
        console.log('❌ 登录请求失败:', error.message);
    });
    
    req.write(loginData);
    req.end();
}

// 测试回收记录状态修复
function testRecycleStatusFix() {
    console.log('\n🧪 测试回收记录状态修复...');
    
    // 步骤1：获取当前回收记录
    getRecycleRecords((records) => {
        if (records.length === 0) {
            console.log('⚠️ 没有回收记录，先创建一些测试数据');
            createTestRecycleRecord(() => {
                testInvalidStatusUpdate();
            });
        } else {
            console.log(`📊 当前有 ${records.length} 个回收记录`);
            testInvalidStatusUpdate();
        }
    });
}

// 获取回收记录
function getRecycleRecords(callback) {
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/recycle/history',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${sessionToken}`
        }
    };
    
    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                if (response.success && response.data) {
                    console.log('📋 回收记录状态统计:');
                    const statusCounts = {};
                    response.data.forEach(record => {
                        const status = record.status || '未知';
                        statusCounts[status] = (statusCounts[status] || 0) + 1;
                    });
                    
                    Object.entries(statusCounts).forEach(([status, count]) => {
                        console.log(`   - ${status}: ${count} 个`);
                    });
                    
                    callback(response.data);
                } else {
                    console.log('❌ 获取回收记录失败:', response.message);
                    callback([]);
                }
            } catch (error) {
                console.log('❌ 解析回收记录响应失败:', error.message);
                callback([]);
            }
        });
    });
    
    req.on('error', (error) => {
        console.log('❌ 获取回收记录请求失败:', error.message);
        callback([]);
    });
    
    req.end();
}

// 创建测试回收记录
function createTestRecycleRecord(callback) {
    console.log('🔧 创建测试回收记录...');
    
    // 先获取一个可用的UPC码
    getAvailableUPC((upc) => {
        if (!upc) {
            console.log('❌ 没有可用的UPC码进行测试');
            return;
        }
        
        console.log(`📋 使用UPC码 ${upc.code} (ID: ${upc.id}) 进行测试`);
        
        // 回收这个UPC码
        const recycleData = JSON.stringify({
            code: upc.code,
            reason: '测试回收记录状态修复'
        });
        
        const options = {
            hostname: 'localhost',
            port: 3001,
            path: '/api/upc/recycle',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(recycleData),
                'Authorization': `Bearer ${sessionToken}`
            }
        };
        
        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    if (response.success) {
                        console.log('✅ 测试回收记录创建成功');
                        callback();
                    } else {
                        console.log('❌ 创建测试回收记录失败:', response.message);
                    }
                } catch (error) {
                    console.log('❌ 解析回收响应失败:', error.message);
                }
            });
        });
        
        req.on('error', (error) => {
            console.log('❌ 回收请求失败:', error.message);
        });
        
        req.write(recycleData);
        req.end();
    });
}

// 获取可用的UPC码
function getAvailableUPC(callback) {
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc-codes',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${sessionToken}`
        }
    };
    
    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                if (response.success && response.data) {
                    const availableUPC = response.data.find(upc => upc.status === 'available');
                    callback(availableUPC);
                } else {
                    callback(null);
                }
            } catch (error) {
                callback(null);
            }
        });
    });
    
    req.on('error', (error) => {
        callback(null);
    });
    
    req.end();
}

// 测试invalid状态更新
function testInvalidStatusUpdate() {
    console.log('\n🔧 测试UPC码状态更新为invalid时的回收记录处理...');
    
    // 获取一个已回收的UPC码
    getRecycledUPC((upc) => {
        if (!upc) {
            console.log('❌ 没有已回收的UPC码进行测试');
            return;
        }
        
        console.log(`📋 使用已回收的UPC码 ${upc.code} (ID: ${upc.id}) 进行测试`);
        
        // 将状态更新为invalid
        const updateData = JSON.stringify({
            upcId: upc.id,
            updates: {
                status: 'invalid',
                notes: '测试状态更新为invalid'
            }
        });
        
        const options = {
            hostname: 'localhost',
            port: 3001,
            path: '/api/upc/update',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(updateData),
                'Authorization': `Bearer ${sessionToken}`
            }
        };
        
        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    if (response.success) {
                        console.log('✅ UPC码状态更新为invalid成功');
                        
                        // 等待一下，然后检查回收记录状态
                        setTimeout(() => {
                            checkRecycleRecordStatus(upc.code);
                        }, 1000);
                        
                    } else {
                        console.log('❌ UPC码状态更新失败:', response.message);
                    }
                } catch (error) {
                    console.log('❌ 解析更新响应失败:', error.message);
                }
            });
        });
        
        req.on('error', (error) => {
            console.log('❌ 更新请求失败:', error.message);
        });
        
        req.write(updateData);
        req.end();
    });
}

// 获取已回收的UPC码
function getRecycledUPC(callback) {
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc-codes',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${sessionToken}`
        }
    };
    
    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                if (response.success && response.data) {
                    const recycledUPC = response.data.find(upc => upc.status === 'recycled');
                    callback(recycledUPC);
                } else {
                    callback(null);
                }
            } catch (error) {
                callback(null);
            }
        });
    });
    
    req.on('error', (error) => {
        callback(null);
    });
    
    req.end();
}

// 检查回收记录状态
function checkRecycleRecordStatus(upcCode) {
    console.log(`\n🔍 检查UPC码 ${upcCode} 的回收记录状态...`);
    
    getRecycleRecords((records) => {
        const record = records.find(r => r.code === upcCode);
        
        if (record) {
            console.log(`📋 找到回收记录:`);
            console.log(`   UPC码: ${record.code}`);
            console.log(`   状态: ${record.status}`);
            console.log(`   创建时间: ${record.created_at || record.recycled_at}`);
            console.log(`   处理时间: ${record.processed_at || '无'}`);
            
            if (record.status === 'processed') {
                console.log('✅ 修复成功！回收记录状态正确更新为"processed"');
            } else if (record.status === 'recycled') {
                console.log('❌ 修复失败！回收记录状态仍为"recycled"，应该是"processed"');
            } else {
                console.log(`⚠️ 回收记录状态为"${record.status}"，需要确认是否正确`);
            }
        } else {
            console.log('❌ 修复失败！回收记录消失了，应该保留并标记为"processed"');
        }
        
        console.log('\n🎉 回收记录状态修复测试完成！');
    });
}

// 运行测试
console.log('🚀 开始测试回收记录状态修复...');
login(() => {
    testRecycleStatusFix();
});
