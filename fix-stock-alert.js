// 修复库存预警问题的脚本
const fs = require('fs');
const path = require('path');

console.log('🔧 开始修复库存预警问题...');

// 读取系统设置
function loadSettings() {
    try {
        const settingsPath = './system_settings.json';
        if (fs.existsSync(settingsPath)) {
            const data = fs.readFileSync(settingsPath, 'utf8');
            return JSON.parse(data);
        }
        return {};
    } catch (error) {
        console.error('读取设置失败:', error.message);
        return {};
    }
}

// 保存系统设置
function saveSettings(settings) {
    try {
        const settingsPath = './system_settings.json';
        fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2), 'utf8');
        console.log('✅ 设置已保存');
        return true;
    } catch (error) {
        console.error('保存设置失败:', error.message);
        return false;
    }
}

// 读取UPC码数据
function loadUPCCodes() {
    try {
        const upcPath = './data/upc_codes.json';
        if (fs.existsSync(upcPath)) {
            const data = fs.readFileSync(upcPath, 'utf8');
            return JSON.parse(data);
        }
        return [];
    } catch (error) {
        console.error('读取UPC码数据失败:', error.message);
        return [];
    }
}

// 保存UPC码数据
function saveUPCCodes(upcCodes) {
    try {
        const upcPath = './data/upc_codes.json';
        fs.writeFileSync(upcPath, JSON.stringify(upcCodes, null, 2), 'utf8');
        console.log('✅ UPC码数据已保存');
        return true;
    } catch (error) {
        console.error('保存UPC码数据失败:', error.message);
        return false;
    }
}

// 主修复函数
function fixStockAlert() {
    console.log('\n📊 当前系统状态:');
    
    // 读取当前设置
    const settings = loadSettings();
    const stockAlert = settings.stockAlert || {};
    
    console.log(`📋 库存预警启用: ${stockAlert.enableStockAlert ? '是' : '否'}`);
    console.log(`📋 预警阈值: ${stockAlert.stockThreshold || 50}`);
    console.log(`📋 通知频率: ${stockAlert.alertFrequency || 'daily'}`);
    console.log(`📋 上次通知: ${stockAlert.lastAlertTime || '从未'}`);
    
    // 读取UPC码数据
    const upcCodes = loadUPCCodes();
    const availableCount = upcCodes.filter(c => c.status === 'available').length;
    const threshold = parseInt(stockAlert.stockThreshold) || 50;
    
    console.log(`📋 当前可用库存: ${availableCount}`);
    console.log(`📋 预警阈值: ${threshold}`);
    console.log(`📋 是否触发预警: ${availableCount <= threshold ? '是' : '否'}`);
    
    console.log('\n🔧 修复选项:');
    console.log('1. 临时禁用库存预警');
    console.log('2. 调整预警阈值到当前库存以下');
    console.log('3. 增加一些测试UPC码到可用状态');
    console.log('4. 修改通知频率为每周一次');
    
    // 提供多种修复方案
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    
    rl.question('请选择修复方案 (1-4): ', (answer) => {
        switch (answer.trim()) {
            case '1':
                // 禁用库存预警
                settings.stockAlert = settings.stockAlert || {};
                settings.stockAlert.enableStockAlert = false;
                if (saveSettings(settings)) {
                    console.log('✅ 已禁用库存预警');
                }
                break;
                
            case '2':
                // 调整预警阈值
                const newThreshold = Math.max(0, availableCount - 1);
                settings.stockAlert = settings.stockAlert || {};
                settings.stockAlert.stockThreshold = newThreshold;
                if (saveSettings(settings)) {
                    console.log(`✅ 已调整预警阈值为 ${newThreshold}`);
                }
                break;
                
            case '3':
                // 增加测试UPC码
                const needCount = threshold - availableCount + 10; // 多加10个作为缓冲
                for (let i = 0; i < needCount; i++) {
                    const newUPC = {
                        id: `upc_temp_${Date.now()}_${i}`,
                        code: `999${String(Date.now()).slice(-8)}${String(i).padStart(3, '0')}`,
                        status: 'available',
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString(),
                        description: '临时测试UPC码 - 用于解决库存预警问题'
                    };
                    upcCodes.push(newUPC);
                }
                if (saveUPCCodes(upcCodes)) {
                    console.log(`✅ 已添加 ${needCount} 个测试UPC码`);
                }
                break;
                
            case '4':
                // 修改通知频率
                settings.stockAlert = settings.stockAlert || {};
                settings.stockAlert.alertFrequency = 'weekly';
                if (saveSettings(settings)) {
                    console.log('✅ 已修改通知频率为每周一次');
                }
                break;
                
            default:
                console.log('❌ 无效选择');
                break;
        }
        
        console.log('\n🔄 请重启服务器以应用更改:');
        console.log('systemctl restart upc-system');
        console.log('\n或者如果是开发环境，请重新启动 node simple-server.js');
        
        rl.close();
    });
}

// 如果直接运行此脚本
if (require.main === module) {
    fixStockAlert();
}

module.exports = { fixStockAlert };
