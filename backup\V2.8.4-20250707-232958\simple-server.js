// UPC管理系统正式版服务器 V2.8.4 - 回收管理数据同步完整修复版
const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

// 启动前依赖检查
function checkStartupDependencies() {
    console.log('🔍 启动前依赖检查...');

    const requiredModules = [
        'http', 'fs', 'path', 'url'
    ];

    const optionalModules = [
        { name: 'nodemailer', service: '邮件服务' },
        { name: 'tencentcloud-sdk-nodejs', service: '短信服务' },
        { name: 'archiver', service: '备份服务' },
        { name: 'node-cron', service: '定时任务' }
    ];

    // 检查必需模块
    for (const module of requiredModules) {
        try {
            require(module);
            console.log(`✅ ${module} - 已加载`);
        } catch (error) {
            console.error(`❌ ${module} - 加载失败:`, error.message);
            process.exit(1);
        }
    }

    // 检查可选模块
    for (const module of optionalModules) {
        try {
            require(module.name);
            console.log(`✅ ${module.name} (${module.service}) - 已加载`);
        } catch (error) {
            console.warn(`⚠️ ${module.name} (${module.service}) - 未安装，相关功能将不可用`);
        }
    }

    console.log('✅ 依赖检查完成');
}

// 执行启动检查
checkStartupDependencies();

// 引入日志服务
let logger;
try {
    logger = require('./logger-service.js');
    console.log('📋 日志服务已加载');
} catch (error) {
    console.log('⚠️ 日志服务加载失败，使用默认日志:', error.message);
    // 创建简单的日志对象作为备用
    logger = {
        info: (msg, cat) => console.log(`[INFO] [${cat || 'SYSTEM'}] ${msg}`),
        warn: (msg, cat) => console.warn(`[WARN] [${cat || 'SYSTEM'}] ${msg}`),
        error: (msg, cat) => console.error(`[ERROR] [${cat || 'SYSTEM'}] ${msg}`),
        debug: (msg, cat) => console.log(`[DEBUG] [${cat || 'SYSTEM'}] ${msg}`),
        operation: (action, details, user) => console.log(`[OPERATION] ${user}: ${action} - ${details}`),
        loginLog: (user, success, ip, details) => console.log(`[LOGIN] ${user} ${success ? 'SUCCESS' : 'FAILED'} from ${ip} ${details}`),
        upcLog: (action, code, user, details) => console.log(`[UPC] ${action} ${code} by ${user} ${details}`),
        systemLog: (event, details) => console.log(`[SYSTEM] ${event} ${details}`)
    };
}

// 引入备份服务
let backupService;
try {
    const BackupService = require('./backup-service.js');
    backupService = new BackupService();
    console.log('💾 备份服务已加载');
} catch (error) {
    console.log('⚠️ 备份服务加载失败:', error.message);
    console.log('💡 请运行: npm install archiver node-cron');
    // 创建简单的备份对象作为备用
    backupService = {
        performBackup: () => Promise.reject(new Error('备份服务未安装依赖包')),
        getBackupList: () => [],
        reloadConfig: () => {},
        cleanupOldBackups: () => Promise.resolve()
    };
}

// 引入邮件和短信服务
let emailService, smsService;
try {
    const EmailService = require('./email-service.js');
    const SMSService = require('./sms-service.js');
    emailService = new EmailService();
    smsService = new SMSService();
    console.log('📧 邮件服务已加载');
    console.log('📱 短信服务已加载');
} catch (error) {
    console.log('⚠️ 通知服务加载失败:', error.message);
    console.log('💡 请运行: npm install nodemailer tencentcloud-sdk-nodejs');
    // 创建简单的通知对象作为备用
    emailService = {
        sendEmail: () => Promise.reject(new Error('邮件服务未安装依赖包')),
        sendTestEmail: () => Promise.reject(new Error('邮件服务未安装依赖包')),
        verifyConfig: () => Promise.resolve({ success: false, message: '邮件服务未安装依赖包' }),
        reloadConfig: () => {}
    };
    smsService = {
        sendSMS: () => Promise.reject(new Error('短信服务未安装依赖包')),
        sendTestSMS: () => Promise.reject(new Error('短信服务未安装依赖包')),
        verifyConfig: () => Promise.resolve({ success: false, message: '短信服务未安装依赖包' }),
        reloadConfig: () => {}
    };
}

// 🔧 新增：引入性能监控和数据完整性服务
let performanceMonitor, dataIntegrityService;
try {
    const PerformanceMonitor = require('./performance-monitor.js');
    const DataIntegrityService = require('./data-integrity-service.js');
    performanceMonitor = new PerformanceMonitor();
    dataIntegrityService = new DataIntegrityService();
    console.log('📊 性能监控服务已加载');
    console.log('🔧 数据完整性服务已加载');
} catch (error) {
    console.log('⚠️ 监控服务加载失败:', error.message);
    // 创建简单的监控对象作为备用
    performanceMonitor = {
        recordRequest: () => {},
        updateActiveConnections: () => {},
        getPerformanceStats: () => ({ status: 'unavailable' }),
        checkHealth: () => ({ status: 'unknown', issues: [], warnings: [] }),
        getMonitoringReport: () => ({ status: 'unavailable' })
    };
    dataIntegrityService = {
        checkDataIntegrity: () => ({ status: 'unavailable' }),
        performMaintenance: () => ({ status: 'unavailable' })
    };
}

// 环境配置
const NODE_ENV = process.env.NODE_ENV || 'development';
const IS_PRODUCTION = NODE_ENV === 'production';
const PORT = process.env.PORT || 3001;
const startTime = Date.now(); // 🔧 新增：记录系统启动时间

console.log(`🌍 运行环境: ${NODE_ENV}`);
console.log(`🏭 生产模式: ${IS_PRODUCTION ? '是' : '否'}`);
console.log(`🔧 版本: V2.8.4`);

// 生产环境日志控制
function logInfo(message, ...args) {
    if (!IS_PRODUCTION) {
        console.log(message, ...args);
    }
}

function logError(message, ...args) {
    console.error(message, ...args);
}

function logSuccess(message, ...args) {
    console.log(message, ...args);
}

// 数据文件路径
const DATA_DIR = path.join(__dirname, 'data');
const USERS_FILE = path.join(DATA_DIR, 'users.json');
const UPC_CODES_FILE = path.join(DATA_DIR, 'upc_codes.json');
const APPLICATIONS_FILE = path.join(DATA_DIR, 'applications.json');
const RECYCLE_RECORDS_FILE = path.join(DATA_DIR, 'recycle_records.json');
const REPORTS_FILE = path.join(DATA_DIR, 'reports.json');
const OPERATION_LOG_FILE = path.join(DATA_DIR, 'operation_logs.json');

// 确保数据目录存在
if (!fs.existsSync(DATA_DIR)) {
    fs.mkdirSync(DATA_DIR, { recursive: true });
}

// 数据加载和保存函数
function loadData(filePath, defaultData = []) {
    try {
        // 如果是相对路径，加上DATA_DIR前缀
        const fullPath = path.isAbsolute(filePath) ? filePath : path.join(DATA_DIR, filePath);
        if (fs.existsSync(fullPath)) {
            const data = fs.readFileSync(fullPath, 'utf8');
            return JSON.parse(data);
        }
    } catch (error) {
        console.error(`加载数据文件失败 ${filePath}:`, error.message);
    }
    return defaultData;
}

// 获取用户显示名称
function getUserDisplayName(userId) {
    // 根据用户ID返回友好的显示名称
    const userNames = {
        'admin': '管理员',
        'manager': '业务经理',
        'operator': '操作员',
        'sutuo_admin': 'Sutuo管理员',
        'test_user': '测试用户'
    };

    return userNames[userId] || userId;
}

function saveData(filePath, data) {
    try {
        console.log(`💾 开始保存数据文件: ${filePath}`);
        console.log(`📊 数据数量: ${Array.isArray(data) ? data.length : '非数组'}`);

        fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');

        // 验证文件是否真的被写入
        const fileStats = fs.statSync(filePath);
        console.log(`✅ 文件保存成功: ${filePath}`);
        console.log(`📁 文件大小: ${fileStats.size} 字节`);
        console.log(`⏰ 修改时间: ${fileStats.mtime.toLocaleString()}`);

        return true;
    } catch (error) {
        console.error(`❌ 保存数据文件失败 ${filePath}:`, error.message);
        return false;
    }
}

// 初始化数据
let users = loadData(USERS_FILE, [
    { id: 1, username: 'admin', password: 'admin123', name: '系统管理员', role: 'admin', email: '<EMAIL>', phone: '************', created_at: new Date().toISOString() },
    { id: 2, username: 'manager', password: 'Manager@2025', name: '业务经理', role: 'manager', email: '<EMAIL>', phone: '13800138000', created_at: new Date().toISOString() },
    { id: 3, username: 'operator', password: 'Operator@2025', name: '操作员', role: 'user', email: '<EMAIL>', phone: '13800138001', created_at: new Date().toISOString() }
]);

// 用户会话管理
let userSessions = new Map(); // 存储用户会话信息
let loginAttempts = new Map(); // 存储登录失败次数

// 🔧 增强：会话验证函数
function validateSession(sessionId, clientIP = null) {
    if (!sessionId) {
        console.log('❌ 会话验证失败: 缺少会话ID');
        return null;
    }

    const session = userSessions.get(sessionId);
    if (!session) {
        console.log(`❌ 会话验证失败: 会话不存在 - ${sessionId}`);
        return null;
    }

    // 检查会话是否过期
    if (new Date() > session.expiryTime) {
        userSessions.delete(sessionId);
        console.log(`🕐 会话已过期: ${session.username} - ${sessionId}`);
        return null;
    }

    // 🔧 增强：检查IP地址一致性（可选，用于增强安全性）
    if (clientIP && session.clientIP && session.clientIP !== clientIP) {
        console.log(`⚠️ 会话IP地址不匹配: ${session.username} - 原IP: ${session.clientIP}, 当前IP: ${clientIP}`);
        // 注意：在某些网络环境下IP可能会变化，所以这里只记录警告，不强制验证失败
    }

    // 🔧 增强：更新最后活动时间
    session.lastActivity = new Date();

    console.log(`✅ 会话验证成功: ${session.username} - ${sessionId}`);
    return session;
}

// 权限验证函数
function validatePermission(session, requiredPermission) {
    if (!session) return false;

    // 获取用户信息
    const user = users.find(u => u.username === session.username);
    if (!user) return false;

    // 管理员拥有所有权限
    if (user.role === 'admin') return true;

    // 检查用户是否有特定权限
    if (user.permissions && user.permissions.includes(requiredPermission)) {
        return true;
    }

    // 基于角色的默认权限检查
    const rolePermissions = {
        'manager': ['upc_management', 'upc_request', 'upc_recycle', 'view_reports', 'user_view'],
        'user': ['upc_request', 'upc_recycle', 'view_own_history']
    };

    const userPermissions = rolePermissions[user.role] || [];
    return userPermissions.includes(requiredPermission);
}

// 从请求中获取会话ID
function getSessionFromRequest(req) {
    // 从Authorization头获取
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
        return authHeader.substring(7);
    }

    // 从查询参数获取（兼容性）
    const parsedUrl = url.parse(req.url, true);
    return parsedUrl.query.sessionId;
}

// 库存预警检查
function checkStockAlert() {
    try {
        // 获取系统设置
        const settings = loadData('system_settings.json', {});
        const stockAlertSettings = settings.stockAlert;

        if (!stockAlertSettings || !stockAlertSettings.enableStockAlert) {
            return; // 库存预警未启用
        }

        // 获取当前库存统计
        const availableCount = upcCodes.filter(c => c.status === 'available').length;
        const threshold = parseInt(stockAlertSettings.stockThreshold) || 50;

        // 检查是否需要预警
        if (availableCount <= threshold) {
            // 检查通知频率限制
            const alertFrequency = stockAlertSettings.alertFrequency || 'daily';
            const lastAlertTime = stockAlertSettings.lastAlertTime;
            const now = new Date();

            // 判断是否应该发送通知
            let shouldSendAlert = false;

            if (!lastAlertTime) {
                // 从未发送过预警，应该发送
                shouldSendAlert = true;
            } else {
                const lastAlert = new Date(lastAlertTime);
                const timeDiff = now.getTime() - lastAlert.getTime();

                switch (alertFrequency) {
                    case 'realtime':
                        // 实时通知，每次都发送
                        shouldSendAlert = true;
                        break;
                    case 'hourly':
                        // 每小时最多一次
                        shouldSendAlert = timeDiff >= 60 * 60 * 1000;
                        break;
                    case 'daily':
                        // 每天最多一次
                        shouldSendAlert = timeDiff >= 24 * 60 * 60 * 1000;
                        break;
                    case 'weekly':
                        // 每周最多一次
                        shouldSendAlert = timeDiff >= 7 * 24 * 60 * 60 * 1000;
                        break;
                    default:
                        // 默认每天一次
                        shouldSendAlert = timeDiff >= 24 * 60 * 60 * 1000;
                        break;
                }
            }

            if (shouldSendAlert) {
                const alertData = {
                    currentStock: availableCount,
                    threshold: threshold,
                    shortageRate: ((threshold - availableCount) / threshold * 100).toFixed(1),
                    alertTime: now.toISOString(),
                    alertType: 'stock_low'
                };

                console.log(`🚨 库存预警触发: 当前库存 ${availableCount} <= 阈值 ${threshold}`);
                console.log(`📅 通知频率: ${alertFrequency}, 上次通知: ${lastAlertTime || '从未'}`);

                // 发送预警通知
                sendStockAlertNotifications(alertData, stockAlertSettings);

                // 更新最后预警时间
                updateLastAlertTime(now.toISOString());
            } else {
                console.log(`⏰ 库存预警跳过: 通知频率限制 (${alertFrequency}), 上次通知: ${lastAlertTime}`);
            }
        }

    } catch (error) {
        console.error('库存预警检查失败:', error.message);
    }
}

// 更新最后预警时间
function updateLastAlertTime(alertTime) {
    try {
        const settings = loadData('system_settings.json', {});
        if (!settings.stockAlert) {
            settings.stockAlert = {};
        }
        settings.stockAlert.lastAlertTime = alertTime;

        // 保存设置
        saveData('system_settings.json', settings);
        console.log(`📝 已更新最后预警时间: ${alertTime}`);
    } catch (error) {
        console.error('更新最后预警时间失败:', error.message);
    }
}

// 发送库存预警通知
async function sendStockAlertNotifications(alertData, settings) {
    const results = {
        system: { success: false, message: '' },
        email: { success: false, message: '', count: 0 },
        sms: { success: false, message: '', count: 0 }
    };

    try {
        // 获取接收人列表
        const emailRecipients = (settings.emailRecipients || settings.alertRecipients || '')
            .split(',')
            .map(r => r.trim())
            .filter(r => r.includes('@'));

        const smsRecipients = (settings.smsRecipients || settings.alertRecipients || '')
            .split(',')
            .map(r => r.trim())
            .filter(r => /^1[3-9]\d{9}$/.test(r));

        const template = settings.alertTemplate || '库存预警：当前库存{current_stock}个，预警阈值{threshold}个';

        // 替换模板变量
        const message = template
            .replace(/{current_stock}/g, alertData.currentStock)
            .replace(/{threshold}/g, alertData.threshold)
            .replace(/{shortage_rate}/g, alertData.shortageRate)
            .replace(/{alert_time}/g, new Date(alertData.alertTime).toLocaleString('zh-CN'))
            .replace(/{date}/g, new Date(alertData.alertTime).toLocaleDateString('zh-CN'))
            .replace(/{time}/g, new Date(alertData.alertTime).toLocaleTimeString('zh-CN'));

        // 系统通知（总是启用）
        if (settings.alertBySystem !== false) {
            console.log(`📢 系统预警: ${message}`);
            results.system.success = true;
            results.system.message = '系统通知已显示';
        }

        // 邮件通知
        if (settings.alertByEmail && emailRecipients.length > 0) {
            console.log(`📧 开始发送预警邮件到 ${emailRecipients.length} 个接收人...`);

            try {
                // 获取邮件配置
                const allSettings = loadData('system_settings.json', {});
                const emailConfig = allSettings.notification?.email;

                // 检查邮件服务是否启用（支持两种配置方式）
                const emailServiceEnabled = allSettings.notification?.enableEmailService || emailConfig?.enabled;
                if (!emailConfig || !emailServiceEnabled) {
                    throw new Error('邮件服务未启用或配置不完整');
                }

                // 调用邮件服务
                const EmailService = require('./email-service');
                const emailService = new EmailService();
                const emailSubject = '【UPC管理系统】库存预警通知';

                let successCount = 0;
                let failCount = 0;

                for (const email of emailRecipients) {
                    try {
                        const result = await emailService.sendEmail(
                            email,
                            emailSubject,
                            message.replace(/\n/g, '<br>'),
                            true // isHtml
                        );

                        if (result.success) {
                            successCount++;
                            console.log(`✅ 预警邮件发送成功: ${email}`);
                        } else {
                            failCount++;
                            console.log(`❌ 预警邮件发送失败: ${email} - ${result.error}`);
                        }
                    } catch (emailError) {
                        failCount++;
                        console.log(`❌ 预警邮件发送异常: ${email} - ${emailError.message}`);
                    }
                }

                results.email.success = successCount > 0;
                results.email.count = successCount;
                results.email.message = `成功发送 ${successCount}/${emailRecipients.length} 封邮件`;

            } catch (emailError) {
                console.error('邮件预警发送失败:', emailError.message);
                results.email.success = false;
                results.email.message = `邮件发送失败: ${emailError.message}`;
            }
        }

        // 短信通知
        if (settings.alertBySMS && smsRecipients.length > 0) {
            console.log(`📱 开始发送预警短信到 ${smsRecipients.length} 个接收人...`);

            try {
                // 获取短信配置
                const allSettings = loadData('system_settings.json', {});
                const smsConfig = allSettings.notification?.sms;

                // 检查短信服务是否启用（支持两种配置方式）
                const smsServiceEnabled = allSettings.notification?.enableSMSService || smsConfig?.enabled;
                if (!smsConfig || !smsServiceEnabled) {
                    throw new Error('短信服务未启用或配置不完整');
                }

                // 调用短信服务
                const SMSService = require('./sms-service');
                const smsService = new SMSService();

                let successCount = 0;
                let failCount = 0;

                for (const phone of smsRecipients) {
                    try {
                        // 调用短信服务，传递正确的参数格式（缩短参数长度）
                        const result = await smsService.sendSMS(
                            phone,
                            message,
                            [
                                alertData.currentStock.toString(), // 当前库存
                                alertData.threshold.toString(),    // 预警阈值
                                new Date(alertData.alertTime).toLocaleDateString('zh-CN', {
                                    month: '2-digit',
                                    day: '2-digit'
                                }) // 简化日期格式：MM/DD
                            ]
                        );

                        if (result && result.success !== false) {
                            successCount++;
                            console.log(`✅ 预警短信发送成功: ${phone}`);
                        } else {
                            failCount++;
                            console.log(`❌ 预警短信发送失败: ${phone} - ${result?.message || result?.error || '未知错误'}`);
                        }
                    } catch (smsError) {
                        failCount++;
                        console.log(`❌ 预警短信发送异常: ${phone} - ${smsError.message}`);
                    }
                }

                results.sms.success = successCount > 0;
                results.sms.count = successCount;
                results.sms.message = `成功发送 ${successCount}/${smsRecipients.length} 条短信`;

            } catch (smsError) {
                console.error('短信预警发送失败:', smsError.message);
                results.sms.success = false;
                results.sms.message = `短信发送失败: ${smsError.message}`;
            }
        }

        // 记录预警发送结果
        console.log('📊 库存预警通知发送结果:', results);
        return results;

    } catch (error) {
        console.error('发送库存预警通知失败:', error.message);
        return results;
    }
}

// 定期清理过期会话和登录失败记录
setInterval(() => {
    const now = new Date();

    // 清理过期会话
    for (const [sessionId, session] of userSessions.entries()) {
        if (now > session.expiryTime) {
            userSessions.delete(sessionId);
            console.log(`🧹 清理过期会话: ${session.username} - ${sessionId}`);
        }
    }

    // 清理1小时前的登录失败记录
    const oneHourAgo = now.getTime() - 60 * 60 * 1000;
    for (const [key, attempts] of loginAttempts.entries()) {
        // 这里简化处理，实际应该记录时间戳
        if (attempts > 0) {
            loginAttempts.delete(key);
        }
    }
}, 5 * 60 * 1000); // 每5分钟清理一次

// 定期检查库存预警
setInterval(() => {
    checkStockAlert();
}, 10 * 60 * 1000); // 每10分钟检查一次

// 启动时检查一次库存预警
setTimeout(() => {
    checkStockAlert();
}, 5000); // 启动5秒后检查

let upcCodes = loadData(UPC_CODES_FILE, []);
let upcApplications = loadData(APPLICATIONS_FILE, []);
let recycleRecords = loadData(RECYCLE_RECORDS_FILE, []);
let reports = loadData(REPORTS_FILE, []);

// 数据版本管理（用于实时同步）
let dataVersions = {
    upc: Date.now(),
    recycle: Date.now(),
    dashboard: Date.now(),
    users: Date.now(),
    settings: Date.now()
};

// 更新数据版本的辅助函数
function updateDataVersion(module) {
    dataVersions[module] = Date.now();
    console.log(`📡 更新数据版本: ${module} -> ${dataVersions[module]}`);
}

// 保存初始数据
saveData(USERS_FILE, users);
saveData(UPC_CODES_FILE, upcCodes);
saveData(APPLICATIONS_FILE, upcApplications);
saveData(RECYCLE_RECORDS_FILE, recycleRecords);
saveData(REPORTS_FILE, reports);

// UPC码导入功能（管理员使用）
function importUPCCodes(codes) {
    const newCodes = [];
    let nextId = upcCodes.length > 0 ? Math.max(...upcCodes.map(c => c.id)) + 1 : 1;

    codes.forEach(code => {
        // 检查是否已存在
        if (!upcCodes.find(c => c.code === code)) {
            newCodes.push({
                id: nextId++,
                code: code,
                status: 'available',
                assigned_user: null,
                created_at: new Date().toISOString()
            });
        }
    });

    upcCodes.push(...newCodes);
    saveData(UPC_CODES_FILE, upcCodes);
    return newCodes.length;
}

// MIME类型映射
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpg',
    '.gif': 'image/gif',
    '.ico': 'image/x-icon'
};

// 获取文件MIME类型
function getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return mimeTypes[ext] || 'application/octet-stream';
}

// 解析POST数据
function parsePostData(req) {
    return new Promise((resolve, reject) => {
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });
        req.on('end', () => {
            try {
                resolve(JSON.parse(body));
            } catch (e) {
                resolve({});
            }
        });
        req.on('error', reject);
    });
}

// 创建HTTP服务器
const server = http.createServer(async (req, res) => {
    // 🔧 新增：性能监控 - 记录请求开始时间
    const requestStartTime = Date.now();
    let isError = false;

    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;
    const method = req.method;

    // 设置CORS头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    // API路由
    if (pathname.startsWith('/api/')) {
        res.setHeader('Content-Type', 'application/json');

        try {
            if (pathname === '/api/health') {
                res.writeHead(200);
                res.end(JSON.stringify({
                    status: 'ok',
                    version: 'V2.8.4 Linux版',
                    timestamp: new Date().toISOString(),
                    message: 'UPC管理系统生产环境运行正常',
                    environment: 'production'
                }));
                return;
            }

            // 🔧 新增：数据版本同步API
            if (pathname === '/api/data/versions') {
                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    versions: dataVersions,
                    timestamp: new Date().toISOString()
                }));
                return;
            }

            // 🔧 新增：系统安全状态检查API
            if (pathname === '/api/security/status') {
                // 验证会话
                const sessionId = getSessionFromRequest(req);
                const session = validateSession(sessionId);

                if (!session) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '未授权访问，请重新登录'
                    }));
                    return;
                }

                // 只有管理员可以查看安全状态
                if (!validatePermission(session, 'upc_management')) {
                    res.writeHead(403);
                    res.end(JSON.stringify({
                        success: false,
                        message: '权限不足，只有管理员可以查看安全状态'
                    }));
                    return;
                }

                const securityStatus = {
                    activeSessions: userSessions.size,
                    loginAttempts: loginAttempts.size,
                    dataVersions: dataVersions,
                    systemUptime: Date.now() - startTime,
                    memoryUsage: process.memoryUsage(),
                    timestamp: new Date().toISOString()
                };

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    data: securityStatus
                }));
                return;
            }

            // 🔧 新增：系统性能监控API
            if (pathname === '/api/system/performance') {
                // 验证会话
                const sessionId = getSessionFromRequest(req);
                const session = validateSession(sessionId);

                if (!session) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '未授权访问，请重新登录'
                    }));
                    return;
                }

                // 只有管理员可以查看性能状态
                if (!validatePermission(session, 'upc_management')) {
                    res.writeHead(403);
                    res.end(JSON.stringify({
                        success: false,
                        message: '权限不足，只有管理员可以查看性能状态'
                    }));
                    return;
                }

                const performanceReport = performanceMonitor.getMonitoringReport();

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    data: performanceReport
                }));
                return;
            }

            // 🔧 新增：数据完整性检查API
            if (pathname === '/api/system/data-integrity') {
                // 验证会话
                const sessionId = getSessionFromRequest(req);
                const session = validateSession(sessionId);

                if (!session) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '未授权访问，请重新登录'
                    }));
                    return;
                }

                // 只有管理员可以执行数据完整性检查
                if (!validatePermission(session, 'upc_management')) {
                    res.writeHead(403);
                    res.end(JSON.stringify({
                        success: false,
                        message: '权限不足，只有管理员可以执行数据完整性检查'
                    }));
                    return;
                }

                try {
                    const integrityResults = dataIntegrityService.checkDataIntegrity();

                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        data: integrityResults
                    }));
                } catch (error) {
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: '数据完整性检查失败: ' + error.message
                    }));
                }
                return;
            }

            if (pathname === '/api/auth/login' && method === 'POST') {
                const data = await parsePostData(req);
                const clientIP = req.connection.remoteAddress || 'unknown';

                // 获取安全设置
                let securitySettings = {
                    minPasswordLength: 8,
                    maxLoginAttempts: 5,
                    sessionTimeout: 120, // 默认2小时
                    enableLoginLog: true
                };
                try {
                    const settings = loadData('system_settings.json', {});
                    if (settings.security) {
                        securitySettings = { ...securitySettings, ...settings.security };
                    }
                } catch (error) {
                    console.log('使用默认安全设置');
                }

                // 检查登录失败次数
                const attemptKey = `${data.username}_${clientIP}`;
                const currentAttempts = loginAttempts.get(attemptKey) || 0;

                if (currentAttempts >= securitySettings.maxLoginAttempts) {
                    if (securitySettings.enableLoginLog) {
                        console.log(`🚫 登录被阻止: ${data.username} - IP: ${clientIP} - 超过最大尝试次数(${securitySettings.maxLoginAttempts})`);
                    }
                    res.writeHead(429);
                    res.end(JSON.stringify({
                        success: false,
                        message: `登录失败次数过多，请稍后再试。最大尝试次数：${securitySettings.maxLoginAttempts}`
                    }));
                    return;
                }

                const user = users.find(u => u.username === data.username && u.password === data.password);

                if (user) {
                    // 登录成功，清除失败记录
                    loginAttempts.delete(attemptKey);

                    // 更新最后登录时间
                    user.last_login = new Date().toISOString();

                    // 创建会话
                    const sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                    const sessionExpiry = new Date(Date.now() + securitySettings.sessionTimeout * 60 * 1000);

                    userSessions.set(sessionId, {
                        userId: user.id,
                        username: user.username,
                        loginTime: new Date(),
                        expiryTime: sessionExpiry,
                        clientIP: clientIP
                    });

                    // 保存用户数据
                    saveData(USERS_FILE, users);

                    // 记录登录日志（如果启用）
                    if (securitySettings.enableLoginLog) {
                        logger.loginLog(user.username, true, clientIP, `会话: ${sessionId}, 超时: ${securitySettings.sessionTimeout}分钟`);
                        console.log(`✅ 用户登录成功: ${user.username} (${user.name}) - IP: ${clientIP} - 会话: ${sessionId} - 超时: ${securitySettings.sessionTimeout}分钟`);
                    } else {
                        logger.info(`用户登录成功: ${user.username} (${user.name})`, 'LOGIN');
                        console.log(`✅ 用户登录成功: ${user.username} (${user.name})`);
                    }

                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        sessionId: sessionId,
                        sessionTimeout: securitySettings.sessionTimeout,
                        user: {
                            id: user.id,
                            username: user.username,
                            name: user.name,
                            role: user.role,
                            email: user.email || '',
                            phone: user.phone || '',
                            avatarType: user.avatarType || 'name',
                            last_login: user.last_login,
                            created_at: user.created_at
                        }
                    }));
                } else {
                    // 登录失败，增加失败次数
                    loginAttempts.set(attemptKey, currentAttempts + 1);

                    if (securitySettings.enableLoginLog) {
                        logger.loginLog(data.username, false, clientIP, `尝试次数: ${currentAttempts + 1}/${securitySettings.maxLoginAttempts}`);
                        console.log(`❌ 登录失败: ${data.username} - IP: ${clientIP} - 尝试次数: ${currentAttempts + 1}/${securitySettings.maxLoginAttempts}`);
                    } else {
                        logger.warn(`登录失败: ${data.username} - 用户名或密码错误`, 'LOGIN');
                        console.log(`❌ 登录失败: ${data.username} - 用户名或密码错误`);
                    }

                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '用户名或密码错误',
                        remainingAttempts: securitySettings.maxLoginAttempts - (currentAttempts + 1)
                    }));
                }
                return;
            }

            if (pathname === '/api/auth/logout' && method === 'POST') {
                const data = await parsePostData(req);
                const sessionId = data.sessionId;

                if (sessionId && userSessions.has(sessionId)) {
                    const session = userSessions.get(sessionId);
                    userSessions.delete(sessionId);
                    console.log(`👋 用户登出: ${session.username} - ${sessionId}`);

                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        message: '登出成功'
                    }));
                } else {
                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        message: '会话已失效'
                    }));
                }
                return;
            }

            if (pathname === '/api/upc/request' && method === 'POST') {
                // 验证会话
                const sessionId = getSessionFromRequest(req);
                const session = validateSession(sessionId);

                if (!session) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '未授权访问，请重新登录'
                    }));
                    return;
                }

                // 权限检查：需要UPC申请权限
                if (!validatePermission(session, 'upc_request')) {
                    res.writeHead(403);
                    res.end(JSON.stringify({
                        success: false,
                        message: '权限不足，无法申请UPC码'
                    }));
                    return;
                }

                const data = await parsePostData(req);

                // 输入验证
                // 获取系统设置中的最大申请数量
                let maxAllocation = 20; // 默认值
                try {
                    const settings = loadData('system_settings.json', {});
                    if (settings.upc && settings.upc.maxAllocation) {
                        maxAllocation = parseInt(settings.upc.maxAllocation);
                        console.log(`📋 从设置中读取最大申请数量: ${maxAllocation}`);
                    } else {
                        console.log('⚠️ 未找到UPC设置，使用默认值:', maxAllocation);
                    }
                } catch (error) {
                    console.log('❌ 读取设置失败，使用默认最大申请数量:', maxAllocation, error.message);
                }

                if (!data.quantity || data.quantity < 1 || data.quantity > maxAllocation) {
                    res.writeHead(400);
                    res.end(JSON.stringify({
                        success: false,
                        message: `申请数量必须在1-${maxAllocation}之间`
                    }));
                    return;
                }

                if (!data.purpose || data.purpose.trim().length === 0) {
                    res.writeHead(400);
                    res.end(JSON.stringify({
                        success: false,
                        message: '请填写申请原因'
                    }));
                    return;
                }

                const availableCodes = upcCodes.filter(c => c.status === 'available').slice(0, data.quantity);

                if (availableCodes.length < data.quantity) {
                    res.writeHead(400);
                    res.end(JSON.stringify({
                        success: false,
                        message: '可用UPC码不足'
                    }));
                    return;
                }

                const requestId = 'REQ' + Date.now();

                // 获取当前用户信息
                const currentUser = users.find(u => u.username === session.username);

                // 更新UPC码状态
                availableCodes.forEach(code => {
                    code.status = 'allocated';
                    code.assigned_user = currentUser.username;
                    code.request_id = requestId; // 记录申请ID
                    code.updated_at = new Date().toISOString();
                });

                // 添加申请记录
                upcApplications.push({
                    id: upcApplications.length + 1,
                    request_id: requestId,
                    user_id: currentUser.username,
                    quantity: data.quantity,
                    purpose: data.purpose,
                    status: 'completed',
                    created_at: new Date().toISOString()
                });

                // 保存数据到文件
                saveData(UPC_CODES_FILE, upcCodes);
                saveData(APPLICATIONS_FILE, upcApplications);

                // 更新数据版本，触发客户端同步
                updateDataVersion('upc');

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    requestId: requestId,
                    upcCodes: availableCodes.map(c => c.code)
                }));
                return;
            }

            if (pathname === '/api/upc/recycle' && method === 'POST') {
                // 验证会话
                const sessionId = getSessionFromRequest(req);
                console.log('🔍 UPC回收API - 会话ID:', sessionId);
                const session = validateSession(sessionId);
                console.log('🔍 UPC回收API - 会话验证结果:', session ? `用户: ${session.username}` : '无效会话');

                if (!session) {
                    console.log('❌ UPC回收API - 会话验证失败');
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '未授权访问，请重新登录'
                    }));
                    return;
                }

                // 权限检查：需要UPC回收权限
                if (!validatePermission(session, 'upc_recycle')) {
                    res.writeHead(403);
                    res.end(JSON.stringify({
                        success: false,
                        message: '权限不足，无法回收UPC码'
                    }));
                    return;
                }

                const data = await parsePostData(req);
                const code = upcCodes.find(c => c.code === data.code);

                // 获取当前用户信息
                const currentUser = users.find(u => u.username === session.username);

                // 权限检查：普通用户只能回收自己的UPC码，管理员可以回收任何UPC码
                if (currentUser.role !== 'admin' && code && code.assigned_user !== currentUser.username) {
                    res.writeHead(403);
                    res.end(JSON.stringify({
                        success: false,
                        message: '权限不足，只能回收自己的UPC码'
                    }));
                    return;
                }

                if (code && code.status === 'allocated') {
                    // 记录原始分配用户
                    const originalUser = code.assigned_user;

                    // 检查自动激活设置
                    const autoReactivateEnabled = data.autoReactivateEnabled || false;

                    // 检查是否在短时间内重复回收（防止重复回收问题）
                    const currentTime = new Date();
                    const lastRecycleTime = code.last_recycled_at ? new Date(code.last_recycled_at) : null;
                    const timeDiff = lastRecycleTime ? (currentTime - lastRecycleTime) / 1000 : Infinity; // 秒

                    if (lastRecycleTime && timeDiff < 5) { // 5秒内不允许重复回收
                        res.writeHead(400);
                        res.end(JSON.stringify({
                            success: false,
                            message: `UPC码刚刚被回收，请等待 ${Math.ceil(5 - timeDiff)} 秒后再试`
                        }));
                        return;
                    }

                    // 根据自动激活设置决定状态
                    const newStatus = autoReactivateEnabled ? 'available' : 'recycled';
                    const recordStatus = autoReactivateEnabled ? 'reusable' : 'recycled';

                    console.log(`🔄 服务器端回收处理: autoReactivateEnabled=${autoReactivateEnabled}, newStatus=${newStatus}`);

                    // 更新UPC码状态
                    code.status = newStatus;
                    code.assigned_user = autoReactivateEnabled ? null : null; // 都清空分配用户
                    code.updated_at = new Date().toISOString();
                    code.last_recycled_at = currentTime.toISOString(); // 记录最后回收时间

                    // 添加回收记录，使用原始分配用户ID
                    recycleRecords.push({
                        id: Date.now() + Math.random(), // 使用时间戳+随机数确保唯一性
                        code: data.code,
                        reason: data.reason || '回收',
                        status: recordStatus,
                        user_id: originalUser || currentUser.username,
                        recycled_by: currentUser.username, // 记录实际回收操作的用户
                        created_at: new Date().toISOString()
                    });

                    // 保存数据到文件
                    saveData(UPC_CODES_FILE, upcCodes);
                    saveData(RECYCLE_RECORDS_FILE, recycleRecords);

                    // 更新数据版本，触发客户端同步
                    updateDataVersion('upc');
                    updateDataVersion('recycle');

                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        message: 'UPC码回收成功'
                    }));
                } else {
                    res.writeHead(400);
                    res.end(JSON.stringify({
                        success: false,
                        message: code ?
                            (code.status === 'recycled' ? 'UPC码已被回收' :
                             code.status === 'available' ? 'UPC码未分配，无需回收' :
                             code.status === 'invalid' ? 'UPC码已无效，无法回收' :
                             `UPC码状态为${code.status}，无法回收`) :
                            'UPC码不存在'
                    }));
                }
                return;
            }

            if (pathname === '/api/upc/delete' && method === 'POST') {
                // 验证会话
                const sessionId = getSessionFromRequest(req);
                const session = validateSession(sessionId);

                if (!session) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '未授权访问，请重新登录'
                    }));
                    return;
                }

                // 权限检查：只有管理员可以删除UPC码
                if (!validatePermission(session, 'upc_management')) {
                    res.writeHead(403);
                    res.end(JSON.stringify({
                        success: false,
                        message: '权限不足，只有管理员可以删除UPC码'
                    }));
                    return;
                }

                const data = await parsePostData(req);
                const upcId = data.upcId;
                const code = data.code;

                // 查找要删除的UPC码
                const upcIndex = upcCodes.findIndex(c => c.id == upcId || c.code === code);

                if (upcIndex === -1) {
                    res.writeHead(404);
                    res.end(JSON.stringify({
                        success: false,
                        message: 'UPC码不存在'
                    }));
                    return;
                }

                const upcToDelete = upcCodes[upcIndex];

                // 检查是否可以删除（所有状态的UPC码都可以删除）
                console.log(`准备删除UPC码: ${upcToDelete.code}, 状态: ${upcToDelete.status}`);

                // 从数组中删除
                upcCodes.splice(upcIndex, 1);

                // 同时从申请记录中移除相关记录
                upcApplications.forEach(app => {
                    if (app.request_id) {
                        // 从申请记录中移除该UPC码
                        const relatedCodes = upcCodes.filter(c => c.request_id === app.request_id);
                        if (relatedCodes.length === 0) {
                            // 如果该申请的所有UPC码都被删除了，可以考虑删除申请记录
                            // 这里暂时保留申请记录，只删除UPC码
                        }
                    }
                });

                // 从回收记录中移除相关记录
                const recycleIndex = recycleRecords.findIndex(r => r.code === upcToDelete.code);
                if (recycleIndex > -1) {
                    recycleRecords.splice(recycleIndex, 1);
                }

                // 保存数据到文件
                saveData(UPC_CODES_FILE, upcCodes);
                saveData(APPLICATIONS_FILE, upcApplications);
                saveData(RECYCLE_RECORDS_FILE, recycleRecords);

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    message: 'UPC码删除成功'
                }));
                return;
            }

            if (pathname === '/api/upc/update' && method === 'POST') {
                // 验证会话
                const sessionId = getSessionFromRequest(req);
                const session = validateSession(sessionId);

                if (!session) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '未授权访问，请重新登录'
                    }));
                    return;
                }

                // 🔧 修复：权限检查逻辑 - 支持用户标记已使用功能
                const data = await parsePostData(req);
                const upcId = data.upcId;
                const updates = data.updates;

                // 获取当前用户信息
                const currentUser = users.find(u => u.username === session.username);

                // 检查是否为标记已使用操作
                const isMarkAsUsedOperation = updates.status === 'invalid' &&
                    (updates.notes === '标记为已使用' || updates.notes === '测试标记为已使用');

                if (isMarkAsUsedOperation) {
                    // 🔧 标记已使用操作：允许用户标记自己回收过的UPC码
                    console.log(`🔍 检测到标记已使用操作，用户: ${currentUser.username}, UPC ID: ${upcId}`);

                    // 🔧 修复：查找UPC码前先验证upcId
                    console.log(`🔍 查找UPC码，upcId: ${upcId} (类型: ${typeof upcId})`);

                    if (!upcId || upcId === null || upcId === undefined) {
                        console.log(`❌ upcId无效: ${upcId}`);
                        res.writeHead(400);
                        res.end(JSON.stringify({
                            success: false,
                            message: 'UPC码ID无效，请刷新页面后重试'
                        }));
                        return;
                    }

                    const upcToCheck = upcCodes.find(c => c.id == upcId);
                    if (!upcToCheck) {
                        console.log(`❌ 未找到UPC码，upcId: ${upcId}`);
                        console.log(`🔍 当前UPC池中的ID列表:`, upcCodes.map(c => c.id).slice(0, 10));
                        res.writeHead(404);
                        res.end(JSON.stringify({
                            success: false,
                            message: `UPC码不存在 (ID: ${upcId})，请刷新页面后重试`
                        }));
                        return;
                    }

                    // 权限验证：检查用户是否有权限标记此UPC码
                    let hasPermission = false;

                    if (currentUser.role === 'admin') {
                        // 管理员可以标记任何UPC码
                        hasPermission = true;
                        console.log(`✅ 管理员权限: ${currentUser.username} 可以标记任何UPC码`);
                    } else {
                        // 普通用户：检查是否回收过此UPC码
                        const recycleRecord = recycleRecords.find(r =>
                            r.code === upcToCheck.code &&
                            (r.user_id === currentUser.username || r.recycled_by === currentUser.username)
                        );

                        if (recycleRecord) {
                            hasPermission = true;
                            console.log(`✅ 回收权限验证通过: ${currentUser.username} 曾回收过 ${upcToCheck.code}`);
                        } else {
                            console.log(`❌ 权限验证失败: ${currentUser.username} 未回收过 ${upcToCheck.code}`);
                            console.log(`🔍 该UPC码的回收记录:`, recycleRecords.filter(r => r.code === upcToCheck.code));
                        }
                    }

                    if (!hasPermission) {
                        res.writeHead(403);
                        res.end(JSON.stringify({
                            success: false,
                            message: '权限不足，只能标记自己回收过的UPC码为已使用'
                        }));
                        return;
                    }
                } else {
                    // 🔧 其他更新操作：只有管理员可以执行
                    if (!validatePermission(session, 'upc_management')) {
                        res.writeHead(403);
                        res.end(JSON.stringify({
                            success: false,
                            message: '权限不足，只有管理员可以更新UPC码'
                        }));
                        return;
                    }
                }

                // 🔧 修复：查找要更新的UPC码（第二次检查）
                console.log(`🔍 第二次查找UPC码，upcId: ${upcId}`);
                const upcIndex = upcCodes.findIndex(c => c.id == upcId);

                if (upcIndex === -1) {
                    console.log(`❌ 第二次查找失败，upcId: ${upcId}`);
                    console.log(`🔍 当前UPC池状态:`, {
                        总数: upcCodes.length,
                        前10个ID: upcCodes.map(c => c.id).slice(0, 10)
                    });
                    res.writeHead(404);
                    res.end(JSON.stringify({
                        success: false,
                        message: `UPC码不存在 (ID: ${upcId})，数据可能已发生变化，请刷新页面后重试`
                    }));
                    return;
                }

                const upcToUpdate = upcCodes[upcIndex];
                const oldStatus = upcToUpdate.status;

                console.log(`准备更新UPC码: ${upcToUpdate.code}, 从状态 ${oldStatus} 更新为 ${updates.status || oldStatus}`);

                // 更新UPC码信息
                if (updates.status) {
                    upcToUpdate.status = updates.status;
                }
                if (updates.assigned_user !== undefined) {
                    upcToUpdate.assigned_user = updates.assigned_user;
                }
                if (updates.notes !== undefined) {
                    upcToUpdate.notes = updates.notes;
                }

                // 更新时间戳
                upcToUpdate.updated_at = new Date().toISOString();

                // 如果状态发生变化，可能需要更新相关记录
                if (updates.status && updates.status !== oldStatus) {
                    // 如果从其他状态变为回收状态，添加回收记录
                    if (updates.status === 'recycled' && oldStatus !== 'recycled') {
                        const recycleRecord = {
                            id: Date.now(),
                            code: upcToUpdate.code,
                            original_purpose: '状态更新回收',
                            recycled_at: new Date().toISOString(),
                            status: 'recycled'
                        };
                        recycleRecords.push(recycleRecord);
                    }

                    // 如果从回收状态变为其他状态，移除回收记录
                    if (oldStatus === 'recycled' && updates.status !== 'recycled') {
                        const recycleIndex = recycleRecords.findIndex(r => r.code === upcToUpdate.code);
                        if (recycleIndex > -1) {
                            recycleRecords.splice(recycleIndex, 1);
                        }
                    }

                    // 🔧 修复：如果UPC码被标记为无效（已使用），更新对应的回收记录状态
                    if (updates.status === 'invalid') {
                        const recycleIndex = recycleRecords.findIndex(r => r.code === upcToUpdate.code);
                        if (recycleIndex > -1) {
                            const oldRecycleStatus = recycleRecords[recycleIndex].status;
                            recycleRecords[recycleIndex].status = 'processed';
                            recycleRecords[recycleIndex].processed_at = new Date().toISOString();
                            console.log(`🔧 更新回收记录状态: ${upcToUpdate.code} (${oldRecycleStatus}) -> processed`);
                        }
                    }
                }

                // 记录UPC操作日志
                logger.upcLog('UPDATE', upcToUpdate.code, 'system', `状态: ${oldStatus} -> ${upcToUpdate.status}, 备注: ${upcToUpdate.notes || '无'}`);

                // 保存数据到文件
                saveData(UPC_CODES_FILE, upcCodes);
                if (updates.status && updates.status !== oldStatus) {
                    saveData(RECYCLE_RECORDS_FILE, recycleRecords);

                    // 如果状态发生变化，检查库存预警
                    setTimeout(() => {
                        checkStockAlert();
                    }, 1000); // 延迟1秒检查，确保数据已保存
                }

                // 更新数据版本，触发客户端同步
                updateDataVersion('upc');
                if (updates.status && updates.status !== oldStatus) {
                    updateDataVersion('recycle');
                }

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    message: 'UPC码更新成功',
                    data: upcToUpdate
                }));
                return;
            }

            if (pathname === '/api/upc/batch-update' && method === 'POST') {
                // 验证会话
                const sessionId = getSessionFromRequest(req);
                const session = validateSession(sessionId);

                if (!session) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '未授权访问，请重新登录'
                    }));
                    return;
                }

                // 权限检查：只有管理员可以批量更新UPC码
                if (!validatePermission(session, 'upc_management')) {
                    res.writeHead(403);
                    res.end(JSON.stringify({
                        success: false,
                        message: '权限不足，只有管理员可以批量更新UPC码'
                    }));
                    return;
                }

                const data = await parsePostData(req);
                const updates = data.updates;

                if (!Array.isArray(updates) || updates.length === 0) {
                    res.writeHead(400);
                    res.end(JSON.stringify({
                        success: false,
                        message: '无效的更新数据'
                    }));
                    return;
                }

                let updateCount = 0;
                const errors = [];

                for (const update of updates) {
                    try {
                        // 查找要更新的UPC码
                        const upcIndex = upcCodes.findIndex(c => c.code === update.code);

                        if (upcIndex === -1) {
                            errors.push(`UPC码 ${update.code} 不存在`);
                            continue;
                        }

                        const upcToUpdate = upcCodes[upcIndex];
                        const oldStatus = upcToUpdate.status;

                        // 更新UPC码信息
                        if (update.status) {
                            upcToUpdate.status = update.status;
                        }
                        if (update.notes !== undefined) {
                            upcToUpdate.notes = update.notes;
                        }

                        // 更新时间戳
                        upcToUpdate.updated_at = new Date().toISOString();

                        // 如果状态发生变化，处理相关记录
                        if (update.status && update.status !== oldStatus) {
                            // 如果从其他状态变为回收状态，添加回收记录
                            if (update.status === 'recycled' && oldStatus !== 'recycled') {
                                const recycleRecord = {
                                    id: Date.now() + updateCount, // 确保ID唯一
                                    code: upcToUpdate.code,
                                    original_purpose: '批量状态更新回收',
                                    recycled_at: new Date().toISOString(),
                                    status: 'recycled'
                                };
                                recycleRecords.push(recycleRecord);
                            }

                            // 如果从回收状态变为其他状态，移除回收记录
                            if (oldStatus === 'recycled' && update.status !== 'recycled') {
                                const recycleIndex = recycleRecords.findIndex(r => r.code === upcToUpdate.code);
                                if (recycleIndex > -1) {
                                    recycleRecords.splice(recycleIndex, 1);
                                }
                            }
                        }

                        updateCount++;
                        console.log(`批量更新UPC码: ${upcToUpdate.code}, 状态: ${oldStatus} -> ${update.status || oldStatus}`);

                    } catch (error) {
                        errors.push(`更新UPC码 ${update.code} 时出错: ${error.message}`);
                    }
                }

                // 保存数据到文件
                saveData(UPC_CODES_FILE, upcCodes);
                saveData(RECYCLE_RECORDS_FILE, recycleRecords);

                // 更新数据版本，触发客户端同步
                updateDataVersion('upc');
                updateDataVersion('recycle');

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    message: `批量更新完成，成功更新 ${updateCount} 个UPC码`,
                    updateCount: updateCount,
                    errors: errors.length > 0 ? errors : undefined
                }));
                return;
            }

            if (pathname === '/api/stats') {
                const stats = {
                    available_codes: upcCodes.filter(c => c.status === 'available').length,
                    allocated_codes: upcCodes.filter(c => c.status === 'allocated').length,
                    recycled_codes: upcCodes.filter(c => c.status === 'recycled').length,
                    invalid_codes: upcCodes.filter(c => c.status === 'invalid').length,
                    total_applications: upcApplications.length,
                    total_users: users.length
                };

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    data: stats
                }));
                return;
            }

            if (pathname === '/api/user/history') {
                // 验证会话
                const sessionId = getSessionFromRequest(req);
                const session = validateSession(sessionId);

                if (!session) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '未授权访问，请重新登录'
                    }));
                    return;
                }

                // 获取当前用户信息
                const currentUser = users.find(u => u.username === session.username);
                if (!currentUser) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '用户不存在'
                    }));
                    return;
                }

                // 权限检查：管理员可以查看所有用户数据，普通用户只能查看自己的数据
                const requestedUserId = parsedUrl.query.userId;
                let targetUserId;

                if (currentUser.role === 'admin') {
                    // 管理员可以查看指定用户的数据，如果没有指定则查看所有数据
                    targetUserId = requestedUserId;
                } else {
                    // 普通用户只能查看自己的数据
                    targetUserId = currentUser.username;
                }

                // 获取用户申请记录
                let userApps;
                if (currentUser.role === 'admin' && !targetUserId) {
                    // 管理员查看所有用户数据
                    userApps = upcApplications;
                } else {
                    // 查看特定用户数据
                    userApps = upcApplications.filter(app => app.user_id === (targetUserId || 'demo_user'));
                }

                const history = userApps.map(app => {
                    // 根据request_id获取该申请对应的UPC码
                    const appCodes = upcCodes
                        .filter(code => code.request_id === app.request_id)
                        .map(code => {
                            // 检查是否被回收 - 修复逻辑：基于UPC码状态
                            const recycleRecord = recycleRecords.find(r => r.code === code.code);

                            // 修复状态判断逻辑：
                            // 1. 如果UPC码状态是recycled，则为已回收
                            // 2. 如果UPC码状态是allocated，则为已完成（未回收）
                            // 3. 如果UPC码状态是available，则检查回收记录的状态
                            // 4. 添加对 "completed" 状态的处理
                            let status = '已完成';
                            let isRecycled = false;
                            let recycleDate = null;

                            if (code.status === 'recycled') {
                                status = '已回收';
                                isRecycled = true;
                                if (recycleRecord) {
                                    recycleDate = new Date(recycleRecord.created_at).toLocaleDateString('zh-CN');
                                }
                            } else if (code.status === 'allocated') {
                                status = '已完成';
                                isRecycled = false;
                            } else if (code.status === 'completed') {
                                // 处理 completed 状态 - 根据回收记录判断
                                if (recycleRecord) {
                                    if (recycleRecord.status === 'recycled' || recycleRecord.status === 'completed') {
                                        status = '已回收';
                                        isRecycled = true;
                                        recycleDate = new Date(recycleRecord.created_at).toLocaleDateString('zh-CN');
                                    } else if (recycleRecord.status === 'reusable') {
                                        status = '可重用';
                                        isRecycled = false;
                                    } else {
                                        status = '已完成';
                                        isRecycled = false;
                                    }
                                } else {
                                    status = '已完成';
                                    isRecycled = false;
                                }
                            } else if (code.status === 'invalid') {
                                status = '无效';
                                isRecycled = false;
                            } else if (code.status === 'available') {
                                // 🔧 修复：如果UPC码状态是available，需要检查回收记录的具体状态
                                if (recycleRecord) {
                                    if (recycleRecord.status === 'recycled') {
                                        // 回收记录状态是recycled，显示为已回收
                                        status = '已回收';
                                        isRecycled = true;
                                        recycleDate = new Date(recycleRecord.created_at).toLocaleDateString('zh-CN');
                                    } else if (recycleRecord.status === 'reusable') {
                                        // 🔧 修复：回收记录状态是reusable，说明已重新激活，显示为可重用
                                        status = '可重用';
                                        isRecycled = false;
                                        // 不设置recycleDate，因为这是重新激活状态
                                    } else if (recycleRecord.status === 'processed') {
                                        // 回收记录状态是processed，说明已被标记为已使用
                                        status = '已处理';
                                        isRecycled = false;
                                    } else {
                                        // 其他状态，显示为已完成
                                        status = '已完成';
                                        isRecycled = false;
                                    }
                                } else {
                                    // 🔧 修复：没有回收记录的available状态UPC码，显示为可用
                                    status = '可用';
                                    isRecycled = false;
                                }
                            }

                            return {
                                code: code.code,
                                status: status,
                                recycled: isRecycled,
                                recycleDate: recycleDate,
                                currentStatus: code.status // 添加当前UPC码状态用于调试
                            };
                        });

                    return {
                        id: app.request_id,
                        date: new Date(app.created_at).toLocaleDateString('zh-CN'),
                        time: new Date(app.created_at).toLocaleTimeString('zh-CN', { hour12: false }),
                        purpose: app.purpose,
                        quantity: app.quantity,
                        applicant: getUserDisplayName(app.user_id), // 添加申请人信息
                        codes: appCodes
                    };
                });

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    data: history
                }));
                return;
            }

            if (pathname === '/api/upc/pool-stats') {
                // 验证会话
                const sessionId = getSessionFromRequest(req);
                const session = validateSession(sessionId);

                if (!session) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '未授权访问，请重新登录'
                    }));
                    return;
                }

                // 获取当前用户信息
                const currentUser = users.find(u => u.username === session.username);
                if (!currentUser) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '用户不存在'
                    }));
                    return;
                }

                // 权限检查：管理员可以查看全局统计，普通用户查看个人统计
                let stats;
                if (currentUser.role === 'admin') {
                    // 管理员查看全局统计
                    stats = {
                        total: upcCodes.length,
                        available: upcCodes.filter(c => c.status === 'available').length,
                        allocated: upcCodes.filter(c => c.status === 'allocated').length,
                        recycled: upcCodes.filter(c => c.status === 'recycled').length,
                        invalid: upcCodes.filter(c => c.status === 'invalid').length
                    };
                } else {
                    // 普通用户查看个人统计，但也需要知道系统可用UPC数量
                    const userCodes = upcCodes.filter(c => c.assigned_user === currentUser.username);
                    stats = {
                        total: userCodes.length,
                        available: upcCodes.filter(c => c.status === 'available').length, // 允许用户查看系统可用UPC数量
                        allocated: userCodes.filter(c => c.status === 'allocated').length,
                        recycled: userCodes.filter(c => c.status === 'recycled').length,
                        invalid: userCodes.filter(c => c.status === 'invalid').length
                    };
                }

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    data: stats
                }));
                return;
            }

            // 今日分配统计API
            if (pathname === '/api/upc/today-allocated') {
                // 验证会话
                const sessionId = getSessionFromRequest(req);
                const session = validateSession(sessionId);

                if (!session) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '未授权访问'
                    }));
                    return;
                }

                // 获取当前用户信息
                const currentUser = users.find(u => u.username === session.username);
                if (!currentUser) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '用户不存在'
                    }));
                    return;
                }

                try {
                    const today = new Date().toLocaleDateString('zh-CN');
                    let todayCount = 0;

                    if (currentUser.role === 'admin') {
                        // 管理员查看全局今日分配
                        todayCount = upcApplications.filter(app => {
                            const appDate = new Date(app.created_at).toLocaleDateString('zh-CN');
                            return appDate === today;
                        }).reduce((sum, app) => sum + app.quantity, 0);
                    } else {
                        // 普通用户查看个人今日分配
                        todayCount = upcApplications.filter(app => {
                            const appDate = new Date(app.created_at).toLocaleDateString('zh-CN');
                            return appDate === today && app.user_id === currentUser.username;
                        }).reduce((sum, app) => sum + app.quantity, 0);
                    }

                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        data: { count: todayCount }
                    }));
                } catch (error) {
                    console.error('获取今日分配统计失败:', error);
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: '获取今日分配统计失败',
                        data: { count: 0 }
                    }));
                }
                return;
            }

            if (pathname === '/api/upc/pool-data') {
                // 验证会话
                const sessionId = getSessionFromRequest(req);
                const session = validateSession(sessionId);

                if (!session) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '未授权访问，请重新登录'
                    }));
                    return;
                }

                // 获取当前用户信息
                const currentUser = users.find(u => u.username === session.username);
                if (!currentUser) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '用户不存在'
                    }));
                    return;
                }

                const page = parseInt(parsedUrl.query.page) || 1;
                const limit = parseInt(parsedUrl.query.limit) || 50;
                const offset = (page - 1) * limit;

                let filteredCodes;
                if (currentUser.role === 'admin') {
                    // 管理员可以查看所有UPC码
                    filteredCodes = upcCodes;
                } else {
                    // 普通用户可以查看：
                    // 1. 分配给自己的UPC码 (assigned_user === username)
                    // 2. 自己重新激活的UPC码 (assigned_user === null 但在回收记录中有自己的操作记录)
                    filteredCodes = upcCodes.filter(c => {
                        // 直接分配给用户的UPC码
                        if (c.assigned_user === currentUser.username) {
                            return true;
                        }

                        // 检查是否是用户重新激活的UPC码
                        if (c.assigned_user === null && c.status === 'available') {
                            // 查找回收记录，看是否是该用户回收或重新激活的
                            const recycleRecord = recycleRecords.find(r =>
                                r.code === c.code &&
                                (r.recycled_by === currentUser.username || r.status === 'reusable')
                            );
                            return !!recycleRecord;
                        }

                        return false;
                    });
                }

                const paginatedCodes = filteredCodes.slice(offset, offset + limit);

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    data: {
                        codes: paginatedCodes,
                        total: filteredCodes.length,
                        page: page,
                        limit: limit
                    }
                }));
                return;
            }

            // 兼容性API端点 - UPC池数据
            if (pathname === '/api/upc-pool' && method === 'GET') {
                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    data: upcCodes
                }));
                return;
            }

            // UPC申请历史API
            if (pathname === '/api/upc/history' && method === 'GET') {
                const userId = parsedUrl.query.userId || 'demo_user';
                const userApps = upcApplications.filter(app => app.user_id === userId);

                const history = userApps.map(app => {
                    // 根据request_id获取该申请对应的UPC码
                    const appCodes = upcCodes
                        .filter(code => code.request_id === app.request_id)
                        .map(code => {
                            // 检查是否被回收
                            const recycleRecord = recycleRecords.find(r => r.code === code.code);

                            let status = '已完成';
                            let isRecycled = false;
                            let recycleDate = null;

                            if (code.status === 'recycled') {
                                status = '已回收';
                                isRecycled = true;
                                if (recycleRecord) {
                                    recycleDate = new Date(recycleRecord.created_at).toLocaleDateString('zh-CN');
                                }
                            } else if (code.status === 'allocated') {
                                status = '已完成';
                                isRecycled = false;
                            } else if (code.status === 'completed') {
                                // 处理 completed 状态 - 根据回收记录判断
                                if (recycleRecord) {
                                    if (recycleRecord.status === 'recycled' || recycleRecord.status === 'completed') {
                                        status = '已回收';
                                        isRecycled = true;
                                        recycleDate = new Date(recycleRecord.created_at).toLocaleDateString('zh-CN');
                                    } else if (recycleRecord.status === 'reusable') {
                                        status = '可重用';
                                        isRecycled = false;
                                    } else {
                                        status = '已完成';
                                        isRecycled = false;
                                    }
                                } else {
                                    status = '已完成';
                                    isRecycled = false;
                                }
                            } else if (code.status === 'invalid') {
                                status = '无效';
                                isRecycled = false;
                            } else if (code.status === 'available') {
                                // 如果UPC码状态是available，需要检查回收记录的具体状态
                                if (recycleRecord) {
                                    if (recycleRecord.status === 'recycled') {
                                        status = '已回收';
                                        isRecycled = true;
                                        recycleDate = new Date(recycleRecord.created_at).toLocaleDateString('zh-CN');
                                    } else if (recycleRecord.status === 'reusable') {
                                        status = '可重用';
                                        isRecycled = false;
                                    } else {
                                        status = '已完成';
                                        isRecycled = false;
                                    }
                                } else {
                                    status = '可用';
                                    isRecycled = false;
                                }
                            }

                            return {
                                code: code.code,
                                status: status,
                                recycled: isRecycled,
                                recycleDate: recycleDate,
                                currentStatus: code.status
                            };
                        });

                    return {
                        id: app.request_id,
                        date: new Date(app.created_at).toLocaleDateString('zh-CN'),
                        time: new Date(app.created_at).toLocaleTimeString('zh-CN', { hour12: false }),
                        purpose: app.purpose,
                        quantity: app.quantity,
                        applicant: getUserDisplayName(app.user_id),
                        codes: appCodes
                    };
                });

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    data: history
                }));
                return;
            }

            // 回收记录API
            if (pathname === '/api/recycle-records' && method === 'GET') {
                // 验证会话
                const sessionId = getSessionFromRequest(req);
                const session = validateSession(sessionId);

                if (!session) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '未授权访问，请重新登录'
                    }));
                    return;
                }

                // 获取当前用户信息
                const currentUser = users.find(u => u.username === session.username);
                if (!currentUser) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '用户不存在'
                    }));
                    return;
                }

                // 权限检查和数据过滤
                let filteredRecords = recycleRecords;
                const requestedUserId = parsedUrl.query.userId;

                if (currentUser.role === 'admin') {
                    // 管理员可以查看所有记录或指定用户的记录
                    if (requestedUserId) {
                        filteredRecords = recycleRecords.filter(r => r.user_id === requestedUserId);
                    }
                    // 如果没有指定用户ID，返回所有记录
                } else {
                    // 普通用户只能查看自己的记录
                    filteredRecords = recycleRecords.filter(r => r.user_id === currentUser.username);
                }

                const formattedRecords = filteredRecords.map(record => ({
                    id: record.id,
                    code: record.code,
                    reason: record.reason || record.original_purpose || '回收',
                    status: record.status,
                    user_id: record.user_id,
                    created_at: record.created_at,
                    recycled_at: record.recycled_at,
                    date: new Date(record.created_at).toLocaleDateString('zh-CN'),
                    time: new Date(record.created_at).toLocaleTimeString('zh-CN', { hour12: false })
                }));

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    data: formattedRecords
                }));
                return;
            }

            if (pathname === '/api/recycle/history') {
                // 验证会话
                const sessionId = getSessionFromRequest(req);
                const session = validateSession(sessionId);

                if (!session) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '未授权访问，请重新登录'
                    }));
                    return;
                }

                // 获取当前用户信息
                const currentUser = users.find(u => u.username === session.username);
                if (!currentUser) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '用户不存在'
                    }));
                    return;
                }

                // 权限检查和数据过滤
                let filteredRecords = recycleRecords;
                const requestedUserId = parsedUrl.query.userId;

                if (currentUser.role === 'admin') {
                    // 管理员可以查看所有记录或指定用户的记录
                    if (requestedUserId) {
                        filteredRecords = recycleRecords.filter(r => r.user_id === requestedUserId);
                    }
                    // 如果没有指定用户ID，返回所有记录
                } else {
                    // 普通用户只能查看自己的记录
                    filteredRecords = recycleRecords.filter(r => r.user_id === currentUser.username);
                }

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    data: filteredRecords.map(r => {
                        // 🔧 修复：查找对应的UPC码ID，用于标记已使用功能
                        const upcCode = upcCodes.find(u => u.code === r.code);

                        // 🔧 调试：记录查找结果
                        if (!upcCode) {
                            console.log(`⚠️ 回收记录 ${r.code} 没有找到对应的UPC码`);
                        } else {
                            console.log(`✅ 回收记录 ${r.code} 找到UPC码ID: ${upcCode.id}`);
                        }

                        return {
                            id: r.id,
                            code: r.code,
                            upcId: upcCode ? upcCode.id : null, // 🔧 新增：添加UPC码ID
                            reason: r.reason,
                            status: r.status,
                            user: r.user_id,
                            date: new Date(r.created_at).toLocaleDateString('zh-CN'),
                            time: new Date(r.created_at).toLocaleTimeString('zh-CN', { hour12: false })
                        };
                    })
                }));
                return;
            }

            if (pathname === '/api/upc/batch-recycle' && method === 'POST') {
                const data = await parsePostData(req);
                let successCount = 0;
                let errors = [];

                data.codes.forEach(code => {
                    const upcCode = upcCodes.find(c => c.code === code);
                    if (upcCode && upcCode.status === 'allocated') {
                        upcCode.status = 'recycled';
                        successCount++;

                        recycleRecords.push({
                            id: Date.now() + Math.random() + successCount, // 使用时间戳+随机数+计数器确保唯一性
                            code: code,
                            reason: data.reason || '批量回收',
                            status: 'recycled',
                            user_id: 'demo_user',
                            created_at: new Date().toISOString()
                        });
                    } else {
                        errors.push(`UPC码 ${code} 不存在或已被回收`);
                    }
                });

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    message: `成功回收 ${successCount} 个UPC码`,
                    recycled_count: successCount,
                    errors: errors.length > 0 ? errors : undefined
                }));
                return;
            }

            if (pathname === '/api/recycle/batch' && method === 'POST') {
                const data = await parsePostData(req);
                const codes = data.codes || [];
                const purpose = data.purpose || '自定义回收';

                if (!Array.isArray(codes) || codes.length === 0) {
                    res.writeHead(400);
                    res.end(JSON.stringify({
                        success: false,
                        message: '无效的UPC码列表'
                    }));
                    return;
                }

                let successCount = 0;
                let alreadyRecycledCount = 0;
                let notFoundCount = 0;
                const errors = [];

                codes.forEach(code => {
                    try {
                        // 查找UPC码
                        const upcIndex = upcCodes.findIndex(c => c.code === code);

                        if (upcIndex === -1) {
                            notFoundCount++;
                            errors.push(`UPC码 ${code} 不存在`);
                            return;
                        }

                        const upcCode = upcCodes[upcIndex];

                        // 检查UPC码状态是否可以回收
                        if (upcCode.status === 'recycled') {
                            alreadyRecycledCount++;
                            errors.push(`UPC码 ${code} 已经被回收`);
                            return;
                        }

                        // 只有allocated状态的UPC码才能被回收
                        if (upcCode.status !== 'allocated') {
                            notFoundCount++;
                            errors.push(`UPC码 ${code} 状态为 ${upcCode.status}，无法回收`);
                            return;
                        }

                        // 检查是否在短时间内重复回收（防止重复回收问题）
                        const currentTime = new Date();
                        const lastRecycleTime = upcCode.last_recycled_at ? new Date(upcCode.last_recycled_at) : null;
                        const timeDiff = lastRecycleTime ? (currentTime - lastRecycleTime) / 1000 : Infinity; // 秒

                        if (lastRecycleTime && timeDiff < 5) { // 5秒内不允许重复回收
                            alreadyRecycledCount++;
                            errors.push(`UPC码 ${code} 刚刚被回收，请等待 ${Math.ceil(5 - timeDiff)} 秒后再试`);
                            return;
                        }

                        // 检查自动激活设置（从请求数据中获取，如果没有则默认为false）
                        const autoReactivateEnabled = data.autoReactivateEnabled || false;

                        // 根据自动激活设置决定状态
                        const newStatus = autoReactivateEnabled ? 'available' : 'recycled';
                        const recordStatus = autoReactivateEnabled ? 'reusable' : 'recycled';

                        // 更新UPC码状态
                        upcCode.status = newStatus;
                        upcCode.updated_at = currentTime.toISOString();
                        upcCode.last_recycled_at = currentTime.toISOString(); // 记录最后回收时间

                        // 添加回收记录
                        const recycleRecord = {
                            id: Date.now() + Math.random() + successCount, // 使用时间戳+随机数+计数器确保唯一性
                            code: code,
                            original_purpose: purpose,
                            recycled_at: currentTime.toISOString(),
                            status: recordStatus, // 使用根据自动激活设置决定的状态
                            user_id: 'system', // 可以根据需要修改
                            created_at: currentTime.toISOString()
                        };

                        recycleRecords.push(recycleRecord);
                        successCount++;

                        console.log(`自定义回收UPC码: ${code}, 原因: ${purpose}`);

                    } catch (error) {
                        errors.push(`处理UPC码 ${code} 时出错: ${error.message}`);
                    }
                });

                // 保存数据到文件
                saveData(UPC_CODES_FILE, upcCodes);
                saveData(RECYCLE_RECORDS_FILE, recycleRecords);

                // 更新数据版本，触发客户端同步
                updateDataVersion('upc');
                updateDataVersion('recycle');

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    message: `批量回收完成，成功回收 ${successCount} 个UPC码`,
                    recycled_count: successCount,
                    already_recycled_count: alreadyRecycledCount,
                    not_found_count: notFoundCount,
                    errors: errors.length > 0 ? errors : undefined
                }));
                return;
            }

            if (pathname === '/api/users' && method === 'GET') {
                // 验证会话
                const sessionId = getSessionFromRequest(req);
                const session = validateSession(sessionId);

                if (!session) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '未授权访问，请重新登录'
                    }));
                    return;
                }

                // 权限检查：只有管理员可以查看用户列表
                if (!validatePermission(session, 'user_management')) {
                    res.writeHead(403);
                    res.end(JSON.stringify({
                        success: false,
                        message: '权限不足，只有管理员可以查看用户列表'
                    }));
                    return;
                }

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    data: users.map(u => ({
                        id: u.id,
                        username: u.username,
                        name: u.name,
                        role: u.role,
                        email: u.email || '',
                        phone: u.phone || '',
                        created_at: u.created_at || new Date().toISOString(),
                        last_login: u.last_login || null,
                        status: u.status || 'active'
                    }))
                }));
                return;
            }

            if (pathname === '/api/users' && method === 'POST') {
                // 验证会话
                const sessionId = getSessionFromRequest(req);
                const session = validateSession(sessionId);

                if (!session) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '未授权访问，请重新登录'
                    }));
                    return;
                }

                // 权限检查：只有管理员可以创建用户
                if (!validatePermission(session, 'user_management')) {
                    res.writeHead(403);
                    res.end(JSON.stringify({
                        success: false,
                        message: '权限不足，只有管理员可以创建用户'
                    }));
                    return;
                }

                const data = await parsePostData(req);

                // 检查用户名是否已存在
                if (users.find(u => u.username === data.username)) {
                    res.writeHead(400);
                    res.end(JSON.stringify({
                        success: false,
                        message: '用户名已存在'
                    }));
                    return;
                }

                const newUser = {
                    id: users.length > 0 ? Math.max(...users.map(u => u.id)) + 1 : 1,
                    username: data.username,
                    password: data.password,
                    name: data.name,
                    role: data.role || 'user',
                    email: data.email || '',
                    phone: data.phone || '',
                    avatarType: data.avatarType || 'name',
                    created_at: new Date().toISOString(),
                    last_login: null
                };

                users.push(newUser);

                // 保存数据到文件
                saveData(USERS_FILE, users);

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    message: '用户添加成功',
                    user: {
                        id: newUser.id,
                        username: newUser.username,
                        name: newUser.name,
                        role: newUser.role
                    }
                }));
                return;
            }

            if (pathname.startsWith('/api/users/') && method === 'PUT') {
                // 验证会话
                const sessionId = getSessionFromRequest(req);
                const session = validateSession(sessionId);

                if (!session) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '未授权访问，请重新登录'
                    }));
                    return;
                }

                // 权限检查：只有管理员可以更新用户信息
                if (!validatePermission(session, 'user_management')) {
                    res.writeHead(403);
                    res.end(JSON.stringify({
                        success: false,
                        message: '权限不足，只有管理员可以更新用户信息'
                    }));
                    return;
                }

                const userId = parseInt(pathname.split('/')[3]);
                const data = await parsePostData(req);
                const userIndex = users.findIndex(u => u.id === userId);

                if (userIndex === -1) {
                    res.writeHead(404);
                    res.end(JSON.stringify({
                        success: false,
                        message: '用户不存在'
                    }));
                    return;
                }

                // 更新用户信息
                if (data.name) users[userIndex].name = data.name;
                if (data.role) users[userIndex].role = data.role;
                if (data.password) users[userIndex].password = data.password;
                if (data.email) users[userIndex].email = data.email;
                if (data.phone) users[userIndex].phone = data.phone;
                users[userIndex].updated_at = new Date().toISOString();

                // 保存数据到文件
                saveData(USERS_FILE, users);

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    message: '用户信息更新成功'
                }));
                return;
            }

            if (pathname.startsWith('/api/users/') && method === 'DELETE') {
                const userId = parseInt(pathname.split('/')[3]);
                const userIndex = users.findIndex(u => u.id === userId);

                if (userIndex === -1) {
                    res.writeHead(404);
                    res.end(JSON.stringify({
                        success: false,
                        message: '用户不存在'
                    }));
                    return;
                }

                users.splice(userIndex, 1);

                // 保存数据到文件
                saveData(USERS_FILE, users);

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    message: '用户删除成功'
                }));
                return;
            }

            // 重置用户密码API
            if (pathname.startsWith('/api/users/') && pathname.endsWith('/reset-password') && method === 'POST') {
                const userId = parseInt(pathname.split('/')[3]);
                const userIndex = users.findIndex(u => u.id === userId);

                if (userIndex === -1) {
                    res.writeHead(404);
                    res.end(JSON.stringify({
                        success: false,
                        message: '用户不存在'
                    }));
                    return;
                }

                // 生成新密码
                const newPassword = 'Reset' + Math.random().toString(36).slice(-6);
                users[userIndex].password = newPassword;

                // 保存数据到文件
                saveData(USERS_FILE, users);

                console.log(`🔑 用户 ${users[userIndex].name} 密码已重置为: ${newPassword}`);

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    message: '密码重置成功',
                    data: {
                        newPassword: newPassword
                    }
                }));
                return;
            }

            // UPC导入API
            if (pathname === '/api/upc/import' && method === 'POST') {
                const data = await parsePostData(req);

                if (!data.codes || !Array.isArray(data.codes)) {
                    res.writeHead(400);
                    res.end(JSON.stringify({
                        success: false,
                        message: 'UPC码数据格式错误'
                    }));
                    return;
                }

                const importedCount = importUPCCodes(data.codes);

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    message: `成功导入 ${importedCount} 个UPC码`,
                    importedCount: importedCount
                }));
                return;
            }

            // 批量更新UPC码API
            if (pathname === '/api/upc/batch-update' && method === 'POST') {
                const data = await parsePostData(req);

                if (!data.updates || !Array.isArray(data.updates)) {
                    res.writeHead(400);
                    res.end(JSON.stringify({
                        success: false,
                        message: '更新数据格式错误'
                    }));
                    return;
                }

                let updateCount = 0;
                let errors = [];

                data.updates.forEach(update => {
                    const { code, status, notes } = update;

                    if (!code) {
                        errors.push('UPC码不能为空');
                        return;
                    }

                    // 查找要更新的UPC码
                    const upcIndex = upcCodes.findIndex(u => u.code === code);

                    if (upcIndex === -1) {
                        errors.push(`UPC码 ${code} 不存在`);
                        return;
                    }

                    let hasChanges = false;

                    // 更新状态
                    if (status && upcCodes[upcIndex].status !== status) {
                        upcCodes[upcIndex].status = status;
                        hasChanges = true;
                    }

                    // 更新备注（如果提供了notes字段）
                    if (notes !== undefined && upcCodes[upcIndex].notes !== notes) {
                        upcCodes[upcIndex].notes = notes;
                        hasChanges = true;
                    }

                    if (hasChanges) {
                        upcCodes[upcIndex].updated_at = new Date().toISOString();
                        updateCount++;
                    }
                });

                // 保存数据到文件
                saveData(UPC_CODES_FILE, upcCodes);

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    message: `成功更新 ${updateCount} 个UPC码`,
                    updateCount: updateCount,
                    errors: errors.length > 0 ? errors : undefined
                }));
                return;
            }

            // UPC重新激活API
            if (pathname === '/api/upc/reactivate' && method === 'POST') {
                // 验证会话
                const sessionId = getSessionFromRequest(req);
                const session = validateSession(sessionId);

                if (!session) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '未授权访问，请重新登录'
                    }));
                    return;
                }

                // 获取当前用户信息
                const currentUser = users.find(u => u.username === session.username);
                if (!currentUser) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '用户不存在'
                    }));
                    return;
                }

                console.log(`🔄 用户 ${currentUser.username} (${currentUser.role}) 请求重新激活UPC码`);

                const data = await parsePostData(req);

                if (!data.upcCodes || !Array.isArray(data.upcCodes) || data.upcCodes.length === 0) {
                    res.writeHead(400);
                    res.end(JSON.stringify({
                        success: false,
                        message: 'UPC码列表不能为空'
                    }));
                    return;
                }

                console.log('🔄 开始重新激活UPC码:', data.upcCodes);

                let successCount = 0;
                const errors = [];

                data.upcCodes.forEach(upcCode => {
                    try {
                        // 查找UPC码
                        const upcIndex = upcCodes.findIndex(u => u.code === upcCode);

                        if (upcIndex === -1) {
                            errors.push(`UPC码 ${upcCode} 不存在`);
                            return;
                        }

                        const upc = upcCodes[upcIndex];

                        console.log(`🔍 检查UPC码 ${upcCode} 状态:`, {
                            当前状态: upc.status,
                            分配用户: upc.assigned_user,
                            更新时间: upc.updated_at
                        });

                        // 权限检查：普通用户只能重新激活自己的UPC码
                        if (currentUser.role !== 'admin') {
                            // 检查UPC码是否属于当前用户
                            // 对于已回收的UPC码，assigned_user可能为null，需要通过回收记录来验证
                            let hasPermission = false;

                            if (upc.assigned_user === currentUser.username) {
                                // 直接分配给用户的UPC码
                                hasPermission = true;
                            } else if (upc.assigned_user === null && upc.status === 'recycled') {
                                // 已回收的UPC码，检查回收记录
                                // 🔧 修复：检查 recycled_by 或 user_id 字段
                                const recycleRecord = recycleRecords.find(r =>
                                    r.code === upcCode && (
                                        r.recycled_by === currentUser.username ||
                                        r.user_id === currentUser.username
                                    )
                                );
                                if (recycleRecord) {
                                    hasPermission = true;
                                    console.log(`✅ 通过回收记录验证权限: ${upcCode} 由 ${currentUser.username} 回收`);
                                } else {
                                    // 🔧 调试：显示回收记录信息
                                    const allRecordsForCode = recycleRecords.filter(r => r.code === upcCode);
                                    console.log(`🔍 UPC码 ${upcCode} 的所有回收记录:`, allRecordsForCode.map(r => ({
                                        user_id: r.user_id,
                                        recycled_by: r.recycled_by,
                                        status: r.status
                                    })));
                                }
                            }

                            if (!hasPermission) {
                                const errorMsg = `UPC码 ${upcCode} 不属于您，无法重新激活`;
                                console.log(`❌ 权限检查失败: ${errorMsg}`);
                                console.log(`   UPC assigned_user: ${upc.assigned_user}`);
                                console.log(`   当前用户: ${currentUser.username}`);
                                errors.push(errorMsg);
                                return;
                            }
                        }

                        // 支持从多种状态重新激活到available
                        if (upc.status === 'available') {
                            const errorMsg = `UPC码 ${upcCode} 已经是可用状态，无需重新激活`;
                            console.log(`⚠️ ${errorMsg}`);
                            errors.push(errorMsg);
                            return;
                        }

                        if (upc.status === 'invalid') {
                            const errorMsg = `UPC码 ${upcCode} 状态为无效，无法重新激活`;
                            console.log(`❌ ${errorMsg}`);
                            errors.push(errorMsg);
                            return;
                        }

                        // 允许从 recycled 和 allocated 状态重新激活
                        if (upc.status !== 'recycled' && upc.status !== 'allocated') {
                            const errorMsg = `UPC码 ${upcCode} 当前状态为 ${upc.status}，无法重新激活`;
                            console.log(`❌ ${errorMsg}`);
                            errors.push(errorMsg);
                            return;
                        }

                        // 🔧 修复：更新UPC池状态为可用
                        const oldStatus = upcCodes[upcIndex].status;
                        const oldAssignedUser = upcCodes[upcIndex].assigned_user;
                        upcCodes[upcIndex].status = 'available';

                        // 🔧 修复：重新激活时将UPC码分配给原始用户，而不是当前操作的管理员
                        // 查找回收记录中的原始用户
                        const recycleRecord = recycleRecords.find(r => r.code === upcCode);
                        const originalUser = recycleRecord ? recycleRecord.user_id : oldAssignedUser;

                        upcCodes[upcIndex].assigned_user = originalUser;
                        upcCodes[upcIndex].updated_at = new Date().toISOString();

                        console.log(`🔄 UPC码重新分配: ${upcCode} -> ${originalUser} (原用户: ${oldAssignedUser})`);
                        console.log(`🔄 UPC码状态已更新: ${upcCode} (${oldStatus} -> available)`);
                        console.log(`📊 UPC码详细信息:`, {
                            code: upcCodes[upcIndex].code,
                            status: upcCodes[upcIndex].status,
                            assigned_user: upcCodes[upcIndex].assigned_user,
                            updated_at: upcCodes[upcIndex].updated_at
                        });

                        // 🔧 修复：同时更新回收记录的状态为可重用
                        console.log(`🔍 查找回收记录: ${upcCode}`);
                        const allRecordsForCode = recycleRecords.filter(r => r.code === upcCode);
                        console.log(`📊 所有回收记录:`, allRecordsForCode.map(r => ({code: r.code, status: r.status, id: r.id, user_id: r.user_id})));

                        // 🔧 修复：查找所有相关的回收记录并更新状态
                        let updatedRecords = 0;
                        allRecordsForCode.forEach(record => {
                            if (record.status === 'recycled' || record.status === 'completed') {
                                const oldStatus = record.status;
                                record.status = 'reusable';
                                record.updated_at = new Date().toISOString();
                                console.log(`🔄 更新回收记录状态: ${upcCode} (${oldStatus} -> reusable)`);
                                updatedRecords++;
                            }
                        });

                        if (updatedRecords === 0) {
                            console.log(`⚠️ 未找到需要更新的回收记录: ${upcCode}`);

                            // 如果没有找到合适的回收记录，但有其他状态的记录，也尝试更新
                            if (allRecordsForCode.length > 0) {
                                const latestRecord = allRecordsForCode[allRecordsForCode.length - 1];
                                const oldStatus = latestRecord.status;
                                latestRecord.status = 'reusable';
                                latestRecord.updated_at = new Date().toISOString();
                                console.log(`🔄 强制更新最新回收记录状态: ${upcCode} (${oldStatus} -> reusable)`);
                                updatedRecords++;
                            } else {
                                console.log(`❌ 完全没有找到回收记录: ${upcCode}`);
                            }
                        } else {
                            console.log(`✅ 成功更新 ${updatedRecords} 条回收记录`);
                        }

                        successCount++;
                        console.log(`✅ UPC码 ${upcCode} 已重新激活`);

                    } catch (error) {
                        console.error(`重新激活UPC码 ${upcCode} 失败:`, error);
                        errors.push(`UPC码 ${upcCode}: ${error.message}`);
                    }
                });

                // 保存数据到文件
                console.log(`💾 开始保存重新激活后的数据...`);
                const upcSaveResult = saveData(UPC_CODES_FILE, upcCodes);
                const recycleSaveResult = saveData(RECYCLE_RECORDS_FILE, recycleRecords);

                if (upcSaveResult && recycleSaveResult) {
                    console.log(`✅ 所有数据文件保存成功`);
                } else {
                    console.error(`❌ 数据文件保存失败: UPC=${upcSaveResult}, 回收=${recycleSaveResult}`);
                }

                // 更新数据版本，触发客户端同步
                updateDataVersion('upc');
                updateDataVersion('recycle');

                console.log(`🎉 重新激活完成: 成功 ${successCount} 个, 失败 ${errors.length} 个`);

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    message: `重新激活完成: 成功 ${successCount} 个`,
                    successCount: successCount,
                    errors: errors.length > 0 ? errors : undefined
                }));
                return;
            }

            // 报告保存API
            if (pathname === '/api/reports' && method === 'POST') {
                const data = await parsePostData(req);

                const report = {
                    id: reports.length + 1,
                    title: data.title || '数据分析报告',
                    content: data.content,
                    type: data.type || 'analysis',
                    created_at: new Date().toISOString(),
                    expires_at: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString() // 15天后过期
                };

                reports.push(report);
                saveData(REPORTS_FILE, reports);

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    message: '报告保存成功',
                    reportId: report.id
                }));
                return;
            }

            // 获取报告列表API
            if (pathname === '/api/reports' && method === 'GET') {
                // 清理过期报告
                const now = new Date();
                reports = reports.filter(report => new Date(report.expires_at) > now);
                saveData(REPORTS_FILE, reports);

                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    data: reports
                }));
                return;
            }

            // 系统设置API
            if (pathname === '/api/settings' && method === 'GET') {
                try {
                    // 加载系统设置
                    let allSettings = {};
                    try {
                        const settingsFile = path.join(DATA_DIR, 'system_settings.json');
                        const loadedData = loadData(settingsFile, {});
                        // 确保加载的数据是对象而不是数组
                        if (Array.isArray(loadedData) || typeof loadedData !== 'object') {
                            throw new Error('设置文件格式错误');
                        }
                        allSettings = loadedData;
                    } catch (error) {
                        // 如果文件不存在或格式错误，返回默认设置
                        allSettings = {
                            basic: {
                                systemName: 'UPC管理系统',
                                companyName: '深圳速拓电子商务有限公司',
                                timezone: 'Asia/Shanghai'
                            },
                            upc: {
                                maxAllocation: 20
                            },
                            security: {
                                minPasswordLength: 8,
                                maxLoginAttempts: 5,
                                sessionTimeout: 120,
                                enableLoginLog: true
                            },
                            stockAlert: {
                                enableStockAlert: true,
                                stockThreshold: 50,
                                alertBySystem: true
                            },
                            lastUpdated: new Date().toISOString()
                        };
                    }

                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        data: allSettings
                    }));
                } catch (error) {
                    console.error('获取系统设置失败:', error);
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: '获取设置失败'
                    }));
                }
                return;
            }

            if (pathname === '/api/settings' && method === 'POST') {
                const data = await parsePostData(req);
                const { category, settings } = data;

                try {
                    // 加载现有设置
                    let allSettings = {};
                    const settingsFile = path.join(DATA_DIR, 'system_settings.json');
                    try {
                        const loadedData = loadData(settingsFile, {});
                        // 确保加载的数据是对象而不是数组
                        if (Array.isArray(loadedData) || typeof loadedData !== 'object') {
                            throw new Error('设置文件格式错误');
                        }
                        allSettings = loadedData;
                    } catch (error) {
                        // 如果文件不存在或格式错误，创建默认设置
                        allSettings = {
                            basic: {},
                            upc: {},
                            security: {},
                            notification: {},
                            stockAlert: {}
                        };
                    }

                    // 更新指定分类的设置
                    allSettings[category] = { ...allSettings[category], ...settings };
                    allSettings.lastUpdated = new Date().toISOString();

                    // 保存设置
                    saveData(settingsFile, allSettings);

                    console.log(`✅ 系统设置已更新 - 分类: ${category}`, settings);

                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        message: '设置保存成功',
                        data: allSettings
                    }));
                } catch (error) {
                    console.error('保存系统设置失败:', error);
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: '保存设置失败'
                    }));
                }
                return;
            }

            // 操作日志API
            if (pathname === '/api/operation-log' && method === 'POST') {
                try {
                    const data = await parsePostData(req);
                    const { type, description, user, timestamp, ip, userAgent } = data;

                    // 加载现有日志
                    let operationLogs = loadData(OPERATION_LOG_FILE, []);

                    // 创建新的日志条目
                    const logEntry = {
                        id: Date.now(),
                        type: type || 'unknown',
                        description: description || '',
                        user: user || 'unknown',
                        timestamp: timestamp || new Date().toISOString(),
                        ip: ip || 'unknown',
                        userAgent: userAgent || 'unknown'
                    };

                    // 添加到日志数组开头
                    operationLogs.unshift(logEntry);

                    // 限制日志数量，保留最近1000条
                    if (operationLogs.length > 1000) {
                        operationLogs = operationLogs.slice(0, 1000);
                    }

                    // 保存日志
                    saveData(OPERATION_LOG_FILE, operationLogs);

                    // 同时记录到系统日志
                    logger.operation(type, description, user);

                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        message: '操作日志已记录'
                    }));
                } catch (error) {
                    console.error('记录操作日志失败:', error);
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: '记录操作日志失败'
                    }));
                }
                return;
            }

            if (pathname === '/api/operation-log' && method === 'GET') {
                try {
                    // 加载操作日志
                    const operationLogs = loadData(OPERATION_LOG_FILE, []);

                    // 获取查询参数
                    const url = new URL(req.url, `http://${req.headers.host}`);
                    const limit = parseInt(url.searchParams.get('limit')) || 50;
                    const offset = parseInt(url.searchParams.get('offset')) || 0;

                    // 分页返回
                    const paginatedLogs = operationLogs.slice(offset, offset + limit);

                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        data: paginatedLogs,
                        total: operationLogs.length
                    }));
                } catch (error) {
                    console.error('获取操作日志失败:', error);
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: '获取操作日志失败'
                    }));
                }
                return;
            }

            // 测试邮件服务API
            if (pathname === '/api/test/email' && method === 'POST') {
                try {
                    const data = await parsePostData(req);
                    const { recipient, subject, content } = data;

                    // 获取当前邮件配置
                    const settings = loadData('system_settings.json', {});
                    const emailConfig = settings.notification?.email;

                    // 检查邮件服务是否启用（支持两种配置方式）
                    const emailServiceEnabled = settings.notification?.enableEmailService || emailConfig?.enabled;
                    console.log('📧 邮件服务调试信息:', {
                        enableEmailService: settings.notification?.enableEmailService,
                        emailEnabled: emailConfig?.enabled,
                        emailServiceEnabled: emailServiceEnabled,
                        hasEmailConfig: !!emailConfig
                    });

                    // 临时修复：如果配置存在且任一启用字段为true，则认为服务已启用
                    if (!emailConfig) {
                        res.writeHead(400);
                        res.end(JSON.stringify({
                            success: false,
                            message: '邮件配置不存在，请先配置邮件服务'
                        }));
                        return;
                    }

                    if (!emailServiceEnabled) {
                        res.writeHead(400);
                        res.end(JSON.stringify({
                            success: false,
                            message: '邮件服务未启用，请先在通知设置中启用邮件服务'
                        }));
                        return;
                    }

                    if (!emailConfig.senderEmail || !emailConfig.emailPassword || !emailConfig.smtpServer) {
                        res.writeHead(400);
                        res.end(JSON.stringify({
                            success: false,
                            message: '邮件配置不完整，请检查SMTP服务器、发送邮箱和密码设置'
                        }));
                        return;
                    }

                    console.log('📧 测试邮件发送:', {
                        recipient,
                        subject,
                        smtp: emailConfig.smtpServer,
                        sender: emailConfig.senderEmail
                    });

                    // 重新加载邮件服务配置
                    emailService.reloadConfig();

                    console.log('📧 开始邮件测试，设置20秒超时...');

                    // 使用真实的邮件服务发送测试邮件，设置20秒总超时
                    const testPromise = emailService.sendTestEmail(recipient);
                    const timeoutPromise = new Promise((_, reject) =>
                        setTimeout(() => reject(new Error('邮件测试超时(20秒)')), 20000)
                    );

                    const sendResult = await Promise.race([testPromise, timeoutPromise]);

                    // 记录测试日志
                    logger.operation('EMAIL_TEST', `测试邮件发送到: ${recipient}`, 'admin');

                    if (sendResult.success) {
                        res.writeHead(200);
                        res.end(JSON.stringify({
                            success: true,
                            message: '测试邮件发送成功',
                            details: {
                                smtpServer: emailConfig.smtpServer,
                                smtpPort: emailConfig.smtpPort,
                                security: emailConfig.smtpSecurity,
                                senderEmail: emailConfig.senderEmail,
                                senderName: emailConfig.senderName,
                                recipient: recipient,
                                messageId: sendResult.messageId,
                                configStatus: '邮件发送成功，配置正确'
                            }
                        }));
                    } else {
                        // 详细的错误信息
                        const errorMessage = sendResult ? sendResult.message : '邮件发送失败';
                        const errorCode = sendResult ? sendResult.error : 'UNKNOWN_ERROR';

                        console.error('📧 邮件测试详细错误:', {
                            message: errorMessage,
                            error: errorCode,
                            config: {
                                smtpServer: emailConfig.smtpServer,
                                smtpPort: emailConfig.smtpPort,
                                security: emailConfig.smtpSecurity,
                                senderEmail: emailConfig.senderEmail
                            }
                        });

                        res.writeHead(400);
                        res.end(JSON.stringify({
                            success: false,
                            message: errorMessage,
                            details: {
                                smtpServer: emailConfig.smtpServer,
                                smtpPort: emailConfig.smtpPort,
                                security: emailConfig.smtpSecurity,
                                senderEmail: emailConfig.senderEmail,
                                error: errorCode,
                                duration: sendResult ? sendResult.duration : 0,
                                configStatus: '邮件配置有问题'
                            }
                        }));
                    }
                } catch (error) {
                    console.error('📧 邮件测试异常:', error);
                    logger.error(`邮件测试失败: ${error.message}`, 'EMAIL');
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: `邮件测试异常: ${error.message}`,
                        details: {
                            error: error.code || 'EXCEPTION',
                            stack: error.stack
                        }
                    }));
                }
                return;
            }

            // 测试短信服务API
            if (pathname === '/api/test/sms' && method === 'POST') {
                try {
                    const data = await parsePostData(req);
                    const { phone, content } = data;

                    // 获取当前短信配置
                    const settings = loadData('system_settings.json', {});
                    const smsConfig = settings.notification?.sms;

                    // 检查短信服务是否启用（支持两种配置方式）
                    const smsServiceEnabled = settings.notification?.enableSMSService || smsConfig?.enabled;
                    console.log('📱 短信服务调试信息:', {
                        enableSMSService: settings.notification?.enableSMSService,
                        smsEnabled: smsConfig?.enabled,
                        smsServiceEnabled: smsServiceEnabled,
                        hasSmsConfig: !!smsConfig
                    });
                    if (!smsConfig || !smsServiceEnabled) {
                        res.writeHead(400);
                        res.end(JSON.stringify({
                            success: false,
                            message: '短信服务未启用，请先在通知设置中启用短信服务'
                        }));
                        return;
                    }

                    if (!smsConfig.smsAccessKey || !smsConfig.smsAccessSecret || !smsConfig.smsSignature) {
                        res.writeHead(400);
                        res.end(JSON.stringify({
                            success: false,
                            message: '短信配置不完整，请检查AccessKey、AccessSecret和签名设置'
                        }));
                        return;
                    }

                    console.log('📱 测试短信发送:', {
                        phone,
                        content,
                        provider: smsConfig.smsProvider,
                        signature: smsConfig.smsSignature
                    });

                    // 重新加载短信服务配置
                    smsService.reloadConfig();

                    // 使用真实的短信服务发送测试短信
                    const sendResult = await smsService.sendTestSMS(phone);

                    // 记录测试日志
                    logger.operation('SMS_TEST', `测试短信发送到: ${phone}`, 'admin');

                    if (sendResult.success) {
                        res.writeHead(200);
                        res.end(JSON.stringify({
                            success: true,
                            message: '测试短信发送成功',
                            details: {
                                provider: smsConfig.smsProvider,
                                signature: smsConfig.smsSignature,
                                appId: smsConfig.smsAppId,
                                templateId: smsConfig.smsTemplateId,
                                phone: phone,
                                messageId: sendResult.messageId,
                                fee: sendResult.fee,
                                configStatus: '短信发送成功，配置正确'
                            }
                        }));
                    } else {
                        res.writeHead(500);
                        res.end(JSON.stringify({
                            success: false,
                            message: sendResult.message,
                            details: {
                                provider: smsConfig.smsProvider,
                                signature: smsConfig.smsSignature,
                                phone: phone,
                                error: sendResult.error
                            }
                        }));
                    }
                } catch (error) {
                    logger.error(`短信测试失败: ${error.message}`, 'SMS');
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: `短信测试失败: ${error.message}`
                    }));
                }
                return;
            }

            // 测试库存预警API
            if (pathname === '/api/test/stock-alert' && method === 'POST') {
                try {
                    // 获取前端传来的测试参数
                    const testData = await parsePostData(req);

                    // 获取系统设置
                    const settings = loadData('system_settings.json', {});
                    const stockAlertSettings = settings.stockAlert || {};

                    // 如果前端传来了测试参数，使用前端的设置进行测试
                    const testSettings = {
                        ...stockAlertSettings,
                        alertByEmail: testData.alertByEmail !== undefined ? testData.alertByEmail : stockAlertSettings.alertByEmail,
                        alertBySMS: testData.alertBySMS !== undefined ? testData.alertBySMS : stockAlertSettings.alertBySMS,
                        alertBySystem: testData.alertBySystem !== undefined ? testData.alertBySystem : stockAlertSettings.alertBySystem,
                        emailRecipients: testData.emailRecipients || stockAlertSettings.emailRecipients,
                        smsRecipients: testData.smsRecipients || stockAlertSettings.smsRecipients
                    };

                    // 获取当前库存统计
                    const availableCount = upcCodes.filter(c => c.status === 'available').length;
                    const allocatedCount = upcCodes.filter(c => c.status === 'allocated').length;
                    const recycledCount = upcCodes.filter(c => c.status === 'recycled').length;

                    // 使用设置中的阈值
                    const threshold = parseInt(testSettings.stockThreshold) || 50;

                    // 构造预警数据（强制触发预警进行测试）
                    const alertData = {
                        currentStock: availableCount,
                        allocated: allocatedCount,
                        recycled: recycledCount,
                        total: upcCodes.length,
                        threshold: threshold,
                        shortageRate: threshold > 0 ? ((threshold - availableCount) / threshold * 100).toFixed(1) : '0',
                        alertTime: new Date().toISOString(),
                        alertType: 'test_alert', // 测试模式
                        settings: {
                            enableStockAlert: testSettings.enableStockAlert,
                            alertByEmail: testSettings.alertByEmail,
                            alertBySMS: testSettings.alertBySMS,
                            alertBySystem: testSettings.alertBySystem,
                            alertFrequency: testSettings.alertFrequency
                        }
                    };

                    console.log('⚠️ 库存预警测试（强制触发）:', alertData);
                    console.log('📋 测试设置:', testSettings);

                    // 强制触发预警通知进行测试
                    const sendResults = await sendStockAlertNotifications(alertData, testSettings);

                    // 构造详细的响应信息
                    let message = '✅ 预警通知测试完成！\n\n';
                    let hasSuccess = false;
                    let hasFailure = false;

                    if (testSettings.alertBySystem) {
                        message += `🔔 系统通知: ${sendResults.system.success ? '✅ 成功' : '❌ 失败'}\n`;
                        if (sendResults.system.success) hasSuccess = true;
                        else hasFailure = true;
                    }

                    if (testSettings.alertByEmail) {
                        message += `📧 邮件通知: ${sendResults.email.success ? '✅ 成功' : '❌ 失败'} (${sendResults.email.message})\n`;
                        if (sendResults.email.success) hasSuccess = true;
                        else hasFailure = true;
                    }

                    if (testSettings.alertBySMS) {
                        message += `📱 短信通知: ${sendResults.sms.success ? '✅ 成功' : '❌ 失败'} (${sendResults.sms.message})\n`;
                        if (sendResults.sms.success) hasSuccess = true;
                        else hasFailure = true;
                    }

                    message += `\n📊 当前库存: ${availableCount}个`;
                    message += `\n📋 测试时间: ${new Date().toLocaleString('zh-CN')}`;

                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: hasSuccess,
                        message: message,
                        data: {
                            ...alertData,
                            sendResults: sendResults
                        }
                    }));
                } catch (error) {
                    console.error('库存预警测试失败:', error);
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: '预警测试失败: ' + error.message,
                        error: error.message
                    }));
                }
                return;
            }

            // 系统信息API
            if (pathname === '/api/system/info' && method === 'GET') {
                try {
                    const os = require('os');
                    const fs = require('fs');
                    const path = require('path');

                    // 获取系统运行时间
                    const uptime = process.uptime();
                    const days = Math.floor(uptime / 86400);
                    const hours = Math.floor((uptime % 86400) / 3600);
                    const minutes = Math.floor((uptime % 3600) / 60);
                    const uptimeString = `${days}天 ${hours}小时 ${minutes}分钟`;

                    // 获取内存使用情况
                    const memUsage = process.memoryUsage();
                    const totalMem = os.totalmem();
                    const freeMem = os.freemem();
                    const usedMem = totalMem - freeMem;

                    // 获取磁盘使用情况（简化版）
                    let diskInfo = { used: 0, total: 0 };
                    try {
                        const stats = fs.statSync(__dirname);
                        diskInfo = {
                            used: Math.round(memUsage.rss / 1024 / 1024), // 使用进程内存作为近似值
                            total: Math.round(totalMem / 1024 / 1024)
                        };
                    } catch (error) {
                        // 使用默认值
                        diskInfo = { used: 128, total: 1024 };
                    }

                    // 获取最后备份时间
                    let lastBackupTime = '未备份';
                    try {
                        const backupDir = './test_backups';
                        if (fs.existsSync(backupDir)) {
                            const files = fs.readdirSync(backupDir)
                                .filter(file => file.startsWith('backup_'))
                                .map(file => {
                                    const filePath = path.join(backupDir, file);
                                    return {
                                        name: file,
                                        time: fs.statSync(filePath).mtime
                                    };
                                })
                                .sort((a, b) => b.time - a.time);

                            if (files.length > 0) {
                                lastBackupTime = files[0].time.toLocaleString('zh-CN');
                            }
                        }
                    } catch (error) {
                        console.error('获取备份信息失败:', error.message);
                    }

                    // 获取数据库统计
                    const dbStats = {
                        upcCodes: upcCodes.length,
                        applications: upcApplications.length,
                        recycleRecords: recycleRecords.length
                    };

                    const systemInfo = {
                        version: 'V2.8.4',
                        buildDate: '2025-07-07',
                        environment: process.env.NODE_ENV || 'development',
                        nodeVersion: process.version,
                        platform: os.platform(),
                        arch: os.arch(),
                        uptime: uptimeString,
                        memory: {
                            used: Math.round(memUsage.rss / 1024 / 1024),
                            total: Math.round(totalMem / 1024 / 1024),
                            usage: Math.round((memUsage.rss / totalMem) * 100)
                        },
                        disk: diskInfo,
                        lastBackup: lastBackupTime,
                        database: dbStats,
                        services: {
                            email: loadData('system_settings.json', {}).notification?.enableEmailService ||
                                   loadData('system_settings.json', {}).notification?.email?.enabled || false,
                            sms: loadData('system_settings.json', {}).notification?.enableSMSService ||
                                 loadData('system_settings.json', {}).notification?.sms?.enabled || false,
                            backup: true,
                            logging: logger && typeof logger.isEnabled === 'function' ? logger.isEnabled() : true
                        }
                    };

                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        data: systemInfo
                    }));
                } catch (error) {
                    console.error('获取系统信息失败:', error);
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: '获取系统信息失败',
                        error: error.message
                    }));
                }
                return;
            }

            // 实时同步检查API
            if (pathname === '/api/sync/check' && method === 'GET') {
                try {
                    // 验证用户身份
                    const authHeader = req.headers.authorization;
                    if (!authHeader || !authHeader.startsWith('Bearer ')) {
                        res.writeHead(401, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ success: false, message: '未授权访问' }));
                        return;
                    }

                    const sessionId = authHeader.substring(7);
                    const session = userSessions.get(sessionId);
                    if (!session) {
                        res.writeHead(401, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ success: false, message: '会话已过期' }));
                        return;
                    }

                    // 返回当前数据版本信息
                    res.writeHead(200, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({
                        success: true,
                        data: dataVersions,
                        timestamp: Date.now()
                    }));
                } catch (error) {
                    console.error('同步检查API错误:', error);
                    res.writeHead(500, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ success: false, message: '服务器内部错误' }));
                }
                return;
            }

            // UPC码管理API
            if (pathname === '/api/upc-codes' && method === 'GET') {
                try {
                    const upcCodes = loadData('upc_codes.json');
                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        data: upcCodes
                    }));
                } catch (error) {
                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        data: []
                    }));
                }
                return;
            }

            if (pathname === '/api/upc-codes' && method === 'POST') {
                const data = await parsePostData(req);
                try {
                    let upcCodes = [];
                    try {
                        upcCodes = loadData('upc_codes.json');
                    } catch (error) {
                        // 文件不存在，创建空数组
                    }

                    const newUPCCode = {
                        id: upcCodes.length > 0 ? Math.max(...upcCodes.map(u => u.id)) + 1 : 1,
                        code: data.code,
                        status: data.status || 'available',
                        created_at: new Date().toISOString(),
                        allocated_at: data.status === 'allocated' ? new Date().toISOString() : null,
                        allocated_to: data.allocated_to || null
                    };

                    upcCodes.push(newUPCCode);
                    saveData('upc_codes.json', upcCodes);

                    console.log(`✅ UPC码已添加: ${newUPCCode.code}`);

                    res.writeHead(201);
                    res.end(JSON.stringify({
                        success: true,
                        data: newUPCCode
                    }));
                } catch (error) {
                    console.error('添加UPC码失败:', error);
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: '添加UPC码失败'
                    }));
                }
                return;
            }

            // 申请记录API
            if (pathname === '/api/applications' && method === 'GET') {
                try {
                    const applications = loadData('applications.json');
                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        data: applications
                    }));
                } catch (error) {
                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        data: []
                    }));
                }
                return;
            }

            if (pathname === '/api/applications' && method === 'POST') {
                const data = await parsePostData(req);
                try {
                    let applications = [];
                    try {
                        applications = loadData('applications.json');
                    } catch (error) {
                        // 文件不存在，创建空数组
                    }

                    const newApplication = {
                        id: applications.length > 0 ? Math.max(...applications.map(a => a.id)) + 1 : 1,
                        applicant_name: data.applicant_name,
                        company_name: data.company_name,
                        contact_email: data.contact_email,
                        contact_phone: data.contact_phone,
                        product_name: data.product_name,
                        product_description: data.product_description,
                        quantity: parseInt(data.quantity) || 1,
                        status: data.status || 'pending',
                        created_at: new Date().toISOString(),
                        processed_at: data.status === 'approved' ? new Date().toISOString() : null,
                        allocated_codes: data.allocated_codes || []
                    };

                    applications.push(newApplication);
                    saveData('applications.json', applications);

                    console.log(`✅ 申请记录已创建: ${newApplication.id} - ${newApplication.applicant_name}`);

                    res.writeHead(201);
                    res.end(JSON.stringify({
                        success: true,
                        data: newApplication
                    }));
                } catch (error) {
                    console.error('创建申请记录失败:', error);
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: '创建申请记录失败'
                    }));
                }
                return;
            }

            // 手动备份API
            if (pathname === '/api/backup/create' && method === 'POST') {
                try {
                    logger.operation('BACKUP_CREATE', '手动创建备份', 'admin');
                    const result = await backupService.performBackup();

                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        message: '备份创建成功',
                        data: result
                    }));
                } catch (error) {
                    logger.error(`手动备份失败: ${error.message}`, 'BACKUP');
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: `备份失败: ${error.message}`
                    }));
                }
                return;
            }

            // 获取备份列表API
            if (pathname === '/api/backup/list' && method === 'GET') {
                try {
                    const backupList = backupService.getBackupList();

                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        message: '获取备份列表成功',
                        data: backupList
                    }));
                } catch (error) {
                    logger.error(`获取备份列表失败: ${error.message}`, 'BACKUP');
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: `获取备份列表失败: ${error.message}`
                    }));
                }
                return;
            }

            // 恢复备份API
            if (pathname === '/api/backup/restore' && method === 'POST') {
                try {
                    const data = await parsePostData(req);
                    const backupFileName = data.backupFileName;

                    if (!backupFileName) {
                        res.writeHead(400);
                        res.end(JSON.stringify({
                            success: false,
                            message: '请指定要恢复的备份文件名'
                        }));
                        return;
                    }

                    logger.operation('BACKUP_RESTORE', `恢复备份: ${backupFileName}`, 'admin');

                    // 使用备份服务验证和准备恢复
                    const restoreResult = await backupService.prepareRestore(backupFileName);

                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        message: `备份文件 ${backupFileName} 验证成功，已准备恢复`,
                        data: {
                            backupFile: restoreResult.backupFile,
                            backupPath: restoreResult.backupPath,
                            backupSize: restoreResult.backupSize,
                            createTime: restoreResult.createTime,
                            warning: '⚠️ 恢复操作将覆盖当前所有数据，此操作不可逆！',
                            recommendation: '🔧 建议联系系统管理员进行恢复操作',
                            steps: [
                                '1. 确认当前数据已备份',
                                '2. 停止所有用户操作',
                                '3. 解压备份文件',
                                '4. 替换数据文件',
                                '5. 重启系统服务',
                                '6. 验证数据完整性'
                            ]
                        }
                    }));

                } catch (error) {
                    logger.error(`恢复备份失败: ${error.message}`, 'BACKUP');
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: `恢复备份失败: ${error.message}`
                    }));
                }
                return;
            }

            // 删除备份文件API
            if (pathname === '/api/backup/delete' && method === 'POST') {
                try {
                    const data = await parsePostData(req);
                    const backupFileName = data.backupFileName;

                    if (!backupFileName) {
                        res.writeHead(400);
                        res.end(JSON.stringify({
                            success: false,
                            message: '请指定要删除的备份文件名'
                        }));
                        return;
                    }

                    logger.operation('BACKUP_DELETE', `删除备份: ${backupFileName}`, 'admin');

                    const result = await backupService.deleteBackup(backupFileName);

                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        message: `备份文件 ${backupFileName} 已删除`,
                        data: result
                    }));

                } catch (error) {
                    logger.error(`删除备份失败: ${error.message}`, 'BACKUP');
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: `删除备份失败: ${error.message}`
                    }));
                }
                return;
            }

            // 清理过期备份API
            if (pathname === '/api/backup/cleanup' && method === 'POST') {
                try {
                    logger.operation('BACKUP_CLEANUP', '清理过期备份', 'admin');
                    const result = await backupService.cleanupOldBackups();

                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        message: '过期备份清理完成',
                        data: result
                    }));
                } catch (error) {
                    logger.error(`清理过期备份失败: ${error.message}`, 'BACKUP');
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: `清理过期备份失败: ${error.message}`
                    }));
                }
                return;
            }

            // 测试短信模板API
            if (pathname === '/api/test/sms-template' && method === 'POST') {
                try {
                    const data = await parsePostData(req);
                    const { phone, templateParams } = data;

                    // 获取当前短信配置
                    const settings = loadData('system_settings.json', {});
                    const smsConfig = settings.notification?.sms;

                    // 检查短信服务是否启用（支持两种配置方式）
                    const smsServiceEnabled = settings.notification?.enableSMSService || smsConfig?.enabled;
                    if (!smsConfig || !smsServiceEnabled) {
                        res.writeHead(400);
                        res.end(JSON.stringify({
                            success: false,
                            message: '短信服务未启用，请先在通知设置中启用短信服务'
                        }));
                        return;
                    }

                    console.log('📱 测试短信模板发送:', {
                        phone,
                        templateParams,
                        provider: smsConfig.smsProvider,
                        templateId: smsConfig.smsTemplateId
                    });

                    // 重新加载短信服务配置
                    smsService.reloadConfig();

                    // 使用指定的模板参数发送短信
                    const sendResult = await smsService.sendSMS(phone, '', templateParams);

                    // 记录测试日志
                    logger.operation('SMS_TEMPLATE_TEST', `测试短信模板发送到: ${phone}`, 'admin');

                    if (sendResult.success) {
                        res.writeHead(200);
                        res.end(JSON.stringify({
                            success: true,
                            message: '短信模板测试发送成功',
                            details: {
                                provider: smsConfig.smsProvider,
                                templateId: smsConfig.smsTemplateId,
                                phone: phone,
                                templateParams: templateParams,
                                messageId: sendResult.messageId,
                                fee: sendResult.fee,
                                configStatus: '短信模板发送成功'
                            }
                        }));
                    } else {
                        res.writeHead(500);
                        res.end(JSON.stringify({
                            success: false,
                            message: sendResult.message,
                            details: {
                                provider: smsConfig.smsProvider,
                                templateId: smsConfig.smsTemplateId,
                                phone: phone,
                                templateParams: templateParams,
                                error: sendResult.error
                            }
                        }));
                    }
                } catch (error) {
                    logger.error(`短信模板测试失败: ${error.message}`, 'SMS');
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: `短信模板测试失败: ${error.message}`
                    }));
                }
                return;
            }

            // 获取日志文件列表API
            if (pathname === '/api/logs/list' && method === 'GET') {
                try {
                    const logFiles = logger.getLogFiles();

                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        message: '获取日志文件列表成功',
                        data: logFiles
                    }));
                } catch (error) {
                    logger.error(`获取日志文件列表失败: ${error.message}`, 'SYSTEM');
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: `获取日志文件列表失败: ${error.message}`
                    }));
                }
                return;
            }

            // 读取日志文件内容API
            if (pathname.startsWith('/api/logs/read/') && method === 'GET') {
                try {
                    const fileName = pathname.split('/api/logs/read/')[1];
                    const url = new URL(req.url, `http://${req.headers.host}`);
                    const lines = parseInt(url.searchParams.get('lines')) || 50;

                    const logContent = logger.readLogFile(fileName, lines);

                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        message: '读取日志文件成功',
                        data: {
                            fileName: fileName,
                            lines: logContent
                        }
                    }));
                } catch (error) {
                    logger.error(`读取日志文件失败: ${error.message}`, 'SYSTEM');
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: `读取日志文件失败: ${error.message}`
                    }));
                }
                return;
            }

            // 下载日志文件API
            if (pathname.startsWith('/api/logs/download/') && method === 'GET') {
                try {
                    const fileName = pathname.split('/api/logs/download/')[1];
                    const logPath = path.join(__dirname, 'logs', fileName);

                    // 检查文件是否存在
                    if (!fs.existsSync(logPath)) {
                        res.writeHead(404);
                        res.end(JSON.stringify({
                            success: false,
                            message: '日志文件不存在'
                        }));
                        return;
                    }

                    // 设置下载响应头
                    res.setHeader('Content-Type', 'application/octet-stream');
                    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);

                    // 创建文件流并发送
                    const fileStream = fs.createReadStream(logPath);
                    fileStream.pipe(res);

                    fileStream.on('error', (error) => {
                        logger.error(`下载日志文件失败: ${error.message}`, 'SYSTEM');
                        if (!res.headersSent) {
                            res.writeHead(500);
                            res.end(JSON.stringify({
                                success: false,
                                message: `下载日志文件失败: ${error.message}`
                            }));
                        }
                    });

                } catch (error) {
                    logger.error(`下载日志文件失败: ${error.message}`, 'SYSTEM');
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: `下载日志文件失败: ${error.message}`
                    }));
                }
                return;
            }

            // 调试API：读取原始文件数据
            if (pathname === '/api/debug/raw-data') {
                const sessionId = getSessionFromRequest(req);
                const session = validateSession(sessionId);

                if (!session) {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '未授权访问，请重新登录'
                    }));
                    return;
                }

                try {
                    // 直接读取文件内容
                    const rawUpcCodes = fs.existsSync(UPC_CODES_FILE) ?
                        JSON.parse(fs.readFileSync(UPC_CODES_FILE, 'utf8')) : [];
                    const rawRecycleRecords = fs.existsSync(RECYCLE_RECORDS_FILE) ?
                        JSON.parse(fs.readFileSync(RECYCLE_RECORDS_FILE, 'utf8')) : [];
                    const rawApplications = fs.existsSync(APPLICATIONS_FILE) ?
                        JSON.parse(fs.readFileSync(APPLICATIONS_FILE, 'utf8')) : [];

                    console.log(`🔍 调试API: 读取原始文件数据`);
                    console.log(`   UPC码数量: ${rawUpcCodes.length}`);
                    console.log(`   回收记录数量: ${rawRecycleRecords.length}`);
                    console.log(`   申请记录数量: ${rawApplications.length}`);

                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        data: {
                            upcCodes: rawUpcCodes,
                            recycleRecords: rawRecycleRecords,
                            applications: rawApplications,
                            timestamp: new Date().toISOString()
                        }
                    }));
                    return;
                } catch (error) {
                    console.error('读取原始文件数据失败:', error);
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: '读取文件数据失败'
                    }));
                    return;
                }
            }

            // 调试API：获取文件时间戳
            if (pathname === '/api/debug/file-timestamps') {
                try {
                    const timestamps = {};

                    if (fs.existsSync(UPC_CODES_FILE)) {
                        timestamps.upcCodes = fs.statSync(UPC_CODES_FILE).mtime;
                    }

                    if (fs.existsSync(RECYCLE_RECORDS_FILE)) {
                        timestamps.recycleRecords = fs.statSync(RECYCLE_RECORDS_FILE).mtime;
                    }

                    if (fs.existsSync(APPLICATIONS_FILE)) {
                        timestamps.applications = fs.statSync(APPLICATIONS_FILE).mtime;
                    }

                    console.log(`🔍 调试API: 文件时间戳查询`);
                    console.log(`   文件数量: ${Object.keys(timestamps).length}`);

                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        data: timestamps
                    }));
                    return;
                } catch (error) {
                    console.error('获取文件时间戳失败:', error);
                    res.writeHead(500);
                    res.end(JSON.stringify({
                        success: false,
                        message: '获取文件时间戳失败'
                    }));
                    return;
                }
            }

            // 默认API响应
            res.writeHead(404);
            res.end(JSON.stringify({
                success: false,
                message: 'API端点不存在'
            }));

        } catch (error) {
            console.error('❌ API错误:', error.message);
            console.error('📍 请求路径:', pathname);
            console.error('🔍 错误详情:', error.stack);
            res.writeHead(500);
            res.end(JSON.stringify({
                success: false,
                message: '服务器内部错误',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            }));
        }
        return;
    }

    // 静态文件服务
    let filePath = pathname === '/' ? '/index.html' : pathname;
    filePath = path.join(__dirname, 'public', filePath);

    fs.readFile(filePath, (err, data) => {
        if (err) {
            res.writeHead(404);
            res.end('File not found');
            return;
        }

        const mimeType = getMimeType(filePath);
        res.setHeader('Content-Type', mimeType);
        res.writeHead(200);
        res.end(data);
    });

    // 🔧 新增：性能监控 - 记录请求完成时间
    const responseTime = Date.now() - requestStartTime;
    performanceMonitor.recordRequest(responseTime, isError);
});

server.listen(PORT, () => {
    console.log('🚀 UPC管理系统 V2.8.4 Linux版已启动');
    console.log(`📍 访问地址: http://localhost:${PORT}`);
    console.log(`🔧 环境: ${NODE_ENV}`);
    console.log(`🏭 生产模式: ${IS_PRODUCTION ? '启用' : '禁用'}`);
    console.log('💾 数据存储: 本地文件系统');
    console.log('📅 启动时间: ' + new Date().toLocaleString('zh-CN'));

    if (IS_PRODUCTION) {
        console.log('🔒 生产环境特性:');
        console.log('   - 详细日志已禁用');
        console.log('   - 错误堆栈已隐藏');
        console.log('   - 调试信息已关闭');
    } else {
        console.log('🔍 开发环境特性:');
        console.log('   - 详细日志已启用');
        console.log('   - 错误堆栈已显示');
        console.log('   - 调试信息已开启');
    }

    console.log('👤 系统账户:');
    console.log('   管理员: sutuo_admin / Sutuo@2025!');
    console.log('   业务经理: manager / Manager@2025');
    console.log('   操作员: operator / Operator@2025');
    console.log('✅ 系统就绪，等待用户连接...');
});

// 错误处理
server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.error(`❌ 端口 ${PORT} 已被占用，请检查是否有其他程序在使用该端口`);
        console.log('💡 解决方案:');
        console.log('   1. 关闭占用端口的程序');
        console.log('   2. 或者修改 simple-server.js 中的 PORT 变量');
    } else {
        console.error('❌ 服务器启动失败:', err.message);
    }
    process.exit(1);
});

// 优雅关闭
process.on('SIGTERM', () => {
    console.log('📴 收到SIGTERM信号，正在关闭服务器...');
    server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('\n📴 收到SIGINT信号，正在关闭服务器...');
    server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
    });
});
