# UPC管理系统 V2.8.1 Linux版部署指南

## 📋 系统要求

- **操作系统**: CentOS 8+, Ubuntu 18.04+, RHEL 8+
- **Node.js**: 14.0.0 或更高版本
- **内存**: 最低 2GB RAM
- **磁盘**: 最低 1GB 可用空间
- **权限**: root 或 sudo 权限

## 🚀 快速安装

### 一键安装（推荐）
```bash
chmod +x install.sh
sudo ./install.sh
```

### 手动安装
```bash
# 1. 安装Node.js
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo dnf install -y nodejs

# 2. 安装依赖
npm install

# 3. 配置服务
sudo cp upc-system.service /etc/systemd/system/
sudo systemctl daemon-reload

# 4. 启动服务
sudo systemctl start upc-system
sudo systemctl enable upc-system
```

## 🔧 服务管理

### systemd 命令
```bash
# 启动服务
sudo systemctl start upc-system

# 停止服务
sudo systemctl stop upc-system

# 重启服务
sudo systemctl restart upc-system

# 查看状态
sudo systemctl status upc-system

# 开机自启
sudo systemctl enable upc-system

# 查看日志
sudo journalctl -u upc-system -f
```

### 手动启动
```bash
chmod +x start.sh
./start.sh
```

## 👤 默认账户

| 角色 | 用户名 | 密码 | 权限 |
|------|--------|------|------|
| 系统管理员 | admin | admin123 | 系统管理、用户管理、备份恢复 |
| 业务经理 | manager | Manager@2025 | UPC管理、报表查看 |
| 操作员 | operator | Operator@2025 | UPC申请、回收操作 |

## 🛡️ 安全配置

### 防火墙设置
```bash
# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=3001/tcp
sudo firewall-cmd --reload

# Ubuntu
sudo ufw allow 3001
```

### SSL证书配置
建议使用 Nginx 反向代理配置 HTTPS：
```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;

    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    location / {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📊 监控和维护

### 性能监控
```bash
# 查看进程
ps aux | grep node

# 查看端口
netstat -tlnp | grep 3001

# 查看资源使用
top -p $(pgrep -f simple-server.js)
```

### 日志管理
```bash
# 查看应用日志
tail -f logs/$(date +%Y-%m-%d).log

# 查看系统日志
sudo journalctl -u upc-system --since "1 hour ago"

# 日志轮转
sudo logrotate -f /etc/logrotate.d/upc-system
```

### 数据备份
```bash
# 创建备份
tar -czf upc-backup-$(date +%Y%m%d).tar.gz data/ logs/

# 定时备份（添加到crontab）
0 2 * * * /path/to/backup-script.sh
```

## 🔍 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   sudo systemctl status upc-system
   sudo journalctl -u upc-system -n 50
   ```

2. **端口被占用**
   ```bash
   sudo lsof -i :3001
   sudo kill -9 <PID>
   ```

3. **权限问题**
   ```bash
   sudo chown -R upc-system:upc-system /opt/upc-system
   sudo chmod +x /opt/upc-system/*.sh
   ```

### 性能优化
```bash
# 调整系统限制
echo "upc-system soft nofile 65536" >> /etc/security/limits.conf
echo "upc-system hard nofile 65536" >> /etc/security/limits.conf

# 优化Node.js
export NODE_OPTIONS="--max-old-space-size=2048"
```
