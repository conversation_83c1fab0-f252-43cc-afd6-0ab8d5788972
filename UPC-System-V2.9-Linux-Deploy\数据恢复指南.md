# UPC管理系统V2.9 数据恢复指南

## 问题说明

在V2.9升级过程中，系统数据被清空，导致回收记录显示不完整。这是因为升级过程中执行了数据清理操作。

## 问题现象

- 回收记录页面只显示少量记录（如3条）
- UPC池中没有历史数据
- 统计数据显示为0或很小的数值

## 解决方案

### 方案1：从备份恢复数据（推荐）

如果您有V2.8.4的数据备份：

1. **查找备份文件**
   ```bash
   # 在服务器上查找备份文件
   find /path/to/upc-system -name "*.zip" -type f
   # 或者查看备份目录
   ls -la data/backups/
   ```

2. **恢复备份数据**
   ```bash
   # 停止服务
   sudo systemctl stop upc-system
   
   # 备份当前数据（以防万一）
   cp -r data data_backup_$(date +%Y%m%d_%H%M%S)
   
   # 解压备份文件到临时目录
   unzip data/backups/backup_YYYYMMDD_HHMMSS.zip -d /tmp/restore/
   
   # 恢复数据文件
   cp /tmp/restore/data/*.json data/
   
   # 重启服务
   sudo systemctl start upc-system
   ```

### 方案2：使用示例数据（测试环境）

如果这是测试环境，可以使用我们提供的示例数据：

1. **当前部署包已包含示例数据**
   - 10个UPC码记录
   - 7个回收记录
   - 包含不同状态的数据

2. **验证数据**
   ```bash
   # 检查数据文件
   cat data/upc_codes.json | jq length
   cat data/recycle_records.json | jq length
   ```

### 方案3：手动重建数据

如果没有备份且需要重建数据：

1. **导入UPC码**
   - 使用系统的"批量导入"功能
   - 准备CSV格式的UPC码文件
   - 通过Web界面导入

2. **重新创建回收记录**
   - 对已使用的UPC码执行回收操作
   - 系统会自动生成回收记录

## 数据文件说明

### upc_codes.json
存储所有UPC码信息，包括：
- id: UPC码唯一标识
- code: UPC码值
- status: 状态（allocated/recycled/available）
- purpose: 用途
- user_id: 用户ID
- request_id: 申请ID
- created_at: 创建时间

### recycle_records.json
存储回收记录，包括：
- id: 记录唯一标识
- code: UPC码值
- reason: 回收原因
- status: 状态（recycled/reusable）
- user_id: 用户ID
- recycled_by: 回收操作者
- created_at: 创建时间

## 预防措施

1. **定期备份**
   ```bash
   # 设置定时备份（每天凌晨2点）
   echo "0 2 * * * cd /path/to/upc-system && zip -r data/backups/backup_$(date +\%Y\%m\%d_\%H\%M\%S).zip data/*.json" | crontab -
   ```

2. **升级前备份**
   - 在系统升级前务必备份所有数据
   - 验证备份文件的完整性

3. **监控数据完整性**
   - 定期检查数据文件大小
   - 监控记录数量变化

## 联系支持

如果遇到数据恢复问题，请提供：
- 系统版本信息
- 错误日志
- 备份文件（如有）
- 问题发生的具体时间

## 验证恢复结果

恢复数据后，请验证：
1. 登录系统查看回收记录页面
2. 检查统计数据是否正确
3. 测试回收和重新激活功能
4. 确认所有功能正常工作
