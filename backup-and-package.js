// UPC管理系统备份和打包脚本
const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

// 获取当前时间戳
const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
const version = 'V2.9.1'; // 更新版本号，包含最新修复

console.log('🚀 开始UPC管理系统备份和打包...');
console.log(`📅 时间戳: ${timestamp}`);
console.log(`🔖 版本: ${version}`);

// 创建备份目录
const backupDir = `backup/${version}-${timestamp}`;
if (!fs.existsSync('backup')) {
    fs.mkdirSync('backup');
}
if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
}

// 需要备份的文件和目录
const filesToBackup = [
    'simple-server.js',
    'package.json',
    'package-lock.json',
    'public/index.html',
    'data',
    'logs',
    'backup-service.js',
    'email-service.js',
    'sms-service.js',
    'logger-service.js',
    'performance-monitor.js',
    'data-integrity-service.js',
    'system_settings.json',
    'install.sh',
    'start.sh',
    'deploy.sh',
    'upc-system.service'
];

// 需要排除的文件和目录
const excludePatterns = [
    'node_modules',
    'test-*.js',
    'test-*.html',
    'fix-*.js',
    'verify-*.js',
    'final-verification.js',
    '*.backup.*',
    'logs/*.log',
    'data/backups'
];

// 复制文件函数
function copyFileOrDir(src, dest) {
    try {
        const stat = fs.statSync(src);
        
        if (stat.isDirectory()) {
            // 创建目录
            if (!fs.existsSync(dest)) {
                fs.mkdirSync(dest, { recursive: true });
            }
            
            // 复制目录内容
            const files = fs.readdirSync(src);
            files.forEach(file => {
                const srcPath = path.join(src, file);
                const destPath = path.join(dest, file);
                
                // 检查是否需要排除
                const shouldExclude = excludePatterns.some(pattern => {
                    if (pattern.includes('*')) {
                        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
                        return regex.test(file) || regex.test(srcPath);
                    }
                    return file === pattern || srcPath.includes(pattern);
                });
                
                if (!shouldExclude) {
                    copyFileOrDir(srcPath, destPath);
                }
            });
        } else {
            // 复制文件
            fs.copyFileSync(src, dest);
        }
    } catch (error) {
        console.warn(`⚠️ 跳过文件: ${src} (${error.message})`);
    }
}

// 执行备份
console.log('\n📦 开始备份文件...');
filesToBackup.forEach(item => {
    if (fs.existsSync(item)) {
        const destPath = path.join(backupDir, item);
        const destDir = path.dirname(destPath);
        
        if (!fs.existsSync(destDir)) {
            fs.mkdirSync(destDir, { recursive: true });
        }
        
        copyFileOrDir(item, destPath);
        console.log(`✅ 已备份: ${item}`);
    } else {
        console.log(`⚠️ 文件不存在: ${item}`);
    }
});

// 创建版本信息文件
const versionInfo = {
    version: version,
    timestamp: timestamp,
    buildDate: new Date().toISOString(),
    description: 'UPC管理系统 - 包含最新修复',
    fixes: [
        'UPC码ID自动生成修复',
        'UPC管理池批量操作修复',
        '回收管理页面位置保持修复',
        '数据完整性检查和修复',
        '性能监控和优化'
    ],
    features: [
        '完整的UPC码管理功能',
        '用户权限管理',
        '回收记录管理',
        '邮件和短信通知',
        '数据备份和恢复',
        '系统监控和日志',
        'Linux服务部署支持'
    ],
    requirements: {
        node: '>=14.0.0',
        npm: '>=6.0.0',
        os: 'Linux/Windows',
        memory: '>=512MB',
        disk: '>=1GB'
    }
};

fs.writeFileSync(
    path.join(backupDir, 'VERSION_INFO.json'),
    JSON.stringify(versionInfo, null, 2),
    'utf8'
);

// 创建README文件
const readmeContent = `# UPC管理系统 ${version}

## 📋 版本信息
- **版本**: ${version}
- **构建时间**: ${new Date().toLocaleString('zh-CN')}
- **构建时间戳**: ${timestamp}

## 🔧 最新修复
- ✅ UPC码ID自动生成修复
- ✅ UPC管理池批量操作修复  
- ✅ 回收管理页面位置保持修复
- ✅ 数据完整性检查和修复
- ✅ 性能监控和优化

## 🚀 部署说明

### 快速部署
\`\`\`bash
# 1. 解压文件
unzip UPC-System-${version}-*.zip
cd UPC-System-${version}-*

# 2. 安装依赖
npm install

# 3. 启动服务
npm start
# 或
node simple-server.js
\`\`\`

### Linux服务部署
\`\`\`bash
# 1. 运行安装脚本
chmod +x install.sh
sudo ./install.sh

# 2. 启动服务
sudo systemctl start upc-system
sudo systemctl enable upc-system
\`\`\`

## 📊 系统要求
- Node.js >= 14.0.0
- NPM >= 6.0.0
- 内存 >= 512MB
- 磁盘空间 >= 1GB

## 🔗 访问地址
- 系统地址: http://localhost:3001
- 默认账户: admin / admin123

## 📞 技术支持
如有问题请联系系统管理员。
`;

fs.writeFileSync(
    path.join(backupDir, 'README.md'),
    readmeContent,
    'utf8'
);

console.log(`✅ 备份完成: ${backupDir}`);

// 创建压缩包
console.log('\n📦 开始创建压缩包...');

const zipFileName = `UPC-System-${version}-${timestamp}.zip`;
const output = fs.createWriteStream(zipFileName);
const archive = archiver('zip', {
    zlib: { level: 9 } // 最高压缩级别
});

output.on('close', () => {
    const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
    console.log(`✅ 压缩包创建完成: ${zipFileName}`);
    console.log(`📁 压缩包大小: ${sizeInMB} MB`);
    console.log(`📊 压缩了 ${archive.pointer()} 字节`);
    
    // 创建部署包（不包含数据文件）
    createDeployPackage();
});

output.on('error', (err) => {
    console.error('❌ 创建压缩包失败:', err);
});

archive.on('warning', (err) => {
    if (err.code === 'ENOENT') {
        console.warn('⚠️ 警告:', err);
    } else {
        throw err;
    }
});

archive.on('error', (err) => {
    console.error('❌ 压缩失败:', err);
});

archive.pipe(output);

// 添加备份目录到压缩包
archive.directory(backupDir, `UPC-System-${version}`);

// 完成压缩
archive.finalize();

// 创建部署包（不包含数据文件）
function createDeployPackage() {
    console.log('\n📦 开始创建部署包（不含数据）...');

    const deployZipFileName = `UPC-System-${version}-Deploy-${timestamp}.zip`;
    const cleanZipFileName = `UPC-System-${version}-Clean-${timestamp}.zip`;
    const deployOutput = fs.createWriteStream(deployZipFileName);
    const deployArchive = archiver('zip', {
        zlib: { level: 9 }
    });
    
    deployOutput.on('close', () => {
        const sizeInMB = (deployArchive.pointer() / 1024 / 1024).toFixed(2);
        console.log(`✅ 部署包创建完成: ${deployZipFileName}`);
        console.log(`📁 部署包大小: ${sizeInMB} MB`);
        
        // 创建清理版本（仅核心文件）
        createCleanPackage(cleanZipFileName);
    });
    
    deployArchive.pipe(deployOutput);
    
    // 添加文件到部署包（排除数据文件）
    const deployFiles = [
        'simple-server.js',
        'package.json',
        'public/index.html',
        'backup-service.js',
        'email-service.js',
        'sms-service.js',
        'logger-service.js',
        'performance-monitor.js',
        'data-integrity-service.js',
        'install.sh',
        'start.sh',
        'deploy.sh',
        'upc-system.service'
    ];
    
    deployFiles.forEach(file => {
        const srcPath = path.join(backupDir, file);
        if (fs.existsSync(srcPath)) {
            if (fs.statSync(srcPath).isDirectory()) {
                deployArchive.directory(srcPath, `UPC-System-${version}-Deploy/${file}`);
            } else {
                deployArchive.file(srcPath, { name: `UPC-System-${version}-Deploy/${file}` });
            }
        }
    });
    
    // 添加版本信息和README
    deployArchive.file(path.join(backupDir, 'VERSION_INFO.json'), { 
        name: `UPC-System-${version}-Deploy/VERSION_INFO.json` 
    });
    deployArchive.file(path.join(backupDir, 'README.md'), { 
        name: `UPC-System-${version}-Deploy/README.md` 
    });
    
    // 创建空的数据目录结构
    deployArchive.append('{}', { name: `UPC-System-${version}-Deploy/data/users.json` });
    deployArchive.append('[]', { name: `UPC-System-${version}-Deploy/data/upc_codes.json` });
    deployArchive.append('[]', { name: `UPC-System-${version}-Deploy/data/applications.json` });
    deployArchive.append('[]', { name: `UPC-System-${version}-Deploy/data/recycle_records.json` });
    deployArchive.append('[]', { name: `UPC-System-${version}-Deploy/data/reports.json` });
    
    deployArchive.finalize();
}

// 创建清理版本（仅核心文件）
function createCleanPackage(cleanZipFileName) {
    console.log('\n📦 开始创建清理版本...');
    const cleanOutput = fs.createWriteStream(cleanZipFileName);
    const cleanArchive = archiver('zip', {
        zlib: { level: 9 }
    });
    
    cleanOutput.on('close', () => {
        const sizeInMB = (cleanArchive.pointer() / 1024 / 1024).toFixed(2);
        console.log(`✅ 清理版本创建完成: ${cleanZipFileName}`);
        console.log(`📁 清理版本大小: ${sizeInMB} MB`);
        
        console.log('\n🎉 所有打包任务完成！');
        console.log('\n📦 生成的文件:');
        console.log(`   1. 完整备份: UPC-System-${version}-${timestamp}.zip`);
        console.log(`   2. 部署包: UPC-System-${version}-Deploy-${timestamp}.zip`);
        console.log(`   3. 清理版本: ${cleanZipFileName}`);
    });
    
    cleanArchive.pipe(cleanOutput);
    
    // 只包含核心运行文件
    const coreFiles = [
        'simple-server.js',
        'package.json',
        'public/index.html'
    ];
    
    coreFiles.forEach(file => {
        const srcPath = path.join(backupDir, file);
        if (fs.existsSync(srcPath)) {
            if (fs.statSync(srcPath).isDirectory()) {
                cleanArchive.directory(srcPath, `UPC-System-${version}-Clean/${file}`);
            } else {
                cleanArchive.file(srcPath, { name: `UPC-System-${version}-Clean/${file}` });
            }
        }
    });
    
    cleanArchive.finalize();
}
