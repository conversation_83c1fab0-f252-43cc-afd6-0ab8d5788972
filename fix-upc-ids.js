// 修复UPC码ID问题的脚本
const fs = require('fs');
const path = require('path');

// 文件路径
const UPC_CODES_FILE = path.join(__dirname, 'data', 'upc_codes.json');

function fixUPCIds() {
    console.log('🔧 开始修复UPC码ID问题...');
    
    try {
        // 读取UPC码数据
        const upcData = JSON.parse(fs.readFileSync(UPC_CODES_FILE, 'utf8'));
        console.log(`📊 总共 ${upcData.length} 个UPC码`);
        
        // 统计问题
        const nullIdCodes = upcData.filter(code => code.id === null || code.id === undefined);
        console.log(`❌ 发现 ${nullIdCodes.length} 个UPC码的ID为null`);
        
        if (nullIdCodes.length === 0) {
            console.log('✅ 所有UPC码的ID都正常，无需修复');
            return;
        }
        
        // 找到当前最大的ID
        const validIds = upcData
            .filter(code => code.id !== null && code.id !== undefined)
            .map(code => {
                // 处理字符串ID（如"upc_001"）和数字ID
                if (typeof code.id === 'string' && code.id.startsWith('upc_')) {
                    return parseInt(code.id.replace('upc_', ''));
                } else if (typeof code.id === 'number') {
                    return code.id;
                } else {
                    return 0;
                }
            });
        
        const maxId = validIds.length > 0 ? Math.max(...validIds) : 0;
        console.log(`📋 当前最大ID: ${maxId}`);
        
        // 为null ID的UPC码分配新ID
        let nextId = maxId + 1;
        let fixedCount = 0;
        
        nullIdCodes.forEach(code => {
            const newId = `upc_${String(nextId).padStart(3, '0')}`;
            console.log(`🔧 修复UPC码 ${code.code}: null -> ${newId}`);
            code.id = newId;
            nextId++;
            fixedCount++;
        });
        
        // 备份原文件
        const backupFile = UPC_CODES_FILE + '.backup.' + Date.now();
        fs.copyFileSync(UPC_CODES_FILE, backupFile);
        console.log(`💾 原文件已备份到: ${backupFile}`);
        
        // 保存修复后的数据
        fs.writeFileSync(UPC_CODES_FILE, JSON.stringify(upcData, null, 2), 'utf8');
        console.log(`✅ 修复完成！共修复 ${fixedCount} 个UPC码的ID`);
        
        // 验证修复结果
        const verifyData = JSON.parse(fs.readFileSync(UPC_CODES_FILE, 'utf8'));
        const stillNullIds = verifyData.filter(code => code.id === null || code.id === undefined);
        
        if (stillNullIds.length === 0) {
            console.log('🎉 验证通过！所有UPC码现在都有有效的ID');
        } else {
            console.log(`⚠️ 警告：仍有 ${stillNullIds.length} 个UPC码的ID为null`);
        }
        
    } catch (error) {
        console.error('❌ 修复过程中发生错误:', error);
    }
}

// 运行修复
fixUPCIds();
