// 修复剩余的null ID问题
const fs = require('fs');
const path = require('path');

// 文件路径
const UPC_CODES_FILE = path.join(__dirname, 'data', 'upc_codes.json');

function fixRemainingNullIds() {
    console.log('🔧 检查并修复剩余的null ID问题...');
    
    try {
        // 读取UPC码数据
        const upcData = JSON.parse(fs.readFileSync(UPC_CODES_FILE, 'utf8'));
        console.log(`📊 总共 ${upcData.length} 个UPC码`);
        
        // 检查null ID
        const nullIdCodes = upcData.filter(code => code.id === null || code.id === undefined);
        console.log(`❌ 仍有 ${nullIdCodes.length} 个UPC码的ID为null`);
        
        if (nullIdCodes.length === 0) {
            console.log('✅ 所有UPC码的ID都正常');
            return;
        }
        
        // 显示有问题的UPC码
        nullIdCodes.forEach(code => {
            console.log(`❌ UPC码 ${code.code} 的ID为null`);
        });
        
        // 找到当前最大的ID
        const validIds = upcData
            .filter(code => code.id !== null && code.id !== undefined)
            .map(code => {
                if (typeof code.id === 'string' && code.id.startsWith('upc_')) {
                    return parseInt(code.id.replace('upc_', ''));
                } else if (typeof code.id === 'number') {
                    return code.id;
                } else {
                    return 0;
                }
            });
        
        const maxId = validIds.length > 0 ? Math.max(...validIds) : 0;
        console.log(`📋 当前最大ID: ${maxId}`);
        
        // 为null ID的UPC码分配新ID
        let nextId = maxId + 1;
        let fixedCount = 0;
        
        nullIdCodes.forEach(code => {
            const newId = `upc_${String(nextId).padStart(3, '0')}`;
            console.log(`🔧 修复UPC码 ${code.code}: null -> ${newId}`);
            code.id = newId;
            nextId++;
            fixedCount++;
        });
        
        if (fixedCount > 0) {
            // 备份原文件
            const backupFile = UPC_CODES_FILE + '.backup.' + Date.now();
            fs.copyFileSync(UPC_CODES_FILE, backupFile);
            console.log(`💾 原文件已备份到: ${backupFile}`);
            
            // 保存修复后的数据
            fs.writeFileSync(UPC_CODES_FILE, JSON.stringify(upcData, null, 2), 'utf8');
            console.log(`✅ 修复完成！共修复 ${fixedCount} 个UPC码的ID`);
        }
        
        // 验证修复结果
        const verifyData = JSON.parse(fs.readFileSync(UPC_CODES_FILE, 'utf8'));
        const stillNullIds = verifyData.filter(code => code.id === null || code.id === undefined);
        
        if (stillNullIds.length === 0) {
            console.log('🎉 验证通过！所有UPC码现在都有有效的ID');
        } else {
            console.log(`⚠️ 警告：仍有 ${stillNullIds.length} 个UPC码的ID为null`);
        }
        
    } catch (error) {
        console.error('❌ 修复过程中发生错误:', error);
    }
}

// 运行修复
fixRemainingNullIds();
