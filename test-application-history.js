// 测试申请记录历史的脚本
const http = require('http');

let sessionToken = '';

// 先登录获取会话
function login(callback) {
    console.log('🔐 正在登录获取会话...');
    
    const loginData = JSON.stringify({
        username: 'admin',
        password: 'admin123'
    });
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(loginData)
        }
    };
    
    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                if (response.success && response.sessionId) {
                    sessionToken = response.sessionId;
                    console.log('✅ 登录成功，获取到会话令牌');
                    callback();
                } else {
                    console.log('❌ 登录失败:', response.message);
                }
            } catch (error) {
                console.log('❌ 解析登录响应失败:', error.message);
            }
        });
    });
    
    req.on('error', (error) => {
        console.log('❌ 登录请求失败:', error.message);
    });
    
    req.write(loginData);
    req.end();
}

// 测试申请记录历史
function testApplicationHistory() {
    console.log('\n🧪 测试申请记录历史...');
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/user/history',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${sessionToken}`
        }
    };
    
    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                if (response.success && response.data) {
                    const history = response.data;
                    console.log(`📊 获取到 ${history.length} 个申请记录`);
                    
                    // 查找包含已删除UPC码的申请记录
                    let deletedUPCCount = 0;
                    let applicationsWithDeleted = 0;
                    let processedUPCCount = 0;
                    let applicationsWithProcessed = 0;
                    
                    history.forEach((app, index) => {
                        console.log(`\n📋 申请记录 ${index + 1}:`);
                        console.log(`   申请ID: ${app.id}`);
                        console.log(`   申请时间: ${app.date}`);
                        console.log(`   UPC码数量: ${app.codes ? app.codes.length : 0}`);
                        
                        if (app.codes && app.codes.length > 0) {
                            const statusCounts = {};
                            app.codes.forEach(code => {
                                const status = code.status || '未知';
                                statusCounts[status] = (statusCounts[status] || 0) + 1;
                                
                                if (status === '已删除') {
                                    deletedUPCCount++;
                                    if (statusCounts[status] === 1) {
                                        applicationsWithDeleted++;
                                    }
                                }
                                
                                if (status === '已处理') {
                                    processedUPCCount++;
                                    if (statusCounts[status] === 1) {
                                        applicationsWithProcessed++;
                                    }
                                }
                            });
                            
                            console.log(`   状态统计:`, statusCounts);
                            
                            // 显示已删除的UPC码详情
                            const deletedCodes = app.codes.filter(c => c.status === '已删除');
                            if (deletedCodes.length > 0) {
                                console.log(`   🗑️ 已删除的UPC码:`);
                                deletedCodes.forEach(code => {
                                    console.log(`      - ${code.code} (删除时间: ${code.deleted_at || '无'})`);
                                });
                            }
                            
                            // 显示已处理的UPC码详情
                            const processedCodes = app.codes.filter(c => c.status === '已处理');
                            if (processedCodes.length > 0) {
                                console.log(`   🔧 已处理的UPC码:`);
                                processedCodes.forEach(code => {
                                    console.log(`      - ${code.code} (当前状态: ${code.currentStatus || '未知'})`);
                                });
                            }
                        }
                    });
                    
                    console.log(`\n📊 总体统计:`);
                    console.log(`   - 包含已删除UPC码的申请记录: ${applicationsWithDeleted} 个`);
                    console.log(`   - 已删除的UPC码总数: ${deletedUPCCount} 个`);
                    console.log(`   - 包含已处理UPC码的申请记录: ${applicationsWithProcessed} 个`);
                    console.log(`   - 已处理的UPC码总数: ${processedUPCCount} 个`);
                    
                    console.log('\n🎉 申请记录历史测试完成！');
                    
                } else {
                    console.log('❌ 获取申请记录历史失败:', response.message);
                }
            } catch (error) {
                console.log('❌ 解析申请记录历史响应失败:', error.message);
            }
        });
    });
    
    req.on('error', (error) => {
        console.log('❌ 获取申请记录历史请求失败:', error.message);
    });
    
    req.end();
}

// 运行测试
console.log('🚀 开始测试申请记录历史...');
login(() => {
    testApplicationHistory();
});
