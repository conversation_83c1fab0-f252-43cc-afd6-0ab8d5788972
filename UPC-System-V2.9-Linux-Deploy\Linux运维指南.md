# UPC管理系统 V2.9 Linux运维指南

## 📋 日常运维任务

### 1. 服务状态检查
```bash
#!/bin/bash
# 服务状态检查脚本

echo "=== UPC管理系统状态检查 ==="
echo "时间: $(date)"
echo

# 检查服务状态
echo "🔍 服务状态:"
systemctl is-active upc-system && echo "✅ 服务运行正常" || echo "❌ 服务未运行"

# 检查端口
echo "🔍 端口状态:"
netstat -tlnp | grep :3001 && echo "✅ 端口3001正常监听" || echo "❌ 端口3001未监听"

# 检查进程
echo "🔍 进程状态:"
pgrep -f simple-server.js && echo "✅ 进程运行正常" || echo "❌ 进程未运行"

# 检查磁盘空间
echo "🔍 磁盘空间:"
df -h /opt/upc-system

# 检查内存使用
echo "🔍 内存使用:"
ps aux | grep simple-server.js | grep -v grep | awk '{print "CPU: "$3"%, MEM: "$4"%"}'
```

### 2. 日志管理
```bash
#!/bin/bash
# 日志管理脚本

LOG_DIR="/opt/upc-system/logs"
BACKUP_DIR="/backup/upc-logs"
RETENTION_DAYS=30

# 创建备份目录
mkdir -p $BACKUP_DIR

# 压缩旧日志
find $LOG_DIR -name "*.log" -mtime +7 -exec gzip {} ;

# 备份日志
find $LOG_DIR -name "*.gz" -mtime +1 -exec cp {} $BACKUP_DIR ;

# 清理过期备份
find $BACKUP_DIR -name "*.gz" -mtime +$RETENTION_DAYS -delete

echo "日志管理完成: $(date)"
```

### 3. 数据备份
```bash
#!/bin/bash
# 数据备份脚本

BACKUP_DIR="/backup/upc-data"
SOURCE_DIR="/opt/upc-system"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 停止服务（可选）
# systemctl stop upc-system

# 创建备份
tar -czf "$BACKUP_DIR/upc-backup-$DATE.tar.gz"     -C $SOURCE_DIR     data/ logs/     --exclude="logs/*.log"

# 启动服务（如果之前停止了）
# systemctl start upc-system

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "upc-backup-*.tar.gz" -mtime +30 -delete

echo "备份完成: $BACKUP_DIR/upc-backup-$DATE.tar.gz"
```

## 🔧 维护命令集合

### 服务管理
```bash
# 重启服务
sudo systemctl restart upc-system

# 重新加载配置
sudo systemctl reload upc-system

# 查看详细状态
sudo systemctl status upc-system -l

# 查看启动失败原因
sudo systemctl --failed
```

### 日志查看
```bash
# 实时查看日志
sudo journalctl -u upc-system -f

# 查看最近100行日志
sudo journalctl -u upc-system -n 100

# 查看特定时间段日志
sudo journalctl -u upc-system --since "2025-01-01" --until "2025-01-02"

# 查看应用日志
tail -f /opt/upc-system/logs/$(date +%Y-%m-%d).log
```

### 性能监控
```bash
# 查看资源使用
htop -p $(pgrep -f simple-server.js)

# 查看网络连接
ss -tlnp | grep :3001

# 查看文件描述符使用
lsof -p $(pgrep -f simple-server.js) | wc -l
```

## 🚨 故障处理流程

### 1. 服务无法启动
```bash
# 检查配置文件
sudo -u upc-system node -c /opt/upc-system/simple-server.js

# 检查端口占用
sudo lsof -i :3001

# 检查权限
ls -la /opt/upc-system/

# 手动启动测试
sudo -u upc-system node /opt/upc-system/simple-server.js
```

### 2. 性能问题
```bash
# 检查CPU使用
top -p $(pgrep -f simple-server.js)

# 检查内存泄漏
ps -o pid,vsz,rss,comm -p $(pgrep -f simple-server.js)

# 检查磁盘IO
iotop -p $(pgrep -f simple-server.js)
```

### 3. 网络问题
```bash
# 检查防火墙
sudo firewall-cmd --list-ports

# 检查SELinux
sudo sealert -a /var/log/audit/audit.log

# 测试网络连接
curl -I http://localhost:3001
```

## 📊 监控脚本

### 系统监控脚本
```bash
#!/bin/bash
# 系统监控脚本 - 每5分钟运行一次

ALERT_EMAIL="<EMAIL>"
LOG_FILE="/var/log/upc-monitor.log"

# 检查服务状态
if ! systemctl is-active --quiet upc-system; then
    echo "$(date): UPC服务异常" >> $LOG_FILE
    echo "UPC管理系统服务异常，请立即检查" | mail -s "UPC服务告警" $ALERT_EMAIL
    # 尝试重启服务
    systemctl restart upc-system
fi

# 检查磁盘空间
DISK_USAGE=$(df /opt/upc-system | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): 磁盘空间不足 $DISK_USAGE%" >> $LOG_FILE
    echo "UPC系统磁盘空间不足: $DISK_USAGE%" | mail -s "磁盘空间告警" $ALERT_EMAIL
fi

# 检查内存使用
MEM_USAGE=$(ps aux | grep simple-server.js | grep -v grep | awk '{print $4}')
if (( $(echo "$MEM_USAGE > 50" | bc -l) )); then
    echo "$(date): 内存使用过高 $MEM_USAGE%" >> $LOG_FILE
fi
```

### 定时任务配置
```bash
# 添加到 /etc/crontab 或使用 crontab -e

# 每5分钟检查系统状态
*/5 * * * * root /opt/upc-system/scripts/monitor.sh

# 每天凌晨2点备份数据
0 2 * * * root /opt/upc-system/scripts/backup.sh

# 每周日凌晨3点清理日志
0 3 * * 0 root /opt/upc-system/scripts/cleanup-logs.sh

# 每月1号重启服务（可选）
0 4 1 * * root systemctl restart upc-system
```

## 🔐 安全维护

### 定期安全检查
```bash
# 检查系统更新
sudo dnf check-update

# 检查安全补丁
sudo dnf updateinfo list security

# 检查异常登录
sudo lastb | head -20

# 检查文件权限
find /opt/upc-system -type f -perm /o+w
```

### 备份恢复测试
```bash
# 定期测试备份恢复
# 1. 在测试环境恢复备份
# 2. 验证数据完整性
# 3. 验证功能正常
```
