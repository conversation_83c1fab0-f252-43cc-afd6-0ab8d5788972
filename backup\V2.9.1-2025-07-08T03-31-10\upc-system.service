[Unit]
Description=UPC Management System V2.8.0
After=network.target

[Service]
Type=simple
User=upc-system
Group=upc-system
WorkingDirectory=/opt/upc-system
ExecStart=/usr/bin/node simple-server.js
Restart=always
RestartSec=10
TimeoutStartSec=60
TimeoutStopSec=30

# 环境变量
Environment=NODE_ENV=production
Environment=PORT=3001

# 日志配置
StandardOutput=journal
StandardError=journal

# 资源限制
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
