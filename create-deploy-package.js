// CentOS 8 部署包创建脚本
const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

// 部署包配置
const DEPLOY_CONFIG = {
    version: 'V3.0',
    timestamp: new Date().toISOString().replace(/[:.]/g, '-'),
    outputDir: './deploy-packages',
    packageName: 'UPC-System-V3.0-CentOS8-Deploy',
    serverInfo: {
        ip: '************',
        port: '3001',
        user: 'root',
        system: 'CentOS 8'
    },
    excludePatterns: [
        'node_modules',
        '.git',
        'backups',
        'deploy-packages',
        '*.log',
        'temp',
        'tmp',
        '.DS_Store',
        'Thumbs.db',
        'create-backup.js',
        'create-deploy-package.js'
    ]
};

// 创建部署目录
function ensureDeployDir() {
    if (!fs.existsSync(DEPLOY_CONFIG.outputDir)) {
        fs.mkdirSync(DEPLOY_CONFIG.outputDir, { recursive: true });
        console.log(`📁 创建部署目录: ${DEPLOY_CONFIG.outputDir}`);
    }
}

// 检查文件是否应该被排除
function shouldExclude(filePath) {
    const relativePath = path.relative('.', filePath);
    return DEPLOY_CONFIG.excludePatterns.some(pattern => {
        if (pattern.includes('*')) {
            const regex = new RegExp(pattern.replace(/\*/g, '.*'));
            return regex.test(relativePath);
        }
        return relativePath.includes(pattern);
    });
}

// 递归获取所有文件
function getAllFiles(dir, fileList = []) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
        const filePath = path.join(dir, file);
        
        if (shouldExclude(filePath)) {
            console.log(`⏭️ 跳过: ${filePath}`);
            return;
        }
        
        if (fs.statSync(filePath).isDirectory()) {
            getAllFiles(filePath, fileList);
        } else {
            fileList.push(filePath);
        }
    });
    
    return fileList;
}

// 创建部署信息文件
function createDeployInfo() {
    const deployInfo = {
        name: "UPC管理系统 V3.0 CentOS 8 部署包",
        version: "3.0.0",
        buildDate: new Date().toISOString(),
        platform: "CentOS 8",
        serverInfo: DEPLOY_CONFIG.serverInfo,
        description: "企业级UPC码管理解决方案 - 完整部署包",
        features: [
            "详细日志已启用",
            "错误堆栈已显示",
            "调试信息已开启",
            "邮件服务启用",
            "短信服务启用",
            "备份功能启用",
            "日志服务启用",
            "库存预警配置管理工具"
        ],
        installation: {
            quickStart: "运行 ./install-centos8.sh 进行一键安装",
            envCheck: "运行 ./check-environment.sh 检查环境",
            uninstall: "运行 ./uninstall.sh 完全卸载",
            management: "运行 node manage-stock-alert.js 管理库存预警"
        },
        requirements: {
            os: "CentOS 8 (推荐)",
            cpu: "1核心以上",
            memory: "1GB以上",
            disk: "2GB以上",
            node: ">=14.0.0",
            npm: ">=6.0.0"
        },
        defaultAccounts: {
            admin: "admin / admin123",
            manager: "manager / Manager@2025",
            operator: "operator / Operator@2025"
        },
        documentation: [
            "README.md - 系统说明和快速开始",
            "部署教程-小白版.md - 详细部署指南",
            "运维管理教程.md - 运维管理指南"
        ],
        support: {
            company: "深圳速拓电子商务有限公司",
            email: "<EMAIL>",
            website: "https://sutuo.net"
        }
    };
    
    return JSON.stringify(deployInfo, null, 2);
}

// 创建部署包
async function createDeployPackage() {
    console.log('🚀 开始创建 CentOS 8 部署包...');
    console.log(`🌐 目标服务器: ${DEPLOY_CONFIG.serverInfo.ip}:${DEPLOY_CONFIG.serverInfo.port}`);
    console.log(`👤 用户: ${DEPLOY_CONFIG.serverInfo.user}`);
    console.log(`🖥️ 系统: ${DEPLOY_CONFIG.serverInfo.system}`);
    
    try {
        // 确保部署目录存在
        ensureDeployDir();
        
        // 获取所有文件
        console.log('📁 扫描文件...');
        const allFiles = getAllFiles('.');
        console.log(`📋 找到 ${allFiles.length} 个文件`);
        
        // 创建部署包文件名
        const packageFileName = `${DEPLOY_CONFIG.packageName}-${DEPLOY_CONFIG.timestamp}.zip`;
        const packagePath = path.join(DEPLOY_CONFIG.outputDir, packageFileName);
        
        // 创建压缩流
        const output = fs.createWriteStream(packagePath);
        const archive = archiver('zip', {
            zlib: { level: 9 } // 最高压缩级别
        });
        
        // 监听事件
        output.on('close', () => {
            const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
            console.log(`✅ CentOS 8 部署包创建完成！`);
            console.log(`📦 文件: ${packageFileName}`);
            console.log(`📏 大小: ${sizeInMB} MB`);
            console.log(`📁 位置: ${path.resolve(packagePath)}`);
            console.log(`📋 包含 ${allFiles.length + 1} 个文件（含部署信息）`);
            console.log(`🌐 服务器: ${DEPLOY_CONFIG.serverInfo.ip}:${DEPLOY_CONFIG.serverInfo.port}`);
            console.log(`👤 用户: ${DEPLOY_CONFIG.serverInfo.user}`);
            console.log(`🖥️ 系统: ${DEPLOY_CONFIG.serverInfo.system}`);
            console.log('\n📋 部署说明:');
            console.log('1. 上传部署包到服务器');
            console.log('2. 解压: unzip ' + packageFileName);
            console.log('3. 环境检查: ./check-environment.sh');
            console.log('4. 一键安装: ./install-centos8.sh');
            console.log('5. 访问系统: http://************:3001');
        });
        
        archive.on('error', (err) => {
            throw err;
        });
        
        archive.on('progress', (progress) => {
            const percent = ((progress.entries.processed / progress.entries.total) * 100).toFixed(1);
            process.stdout.write(`\r📦 压缩进度: ${percent}% (${progress.entries.processed}/${progress.entries.total})`);
        });
        
        // 连接输出流
        archive.pipe(output);
        
        // 添加部署信息文件
        const deployInfo = createDeployInfo();
        archive.append(deployInfo, { name: 'deploy-info.json' });
        
        // 添加所有文件
        console.log('📦 开始压缩文件...');
        allFiles.forEach(file => {
            archive.file(file, { name: file });
        });
        
        // 完成压缩
        await archive.finalize();
        
    } catch (error) {
        console.error('❌ 部署包创建失败:', error);
        process.exit(1);
    }
}

// 运行创建
if (require.main === module) {
    createDeployPackage();
}

module.exports = { createDeployPackage };
