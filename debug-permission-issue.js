#!/usr/bin/env node

// 调试权限问题：用户只能看到自己的回收记录

const http = require('http');

console.log('🔍 调试权限问题：用户只能看到自己的回收记录');
console.log('=====================================');

// HTTP请求工具函数
function makeRequest(options) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const result = {
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data ? JSON.parse(data) : null
                    };
                    resolve(result);
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data,
                        parseError: error.message
                    });
                }
            });
        });
        
        req.on('error', reject);
        req.setTimeout(10000, () => {
            req.destroy();
            reject(new Error('请求超时'));
        });
        
        if (options.body) {
            req.write(options.body);
        }
        req.end();
    });
}

// 登录函数
async function login(credentials) {
    console.log(`🔍 登录: ${credentials.username}`);
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(credentials)
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        console.log(`✅ 登录成功: ${response.data.user.name} (${response.data.user.role})`);
        return response.data.sessionId;
    } else {
        throw new Error(`登录失败: ${response.data?.message || '未知错误'}`);
    }
}

// 获取回收历史
async function getRecycleHistory(sessionId, userType) {
    console.log(`📊 获取回收历史 (${userType})...`);
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/recycle/history',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${sessionId}`
        }
    };
    
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data.success) {
        console.log(`✅ 获取回收历史成功，记录数: ${response.data.data.length}`);
        return response.data.data;
    } else {
        throw new Error(`获取回收历史失败: ${response.data?.message || '未知错误'}`);
    }
}

// 分析权限问题
async function analyzePermissionIssue() {
    try {
        console.log('开始分析权限问题...\n');
        
        // 步骤1: 管理员登录并查看所有记录
        console.log('📋 步骤1: 管理员视角');
        const adminSession = await login({ username: 'manager', password: 'Manager@2025' });
        const adminHistory = await getRecycleHistory(adminSession, '管理员');
        
        console.log(`\n📊 管理员看到的记录分布:`);
        const adminUserGroups = {};
        adminHistory.forEach(record => {
            if (!adminUserGroups[record.user]) {
                adminUserGroups[record.user] = 0;
            }
            adminUserGroups[record.user]++;
        });
        
        Object.keys(adminUserGroups).forEach(user => {
            console.log(`   ${user}: ${adminUserGroups[user]}条记录`);
        });
        
        // 步骤2: 用户登录并查看自己的记录
        console.log('\n📋 步骤2: 用户视角');
        const userSession = await login({ username: 'operator', password: 'Operator@2025' });
        const userHistory = await getRecycleHistory(userSession, '用户');
        
        console.log(`\n📊 用户看到的记录分布:`);
        const userUserGroups = {};
        userHistory.forEach(record => {
            if (!userUserGroups[record.user]) {
                userUserGroups[record.user] = 0;
            }
            userUserGroups[record.user]++;
        });
        
        Object.keys(userUserGroups).forEach(user => {
            console.log(`   ${user}: ${userUserGroups[user]}条记录`);
        });
        
        // 步骤3: 分析问题
        console.log('\n📋 步骤3: 问题分析');
        
        const managerRecords = adminHistory.filter(r => r.user === 'manager');
        const operatorRecords = adminHistory.filter(r => r.user === 'operator');
        
        console.log(`\n🔍 记录归属分析:`);
        console.log(`   manager的记录: ${managerRecords.length}条`);
        console.log(`   operator的记录: ${operatorRecords.length}条`);
        console.log(`   用户端能看到的记录: ${userHistory.length}条`);
        
        // 步骤4: 模拟问题场景
        console.log('\n📋 步骤4: 模拟问题场景');
        
        if (managerRecords.length > 0) {
            const managerRecord = managerRecords[0];
            console.log(`\n🎯 选择manager的记录进行测试: ${managerRecord.code}`);
            
            // 检查用户端是否能看到这个记录
            const userCanSee = userHistory.find(r => r.code === managerRecord.code);
            
            if (userCanSee) {
                console.log(`✅ 用户端可以看到这个记录`);
            } else {
                console.log(`❌ 用户端看不到这个记录`);
                console.log(`   这就是问题所在！`);
                console.log(`   当管理员重新激活manager的UPC码时，`);
                console.log(`   用户端无法看到这个记录，因此无法获取upcId`);
            }
        }
        
        // 步骤5: 验证解决方案
        console.log('\n📋 步骤5: 验证解决方案');
        
        if (operatorRecords.length > 0) {
            const operatorRecord = operatorRecords[0];
            console.log(`\n🎯 测试operator自己的记录: ${operatorRecord.code}`);
            
            // 检查用户端是否能看到自己的记录
            const userCanSeeOwn = userHistory.find(r => r.code === operatorRecord.code);
            
            if (userCanSeeOwn) {
                console.log(`✅ 用户端可以看到自己的记录`);
                console.log(`   upcId: ${userCanSeeOwn.upcId} (${typeof userCanSeeOwn.upcId})`);
                
                if (userCanSeeOwn.upcId) {
                    console.log(`✅ upcId有效，可以标记已使用`);
                } else {
                    console.log(`❌ upcId无效，无法标记已使用`);
                }
            } else {
                console.log(`❌ 用户端看不到自己的记录（这不应该发生）`);
            }
        }
        
        // 总结
        console.log('\n📊 问题总结');
        console.log('=====================================');
        console.log('🔍 根本原因:');
        console.log('   1. 用户端只能看到自己的回收记录');
        console.log('   2. 当管理员重新激活其他用户的UPC码时');
        console.log('   3. 原用户无法在自己的记录列表中看到这个重新激活的记录');
        console.log('   4. 因此无法获取upcId，导致"缺少有效的ID信息"错误');
        
        console.log('\n🔧 解决方案:');
        console.log('   1. 修改权限逻辑：用户可以看到分配给自己的所有UPC码记录');
        console.log('   2. 或者：重新激活时将UPC码重新分配给原用户');
        console.log('   3. 或者：在重新激活API中创建新的回收记录给原用户');
        
    } catch (error) {
        console.log(`❌ 分析失败: ${error.message}`);
        process.exit(1);
    }
}

// 运行分析
analyzePermissionIssue();
