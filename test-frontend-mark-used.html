<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端标记已使用功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.danger {
            background: #dc3545;
        }
        .btn.danger:hover {
            background: #c82333;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 前端标记已使用功能测试</h1>
        
        <div class="test-section">
            <h3>🔐 用户登录</h3>
            <button class="btn" onclick="testLogin()">登录测试用户</button>
            <div id="loginLog" class="log"></div>
        </div>
        
        <div class="test-section">
            <h3>📊 获取回收数据</h3>
            <button class="btn" onclick="loadRecycleData()">加载回收数据</button>
            <div id="recycleLog" class="log"></div>
            <div id="recycleTable"></div>
        </div>
        
        <div class="test-section">
            <h3>🎯 标记已使用测试</h3>
            <button class="btn danger" onclick="testMarkAsUsed()">测试标记已使用</button>
            <div id="markUsedLog" class="log"></div>
        </div>
    </div>

    <script>
        let sessionId = null;
        let currentUser = null;
        let recycleData = null;

        // 日志函数
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'warning';
            element.innerHTML += `<div class="status ${className}">[${timestamp}] ${message}</div>`;
            element.scrollTop = element.scrollHeight;
        }

        // API调用函数
        async function apiCall(url, options = {}) {
            const defaultOptions = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    ...(sessionId && { 'Authorization': `Bearer ${sessionId}` })
                }
            };

            const finalOptions = { ...defaultOptions, ...options };
            
            try {
                const response = await fetch(`http://localhost:3001${url}`, finalOptions);
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.message || `HTTP ${response.status}`);
                }
                
                return data;
            } catch (error) {
                throw new Error(error.message || '网络请求失败');
            }
        }

        // 登录测试
        async function testLogin() {
            log('loginLog', '开始登录测试...', 'info');
            
            try {
                const response = await apiCall('/api/auth/login', {
                    method: 'POST',
                    body: JSON.stringify({
                        username: 'manager',
                        password: 'Manager@2025'
                    })
                });

                if (response.success) {
                    sessionId = response.sessionId;
                    currentUser = response.user;
                    log('loginLog', `✅ 登录成功: ${currentUser.name} (${currentUser.role})`, 'success');
                } else {
                    throw new Error(response.message || '登录失败');
                }
            } catch (error) {
                log('loginLog', `❌ 登录失败: ${error.message}`, 'error');
            }
        }

        // 加载回收数据
        async function loadRecycleData() {
            if (!sessionId) {
                log('recycleLog', '❌ 请先登录', 'error');
                return;
            }

            log('recycleLog', '开始加载回收数据...', 'info');
            
            try {
                const response = await apiCall('/api/recycle/history');

                if (response.success) {
                    recycleData = response.data;
                    log('recycleLog', `✅ 加载成功，共 ${recycleData.length} 条记录`, 'success');
                    
                    // 分析数据
                    const reusableRecords = recycleData.filter(r => r.status === 'reusable');
                    const recordsWithUpcId = recycleData.filter(r => r.upcId);
                    const recordsWithoutUpcId = recycleData.filter(r => !r.upcId);
                    
                    log('recycleLog', `📊 数据分析:`, 'info');
                    log('recycleLog', `  - 可重用记录: ${reusableRecords.length}`, 'info');
                    log('recycleLog', `  - 有upcId的记录: ${recordsWithUpcId.length}`, 'info');
                    log('recycleLog', `  - 没有upcId的记录: ${recordsWithoutUpcId.length}`, 'warning');
                    
                    // 显示表格
                    displayRecycleTable(reusableRecords.slice(0, 10)); // 只显示前10条可重用记录
                } else {
                    throw new Error(response.message || '加载失败');
                }
            } catch (error) {
                log('recycleLog', `❌ 加载失败: ${error.message}`, 'error');
            }
        }

        // 显示回收数据表格
        function displayRecycleTable(records) {
            const tableContainer = document.getElementById('recycleTable');
            
            if (records.length === 0) {
                tableContainer.innerHTML = '<p>没有可重用的记录</p>';
                return;
            }

            let tableHTML = `
                <table>
                    <thead>
                        <tr>
                            <th>UPC码</th>
                            <th>UPC ID</th>
                            <th>状态</th>
                            <th>回收时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            records.forEach(record => {
                tableHTML += `
                    <tr>
                        <td>${record.code}</td>
                        <td>${record.upcId || '❌ 无'}</td>
                        <td>${record.status}</td>
                        <td>${record.date} ${record.time}</td>
                        <td>
                            <button class="btn danger" onclick="markAsUsed('${record.id}', '${record.code}', ${record.upcId})">
                                标记已使用
                            </button>
                        </td>
                    </tr>
                `;
            });

            tableHTML += '</tbody></table>';
            tableContainer.innerHTML = tableHTML;
        }

        // 标记已使用
        async function markAsUsed(recordId, upcCode, upcId) {
            log('markUsedLog', `🎯 开始标记 ${upcCode} (ID: ${upcId}) 为已使用...`, 'info');
            
            if (!upcId || upcId === 'null' || upcId === 'undefined') {
                log('markUsedLog', `❌ UPC ID 无效: ${upcId}`, 'error');
                return;
            }

            try {
                const response = await apiCall('/api/upc/update', {
                    method: 'POST',
                    body: JSON.stringify({
                        upcId: parseInt(upcId),
                        updates: {
                            status: 'invalid',
                            notes: '标记为已使用'
                        }
                    })
                });

                if (response.success) {
                    log('markUsedLog', `✅ 标记成功: ${upcCode}`, 'success');
                    // 重新加载数据
                    setTimeout(() => {
                        loadRecycleData();
                    }, 1000);
                } else {
                    throw new Error(response.message || '标记失败');
                }
            } catch (error) {
                log('markUsedLog', `❌ 标记失败: ${error.message}`, 'error');
            }
        }

        // 测试标记已使用
        async function testMarkAsUsed() {
            if (!recycleData) {
                log('markUsedLog', '❌ 请先加载回收数据', 'error');
                return;
            }

            const reusableRecords = recycleData.filter(r => r.status === 'reusable' && r.upcId);
            
            if (reusableRecords.length === 0) {
                log('markUsedLog', '❌ 没有找到可标记的记录', 'error');
                return;
            }

            const testRecord = reusableRecords[0];
            log('markUsedLog', `🎯 自动测试记录: ${testRecord.code} (ID: ${testRecord.upcId})`, 'info');
            
            await markAsUsed(testRecord.id, testRecord.code, testRecord.upcId);
        }
    </script>
</body>
</html>
