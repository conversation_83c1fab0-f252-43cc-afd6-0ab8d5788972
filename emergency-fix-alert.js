// 紧急修复库存预警问题
const fs = require('fs');

console.log('🚨 紧急修复库存预警问题...');

// 方案1: 临时禁用库存预警
function disableStockAlert() {
    try {
        const settingsPath = './system_settings.json';
        const settings = JSON.parse(fs.readFileSync(settingsPath, 'utf8'));
        
        console.log('📊 当前配置:');
        console.log(`   预警启用: ${settings.stockAlert?.enableStockAlert}`);
        console.log(`   预警阈值: ${settings.stockAlert?.stockThreshold}`);
        console.log(`   预警频率: ${settings.stockAlert?.alertFrequency}`);
        
        // 禁用预警
        if (!settings.stockAlert) settings.stockAlert = {};
        settings.stockAlert.enableStockAlert = false;
        settings.lastUpdated = new Date().toISOString();
        
        fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2), 'utf8');
        console.log('✅ 已禁用库存预警');
        
        return true;
    } catch (error) {
        console.error('❌ 禁用预警失败:', error.message);
        return false;
    }
}

// 方案2: 调整预警阈值
function adjustThreshold() {
    try {
        const settingsPath = './system_settings.json';
        const settings = JSON.parse(fs.readFileSync(settingsPath, 'utf8'));
        
        // 获取当前库存
        const upcPath = './data/upc_codes.json';
        const upcCodes = JSON.parse(fs.readFileSync(upcPath, 'utf8'));
        const currentStock = upcCodes.filter(c => c.status === 'available').length;
        
        console.log(`📊 当前库存: ${currentStock} 个`);
        
        // 设置阈值为当前库存的一半
        const newThreshold = Math.max(1, Math.floor(currentStock / 2));
        
        if (!settings.stockAlert) settings.stockAlert = {};
        settings.stockAlert.stockThreshold = newThreshold;
        settings.stockAlert.alertFrequency = 'daily';
        settings.lastUpdated = new Date().toISOString();
        
        fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2), 'utf8');
        console.log(`✅ 已调整预警阈值为 ${newThreshold} 个`);
        console.log(`✅ 已调整预警频率为 daily`);
        
        return true;
    } catch (error) {
        console.error('❌ 调整阈值失败:', error.message);
        return false;
    }
}

// 方案3: 增加库存
function addStock() {
    try {
        const upcPath = './data/upc_codes.json';
        const upcCodes = JSON.parse(fs.readFileSync(upcPath, 'utf8'));
        
        const currentStock = upcCodes.filter(c => c.status === 'available').length;
        console.log(`📊 当前库存: ${currentStock} 个`);
        
        // 添加50个临时UPC码
        const addCount = 50;
        for (let i = 0; i < addCount; i++) {
            const newUPC = {
                id: `temp_${Date.now()}_${i}`,
                code: `999${String(Date.now()).slice(-8)}${String(i).padStart(3, '0')}`,
                status: 'available',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                description: '临时UPC码 - 用于解决库存预警问题',
                source: 'emergency_fix'
            };
            upcCodes.push(newUPC);
        }
        
        fs.writeFileSync(upcPath, JSON.stringify(upcCodes, null, 2), 'utf8');
        console.log(`✅ 已添加 ${addCount} 个临时UPC码`);
        console.log(`📊 新库存: ${currentStock + addCount} 个`);
        
        return true;
    } catch (error) {
        console.error('❌ 添加库存失败:', error.message);
        return false;
    }
}

// 主函数
function main() {
    console.log('🔧 选择修复方案:');
    console.log('1. 禁用库存预警（推荐）');
    console.log('2. 调整预警阈值');
    console.log('3. 增加临时库存');
    
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    
    rl.question('请选择方案 (1-3): ', (answer) => {
        let success = false;
        
        switch (answer.trim()) {
            case '1':
                success = disableStockAlert();
                break;
            case '2':
                success = adjustThreshold();
                break;
            case '3':
                success = addStock();
                break;
            default:
                console.log('❌ 无效选择');
                break;
        }
        
        if (success) {
            console.log('\n🔄 请重启服务器以应用更改:');
            console.log('1. 停止当前服务器 (Ctrl+C)');
            console.log('2. 重新启动: node simple-server.js');
            console.log('\n或者如果是系统服务:');
            console.log('systemctl restart upc-system');
        }
        
        rl.close();
    });
}

// 如果直接运行
if (require.main === module) {
    main();
} else {
    // 如果被导入，直接执行方案1
    console.log('🚨 自动执行紧急修复...');
    if (disableStockAlert()) {
        console.log('✅ 紧急修复完成，请重启服务器');
    }
}

module.exports = { disableStockAlert, adjustThreshold, addStock };
