// 创建全新的UPC管理系统V3.0目录结构
const fs = require('fs');
const path = require('path');

// 配置
const CONFIG = {
    sourceDir: '.',
    targetDir: '../UPC-System-V3.0',
    version: 'V3.0',
    serverInfo: {
        ip: '************',
        port: '3001',
        user: 'root',
        system: 'CentOS 8'
    },
    excludePatterns: [
        'node_modules',
        '.git',
        'backups',
        'deploy-packages',
        'logs',
        'temp',
        'tmp',
        '*.log',
        '.DS_Store',
        'Thumbs.db',
        'test-*.js',
        'create-*.js',
        'fix-*.js',
        'verify-*.js',
        'final-*.js',
        'backup',
        'UPC-System-V2.9-Linux-Deploy'
    ]
};

// 检查文件是否应该被排除
function shouldExclude(filePath) {
    const relativePath = path.relative(CONFIG.sourceDir, filePath);
    return CONFIG.excludePatterns.some(pattern => {
        if (pattern.includes('*')) {
            const regex = new RegExp(pattern.replace(/\*/g, '.*'));
            return regex.test(relativePath);
        }
        return relativePath.includes(pattern);
    });
}

// 创建目录
function ensureDir(dirPath) {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`📁 创建目录: ${dirPath}`);
    }
}

// 复制文件
function copyFile(src, dest) {
    try {
        const destDir = path.dirname(dest);
        ensureDir(destDir);
        fs.copyFileSync(src, dest);
        return true;
    } catch (error) {
        console.error(`❌ 复制文件失败: ${src} -> ${dest}`, error.message);
        return false;
    }
}

// 更新文件中的版本信息
function updateVersionInfo(filePath, content) {
    let updatedContent = content;
    
    // 更新版本号
    updatedContent = updatedContent.replace(/V2\.\d+(\.\d+)?/g, 'V3.0');
    updatedContent = updatedContent.replace(/v2\.\d+(\.\d+)?/g, 'v3.0');
    updatedContent = updatedContent.replace(/版本.*?V2\.\d+/g, '版本 V3.0');
    updatedContent = updatedContent.replace(/"version":\s*"2\.\d+\.\d+"/g, '"version": "3.0.0"');
    
    // 更新服务器信息
    updatedContent = updatedContent.replace(/localhost:3000/g, `${CONFIG.serverInfo.ip}:${CONFIG.serverInfo.port}`);
    updatedContent = updatedContent.replace(/127\.0\.0\.1:3000/g, `${CONFIG.serverInfo.ip}:${CONFIG.serverInfo.port}`);
    updatedContent = updatedContent.replace(/端口.*?3000/g, `端口 ${CONFIG.serverInfo.port}`);
    updatedContent = updatedContent.replace(/PORT.*?3000/g, `PORT ${CONFIG.serverInfo.port}`);
    
    // 更新IP地址
    updatedContent = updatedContent.replace(/your-server-ip/g, CONFIG.serverInfo.ip);
    updatedContent = updatedContent.replace(/您的服务器IP/g, CONFIG.serverInfo.ip);
    
    // 更新构建日期
    const buildDate = new Date().toISOString().split('T')[0];
    updatedContent = updatedContent.replace(/BUILD_DATE.*?=.*?['"`].*?['"`]/g, `BUILD_DATE = '${buildDate}'`);
    updatedContent = updatedContent.replace(/构建日期.*?2025-\d{2}-\d{2}/g, `构建日期: ${buildDate}`);
    
    return updatedContent;
}

// 递归复制目录
function copyDirectory(srcDir, destDir) {
    const items = fs.readdirSync(srcDir);
    let copiedFiles = 0;
    let totalFiles = 0;
    
    items.forEach(item => {
        const srcPath = path.join(srcDir, item);
        const destPath = path.join(destDir, item);
        
        if (shouldExclude(srcPath)) {
            console.log(`⏭️ 跳过: ${path.relative(CONFIG.sourceDir, srcPath)}`);
            return;
        }
        
        const stat = fs.statSync(srcPath);
        
        if (stat.isDirectory()) {
            const result = copyDirectory(srcPath, destPath);
            copiedFiles += result.copied;
            totalFiles += result.total;
        } else {
            totalFiles++;
            
            // 读取文件内容
            let content = fs.readFileSync(srcPath, 'utf8');
            
            // 更新版本信息
            const updatedContent = updateVersionInfo(srcPath, content);
            
            // 写入目标文件
            const destDir = path.dirname(destPath);
            ensureDir(destDir);
            fs.writeFileSync(destPath, updatedContent, 'utf8');
            
            copiedFiles++;
            console.log(`📄 复制: ${path.relative(CONFIG.sourceDir, srcPath)}`);
        }
    });
    
    return { copied: copiedFiles, total: totalFiles };
}

// 创建V3.0系统目录结构
function createV3System() {
    console.log('🚀 开始创建UPC管理系统V3.0...');
    console.log(`📂 源目录: ${path.resolve(CONFIG.sourceDir)}`);
    console.log(`📂 目标目录: ${path.resolve(CONFIG.targetDir)}`);
    
    try {
        // 创建目标目录
        ensureDir(CONFIG.targetDir);
        
        // 复制所有文件
        console.log('\n📁 开始复制文件...');
        const result = copyDirectory(CONFIG.sourceDir, CONFIG.targetDir);
        
        console.log('\n✅ V3.0系统创建完成！');
        console.log(`📊 统计信息:`);
        console.log(`   - 复制文件: ${result.copied}`);
        console.log(`   - 总文件数: ${result.total}`);
        console.log(`📁 目标位置: ${path.resolve(CONFIG.targetDir)}`);
        
        // 创建系统信息文件
        const systemInfo = {
            name: "UPC管理系统",
            version: "V3.0",
            buildDate: new Date().toISOString(),
            serverInfo: CONFIG.serverInfo,
            features: [
                "详细日志已启用",
                "错误堆栈已显示", 
                "调试信息已开启",
                "邮件服务启用",
                "短信服务启用",
                "备份功能启用",
                "日志服务启用"
            ],
            description: "企业级UPC码管理解决方案 - 完整版本",
            author: "深圳速拓电子商务有限公司"
        };
        
        fs.writeFileSync(
            path.join(CONFIG.targetDir, 'system-info.json'),
            JSON.stringify(systemInfo, null, 2),
            'utf8'
        );
        
        console.log('📋 系统信息文件已创建');
        
    } catch (error) {
        console.error('❌ 创建V3.0系统失败:', error);
        process.exit(1);
    }
}

// 运行创建
if (require.main === module) {
    createV3System();
}

module.exports = { createV3System };
