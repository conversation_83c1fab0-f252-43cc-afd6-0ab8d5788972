// 测试UPC管理池批量操作修复的脚本
const http = require('http');

let sessionToken = '';

// 先登录获取会话
function login(callback) {
    console.log('🔐 正在登录获取会话...');

    const loginData = JSON.stringify({
        username: 'admin',
        password: 'admin123'
    });

    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/auth/login',  // 修正登录API路径
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(loginData)
        }
    };

    const req = http.request(options, (res) => {
        let data = '';

        res.on('data', (chunk) => {
            data += chunk;
        });

        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                if (response.success && response.sessionId) {
                    sessionToken = response.sessionId;
                    console.log('✅ 登录成功，获取到会话令牌:', sessionToken);
                    callback();
                } else {
                    console.log('❌ 登录失败:', response.message);
                }
            } catch (error) {
                console.log('❌ 解析登录响应失败:', error.message);
            }
        });
    });

    req.on('error', (error) => {
        console.log('❌ 登录请求失败:', error.message);
    });

    req.write(loginData);
    req.end();
}

// 测试批量更新功能
function testBatchUpdate() {
    console.log('🧪 测试UPC管理池批量操作修复...');
    
    // 首先获取当前的UPC码列表
    const getOptions = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc-codes',
        method: 'GET'
    };
    
    const getReq = http.request(getOptions, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                
                if (response.success && response.data) {
                    const upcCodes = response.data;
                    console.log(`📊 获取到 ${upcCodes.length} 个UPC码`);
                    
                    // 显示前几个UPC码的ID格式
                    console.log('📋 UPC码ID格式示例:');
                    upcCodes.slice(0, 5).forEach(upc => {
                        console.log(`   - ID: ${upc.id} (类型: ${typeof upc.id}), 码: ${upc.code}, 状态: ${upc.status}`);
                    });
                    
                    // 选择前3个可用的UPC码进行批量测试
                    const availableUPCs = upcCodes.filter(upc => upc.status === 'available').slice(0, 3);
                    
                    if (availableUPCs.length < 3) {
                        console.log('⚠️ 可用的UPC码不足3个，无法进行批量测试');
                        return;
                    }
                    
                    console.log('\n🎯 选择的测试UPC码:');
                    availableUPCs.forEach(upc => {
                        console.log(`   - ID: ${upc.id}, 码: ${upc.code}`);
                    });
                    
                    // 构建批量更新请求（修正数据格式）
                    const batchUpdateData = {
                        updates: availableUPCs.map(upc => ({
                            code: upc.code,
                            status: 'reserved',
                            assigned_user: 'test_user',
                            notes: '批量操作测试'
                        }))
                    };

                    console.log('\n📤 发送批量更新请求...');
                    console.log('请求数据:', JSON.stringify(batchUpdateData, null, 2));

                    // 发送批量更新请求
                    const postData = JSON.stringify(batchUpdateData);
                    
                    const postOptions = {
                        hostname: 'localhost',
                        port: 3001,
                        path: '/api/upc/batch-update',  // 修正API路径
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Content-Length': Buffer.byteLength(postData),
                            'Authorization': `Bearer ${sessionToken}`  // 添加会话令牌
                        }
                    };
                    
                    const postReq = http.request(postOptions, (postRes) => {
                        let postData = '';
                        
                        postRes.on('data', (chunk) => {
                            postData += chunk;
                        });
                        
                        postRes.on('end', () => {
                            console.log(`\n📊 批量更新响应状态码: ${postRes.statusCode}`);
                            
                            try {
                                const postResponse = JSON.parse(postData);
                                console.log('📊 批量更新响应数据:', JSON.stringify(postResponse, null, 2));
                                
                                if (postRes.statusCode === 200 && postResponse.success) {
                                    console.log('✅ 批量更新成功！');
                                    console.log(`📋 更新了 ${postResponse.updatedCount} 个UPC码`);
                                    
                                    if (postResponse.details) {
                                        console.log('📋 更新详情:');
                                        postResponse.details.forEach(detail => {
                                            console.log(`   - ${detail.code}: ${detail.message}`);
                                        });
                                    }
                                    
                                    // 验证更新结果
                                    setTimeout(() => {
                                        verifyBatchUpdate(availableUPCs.map(upc => upc.code));
                                    }, 1000);
                                    
                                } else {
                                    console.log('❌ 批量更新失败！');
                                    console.log(`错误信息: ${postResponse.message || '未知错误'}`);
                                    
                                    if (postResponse.errors) {
                                        console.log('❌ 详细错误:');
                                        postResponse.errors.forEach(error => {
                                            console.log(`   - ${error}`);
                                        });
                                    }
                                }
                            } catch (error) {
                                console.log('❌ 解析批量更新响应失败:', error.message);
                                console.log('原始响应:', postData);
                            }
                        });
                    });
                    
                    postReq.on('error', (error) => {
                        console.log('❌ 批量更新请求失败:', error.message);
                    });
                    
                    // 发送请求
                    postReq.write(postData);
                    postReq.end();
                    
                } else {
                    console.log('❌ 获取UPC码列表失败:', response.message || '未知错误');
                }
            } catch (error) {
                console.log('❌ 解析UPC码列表响应失败:', error.message);
                console.log('原始响应:', data);
            }
        });
    });
    
    getReq.on('error', (error) => {
        console.log('❌ 获取UPC码列表请求失败:', error.message);
    });
    
    getReq.end();
}

// 验证批量更新结果
function verifyBatchUpdate(testCodes) {
    console.log('\n🔍 验证批量更新结果...');
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc-codes',
        method: 'GET'
    };
    
    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                
                if (response.success && response.data) {
                    const upcCodes = response.data;
                    
                    console.log('📋 验证测试UPC码的更新状态:');
                    testCodes.forEach(code => {
                        const upc = upcCodes.find(u => u.code === code);
                        if (upc) {
                            console.log(`   - ${code}: 状态=${upc.status}, 分配用户=${upc.assigned_user || '无'}, 备注=${upc.notes || '无'}`);
                            
                            if (upc.status === 'reserved' && upc.assigned_user === 'test_user') {
                                console.log('     ✅ 更新成功');
                            } else {
                                console.log('     ❌ 更新失败');
                            }
                        } else {
                            console.log(`   - ${code}: ❌ 找不到UPC码`);
                        }
                    });
                    
                    console.log('\n🎉 批量操作修复验证完成！');
                }
            } catch (error) {
                console.log('❌ 解析验证响应失败:', error.message);
            }
        });
    });
    
    req.on('error', (error) => {
        console.log('❌ 验证请求失败:', error.message);
    });
    
    req.end();
}

// 运行测试
console.log('🚀 开始测试UPC管理池批量操作修复...');
login(() => {
    testBatchUpdate();
});
