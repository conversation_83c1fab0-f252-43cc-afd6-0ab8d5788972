// 测试修复后的批量导入功能
const http = require('http');

// 测试批量导入UPC码功能
function testBatchImport() {
    console.log('🧪 测试修复后的批量导入功能...');
    
    // 测试数据 - 批量导入UPC码
    const testCodes = ['TEST001111111', 'TEST002222222', 'TEST003333333', 'TEST004444444', 'TEST005555555'];
    
    // 构建请求数据
    const postData = JSON.stringify({
        codes: testCodes
    });
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc/import',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData)
        }
    };
    
    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            console.log(`📊 响应状态码: ${res.statusCode}`);
            
            try {
                const response = JSON.parse(data);
                console.log(`📊 响应数据: ${JSON.stringify(response, null, 2)}`);
                
                if (res.statusCode === 200 && response.success) {
                    console.log('✅ 批量导入成功！');
                    console.log(`📋 导入数量: ${response.importedCount}`);
                    
                    // 验证导入结果
                    setTimeout(() => {
                        verifyBatchImport(testCodes);
                    }, 1000);
                    
                } else {
                    console.log('❌ 批量导入失败！');
                    console.log(`错误信息: ${response.message || '未知错误'}`);
                }
            } catch (error) {
                console.log('❌ 解析响应数据失败:', error.message);
                console.log('原始响应:', data);
            }
        });
    });
    
    req.on('error', (error) => {
        console.log('❌ 请求失败:', error.message);
    });
    
    // 发送请求
    req.write(postData);
    req.end();
}

// 验证批量导入的结果
function verifyBatchImport(expectedCodes) {
    console.log('\n🔍 验证批量导入结果...');
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/upc-codes',
        method: 'GET'
    };
    
    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                
                if (response.success && response.data) {
                    const upcCodes = response.data;
                    console.log(`📊 当前UPC码总数: ${upcCodes.length}`);
                    
                    // 查找刚导入的UPC码
                    const importedCodes = upcCodes.filter(code => 
                        expectedCodes.includes(code.code)
                    );
                    
                    console.log(`\n📋 找到导入的UPC码: ${importedCodes.length}/${expectedCodes.length}`);
                    
                    if (importedCodes.length === expectedCodes.length) {
                        console.log('✅ 所有UPC码都成功导入！');
                        
                        // 检查ID是否唯一且格式正确
                        const ids = importedCodes.map(code => code.id);
                        const uniqueIds = new Set(ids);
                        
                        if (uniqueIds.size === ids.length) {
                            console.log('✅ 所有导入的UPC码都有唯一的ID！');
                        } else {
                            console.log('❌ 发现重复的ID！');
                        }
                        
                        // 显示导入的UPC码详情
                        console.log('\n📋 导入的UPC码详情:');
                        importedCodes.forEach(code => {
                            console.log(`   - ID: ${code.id}, 码: ${code.code}, 状态: ${code.status}`);
                        });
                        
                        // 检查ID格式
                        const invalidIds = importedCodes.filter(code => 
                            !code.id || 
                            (typeof code.id !== 'string' || !code.id.startsWith('upc_'))
                        );
                        
                        if (invalidIds.length === 0) {
                            console.log('\n✅ 所有导入的UPC码都有正确的ID格式！');
                        } else {
                            console.log(`\n❌ 发现 ${invalidIds.length} 个UPC码的ID格式不正确:`);
                            invalidIds.forEach(code => {
                                console.log(`   - 码: ${code.code}, ID: ${code.id} (类型: ${typeof code.id})`);
                            });
                        }
                        
                    } else {
                        console.log('❌ 部分UPC码导入失败！');
                        const missingCodes = expectedCodes.filter(code => 
                            !upcCodes.find(upc => upc.code === code)
                        );
                        console.log('缺失的UPC码:', missingCodes);
                    }
                    
                    console.log('\n🎉 验证完成！');
                }
            } catch (error) {
                console.log('❌ 解析响应数据失败:', error.message);
            }
        });
    });
    
    req.on('error', (error) => {
        console.log('❌ 请求失败:', error.message);
    });
    
    req.end();
}

// 运行测试
testBatchImport();
