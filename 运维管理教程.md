# UPC管理系统 V3.0 运维管理教程

## 📋 目录
1. [系统监控](#系统监控)
2. [日常维护](#日常维护)
3. [性能优化](#性能优化)
4. [故障排除](#故障排除)
5. [数据备份](#数据备份)
6. [安全管理](#安全管理)
7. [系统升级](#系统升级)
8. [库存预警管理](#库存预警管理)
9. [监控告警](#监控告警)

---

## 📊 系统监控

### 1. 服务状态监控
```bash
# 查看服务运行状态
systemctl status upc-system

# 查看服务是否自启动
systemctl is-enabled upc-system

# 查看服务启动时间
systemctl show upc-system --property=ActiveEnterTimestamp
```

### 2. 系统资源监控
```bash
# 查看CPU和内存使用情况
top -p $(pgrep -f "node simple-server.js")

# 查看内存详细信息
free -h

# 查看磁盘使用情况
df -h /opt/upc-system

# 查看系统负载
uptime
```

### 3. 网络监控
```bash
# 查看端口监听状态
netstat -tuln | grep 3001

# 查看网络连接
ss -tuln | grep 3001

# 测试服务响应
curl -I http://localhost:3001
```

### 4. 日志监控
```bash
# 实时查看系统日志
journalctl -u upc-system -f

# 查看最近的错误日志
journalctl -u upc-system -p err -n 20

# 查看今天的日志
journalctl -u upc-system --since today

# 查看指定时间段的日志
journalctl -u upc-system --since "2025-07-08 00:00:00" --until "2025-07-08 23:59:59"
```

---

## 🔧 日常维护

### 1. 定期检查清单
**每日检查**:
- [ ] 服务运行状态
- [ ] 系统资源使用情况
- [ ] 错误日志
- [ ] 磁盘空间

**每周检查**:
- [ ] 系统更新
- [ ] 日志文件大小
- [ ] 数据备份完整性
- [ ] 性能指标

**每月检查**:
- [ ] 安全补丁
- [ ] 数据库优化
- [ ] 备份策略评估
- [ ] 容量规划

### 2. 日志管理
```bash
# 查看日志文件大小
du -sh /opt/upc-system/logs/

# 清理旧日志（保留最近30天）
find /opt/upc-system/logs/ -name "*.log" -mtime +30 -delete

# 压缩旧日志
find /opt/upc-system/logs/ -name "*.log" -mtime +7 -exec gzip {} \;

# 设置日志轮转
cat > /etc/logrotate.d/upc-system << EOF
/opt/upc-system/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 upc upc
    postrotate
        systemctl reload upc-system
    endscript
}
EOF
```

### 3. 临时文件清理
```bash
# 清理临时文件
rm -rf /tmp/upc-*

# 清理npm缓存
sudo -u upc npm cache clean --force

# 清理系统缓存
echo 3 > /proc/sys/vm/drop_caches
```

---

## ⚡ 性能优化

### 1. Node.js性能优化
```bash
# 设置Node.js内存限制
export NODE_OPTIONS="--max-old-space-size=2048"

# 启用生产模式
export NODE_ENV=production

# 优化垃圾回收
export NODE_OPTIONS="--optimize-for-size"
```

### 2. 系统级优化
```bash
# 优化文件描述符限制
echo "upc soft nofile 65536" >> /etc/security/limits.conf
echo "upc hard nofile 65536" >> /etc/security/limits.conf

# 优化网络参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65535" >> /etc/sysctl.conf
sysctl -p
```

### 3. 数据库优化
```bash
# 定期清理过期数据
cd /opt/upc-system
sudo -u upc node -e "
const fs = require('fs');
const path = './data/operation_logs.json';
if (fs.existsSync(path)) {
    const logs = JSON.parse(fs.readFileSync(path));
    const cutoff = new Date();
    cutoff.setDate(cutoff.getDate() - 90);
    const filtered = logs.filter(log => new Date(log.timestamp) > cutoff);
    fs.writeFileSync(path, JSON.stringify(filtered, null, 2));
    console.log('清理完成，保留最近90天的日志');
}
"
```

---

## 🚨 故障排除

### 1. 服务无法启动
```bash
# 检查配置文件
sudo -u upc node -c /opt/upc-system/simple-server.js

# 检查端口占用
lsof -i :3001

# 检查权限
ls -la /opt/upc-system/

# 手动启动调试
cd /opt/upc-system
sudo -u upc node simple-server.js
```

### 2. 内存泄漏问题
```bash
# 监控内存使用
watch -n 5 'ps aux | grep "node simple-server.js"'

# 生成内存快照
kill -USR2 $(pgrep -f "node simple-server.js")

# 重启服务释放内存
systemctl restart upc-system
```

### 3. 网络连接问题
```bash
# 检查防火墙
firewall-cmd --list-all

# 检查SELinux
getenforce
sealert -a /var/log/audit/audit.log

# 检查网络接口
ip addr show
```

### 4. 数据损坏问题
```bash
# 验证JSON文件格式
cd /opt/upc-system/data
for file in *.json; do
    echo "检查 $file"
    python -m json.tool "$file" > /dev/null && echo "✅ 格式正确" || echo "❌ 格式错误"
done

# 从备份恢复
cp /opt/upc-system/backups/latest/* /opt/upc-system/data/
chown -R upc:upc /opt/upc-system/data/
systemctl restart upc-system
```

---

## 💾 数据备份

### 1. 自动备份脚本
```bash
# 创建备份脚本
cat > /opt/upc-system/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/upc-system/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="backup_$DATE.tar.gz"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据
cd /opt/upc-system
tar -czf "$BACKUP_DIR/$BACKUP_FILE" data/ --exclude="data/backups"

# 保留最近30个备份
cd $BACKUP_DIR
ls -t backup_*.tar.gz | tail -n +31 | xargs -r rm

echo "备份完成: $BACKUP_FILE"
EOF

chmod +x /opt/upc-system/backup.sh
chown upc:upc /opt/upc-system/backup.sh
```

### 2. 定时备份
```bash
# 添加定时任务
crontab -u upc -e

# 添加以下内容（每天凌晨2点备份）
0 2 * * * /opt/upc-system/backup.sh >> /opt/upc-system/logs/backup.log 2>&1
```

### 3. 远程备份
```bash
# 同步到远程服务器
rsync -avz /opt/upc-system/backups/ user@backup-server:/backups/upc-system/

# 上传到云存储（示例：阿里云OSS）
ossutil cp /opt/upc-system/backups/ oss://your-bucket/upc-backups/ -r
```

---

## 🔒 安全管理

### 1. 访问控制
```bash
# 检查文件权限
ls -la /opt/upc-system/

# 设置正确权限
chown -R upc:upc /opt/upc-system/
chmod 755 /opt/upc-system/
chmod 644 /opt/upc-system/data/*.json
chmod 600 /opt/upc-system/config/*.conf
```

### 2. 防火墙配置
```bash
# 只允许特定IP访问
firewall-cmd --permanent --add-rich-rule="rule family='ipv4' source address='YOUR_IP' port protocol='tcp' port='3001' accept"
firewall-cmd --permanent --remove-port=3001/tcp
firewall-cmd --reload

# 启用fail2ban防护
dnf install -y fail2ban
systemctl enable fail2ban
systemctl start fail2ban
```

### 3. SSL/TLS配置
```bash
# 安装nginx作为反向代理
dnf install -y nginx

# 配置SSL证书
cat > /etc/nginx/conf.d/upc-system.conf << 'EOF'
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/your/cert.pem;
    ssl_certificate_key /path/to/your/key.pem;
    
    location / {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

systemctl enable nginx
systemctl start nginx
```

---

## 🔄 系统升级

### 1. 升级前准备
```bash
# 创建完整备份
/opt/upc-system/backup.sh

# 记录当前版本
cd /opt/upc-system
node -e "console.log(require('./package.json').version)"

# 停止服务
systemctl stop upc-system
```

### 2. 升级步骤
```bash
# 下载新版本
cd /tmp
wget [新版本下载链接]
unzip UPC-System-V3.1-CentOS8-Deploy.zip

# 备份配置文件
cp /opt/upc-system/data/* /tmp/data-backup/

# 更新系统文件
cp -r UPC-System-V3.1-CentOS8-Deploy/* /opt/upc-system/

# 恢复数据文件
cp /tmp/data-backup/* /opt/upc-system/data/

# 更新依赖
cd /opt/upc-system
sudo -u upc npm install --production

# 设置权限
chown -R upc:upc /opt/upc-system/

# 启动服务
systemctl start upc-system
```

### 3. 升级验证
```bash
# 检查服务状态
systemctl status upc-system

# 检查版本
curl http://localhost:3001/api/system/info

# 检查功能
curl http://localhost:3001/api/health
```

---

## 📊 库存预警管理

### 1. 库存预警配置工具
系统提供了专门的库存预警配置管理工具：

```bash
cd /opt/upc-system
node manage-stock-alert.js
```

### 2. 配置选项说明

#### 预警阈值设置
```bash
# 查看当前库存
node -e "
const fs = require('fs');
const data = JSON.parse(fs.readFileSync('./data/upc_codes.json'));
const available = data.filter(c => c.status === 'available').length;
console.log('当前可用库存:', available);
"

# 建议阈值设置
# - 小型企业: 10-20个
# - 中型企业: 50-100个
# - 大型企业: 200-500个
```

#### 预警频率选项
- **realtime**: 实时预警（不推荐，会频繁通知）
- **hourly**: 每小时一次（适合紧急情况）
- **daily**: 每天一次（推荐）
- **weekly**: 每周一次（适合库存充足时）

#### 通知方式配置
```bash
# 邮件配置
# 在系统设置中配置SMTP服务器信息

# 短信配置
# 配置腾讯云短信服务参数
```

### 3. 常见问题处理

#### 问题1: 频繁收到预警通知
**原因**: 当前库存低于阈值，且频率设置过高
**解决**:
```bash
# 使用管理工具调整
node manage-stock-alert.js
# 选择 "8. 快速修复（推荐设置）"
```

#### 问题2: 预警通知不及时
**原因**: 预警频率设置过低或阈值设置不合理
**解决**:
```bash
# 调整为更频繁的检查
# 降低预警阈值
# 确保邮件短信服务正常
```

#### 问题3: 预警通知发送失败
**检查步骤**:
```bash
# 检查邮件配置
journalctl -u upc-system | grep "邮件"

# 检查短信配置
journalctl -u upc-system | grep "短信"

# 检查网络连接
curl -I smtp.163.com:25
```

### 4. 预警配置最佳实践

#### 推荐配置
```json
{
  "enableStockAlert": true,
  "stockThreshold": 20,
  "alertFrequency": "daily",
  "alertByEmail": true,
  "alertBySMS": true,
  "emailRecipients": "<EMAIL>",
  "smsRecipients": "13800138000"
}
```

#### 配置策略
1. **阈值设置**: 根据日常消耗量设置，建议为7-14天的用量
2. **频率控制**: 正常情况下使用daily，紧急时可临时调整为hourly
3. **多重通知**: 同时启用邮件和短信，确保及时收到通知
4. **定期检查**: 每月检查一次配置是否合理

### 5. 手动管理命令

#### 查看当前配置
```bash
cd /opt/upc-system
node -e "
const fs = require('fs');
const settings = JSON.parse(fs.readFileSync('./system_settings.json'));
console.log('库存预警配置:', JSON.stringify(settings.stockAlert, null, 2));
"
```

#### 临时禁用预警
```bash
# 编辑配置文件
nano /opt/upc-system/system_settings.json
# 将 enableStockAlert 设置为 false
# 重启服务
systemctl restart upc-system
```

#### 重置预警时间
```bash
cd /opt/upc-system
node -e "
const fs = require('fs');
const settings = JSON.parse(fs.readFileSync('./system_settings.json'));
settings.stockAlert.lastAlertTime = null;
fs.writeFileSync('./system_settings.json', JSON.stringify(settings, null, 2));
console.log('预警时间已重置');
"
systemctl restart upc-system
```

---

## 📈 监控告警

### 1. 系统监控脚本
```bash
# 创建监控脚本
cat > /opt/upc-system/monitor.sh << 'EOF'
#!/bin/bash

# 检查服务状态
if ! systemctl is-active --quiet upc-system; then
    echo "ALERT: UPC系统服务已停止" | mail -s "UPC系统告警" <EMAIL>
fi

# 检查内存使用
MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
if [ $MEMORY_USAGE -gt 80 ]; then
    echo "ALERT: 内存使用率过高: ${MEMORY_USAGE}%" | mail -s "UPC系统告警" <EMAIL>
fi

# 检查磁盘空间
DISK_USAGE=$(df /opt/upc-system | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "ALERT: 磁盘使用率过高: ${DISK_USAGE}%" | mail -s "UPC系统告警" <EMAIL>
fi

# 检查错误日志
ERROR_COUNT=$(journalctl -u upc-system --since "1 hour ago" -p err | wc -l)
if [ $ERROR_COUNT -gt 10 ]; then
    echo "ALERT: 最近1小时内错误日志过多: ${ERROR_COUNT}条" | mail -s "UPC系统告警" <EMAIL>
fi
EOF

chmod +x /opt/upc-system/monitor.sh
```

### 2. 定时监控
```bash
# 添加监控任务（每5分钟检查一次）
crontab -e
*/5 * * * * /opt/upc-system/monitor.sh
```

### 3. 集成监控系统
```bash
# 安装Prometheus Node Exporter
wget https://github.com/prometheus/node_exporter/releases/download/v1.3.1/node_exporter-1.3.1.linux-amd64.tar.gz
tar xvfz node_exporter-1.3.1.linux-amd64.tar.gz
sudo cp node_exporter-1.3.1.linux-amd64/node_exporter /usr/local/bin/

# 创建systemd服务
cat > /etc/systemd/system/node_exporter.service << 'EOF'
[Unit]
Description=Node Exporter
After=network.target

[Service]
User=nobody
Group=nobody
Type=simple
ExecStart=/usr/local/bin/node_exporter

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable node_exporter
systemctl start node_exporter
```

---

## 📞 技术支持

### 故障报告模板
当遇到问题时，请提供以下信息：

1. **系统信息**
   ```bash
   uname -a
   cat /etc/os-release
   ```

2. **服务状态**
   ```bash
   systemctl status upc-system
   journalctl -u upc-system -n 50
   ```

3. **系统资源**
   ```bash
   free -h
   df -h
   top -n 1
   ```

4. **网络状态**
   ```bash
   netstat -tuln | grep 3001
   curl -I http://localhost:3001
   ```

**联系方式**:
- 📧 邮箱: <EMAIL>
- 🌐 网站: https://sutuo.net
- 📱 电话: [技术支持电话]

---

**© 2025 深圳速拓电子商务有限公司 版权所有**
